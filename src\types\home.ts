export interface NameGenerationParams {
    primaryCulture: string;
    secondaryCulture: string;
    nameLength: number;
    gender: string;
    spiritualPreference: string;
    responseLanguage?: string;
    languages?: string[];
}

export interface Pronunciation {
    language: string;
    text: string;
}

export interface GeneratedName {
    original: string;
    translated: string;
    meaning: string;
    culturalContext: string[];
    pronunciations: Pronunciation[];
    safetyReport?: {
        phoneticConflicts: string[];
        culturalTaboos: string[];
    };
}

export interface NameVariant {
    language: string
    name: string
    pronunciation: string
    meaning: string
    culturalContext: string
    safetyScore: number
  }
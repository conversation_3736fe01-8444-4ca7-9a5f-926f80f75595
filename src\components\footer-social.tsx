import Link from "next/link"
import { type Footer as FooterSocialType } from "@/types/locales/footer"
import { SOCIAL_ICONS } from "@/components/icons/social-icons"
import { type Locale, getPathname } from "@/i18n-config";

interface FooterSocialProps {
    lang: Locale;
    i18n: FooterSocialType;
}

export function FooterSocial({ lang, i18n }: FooterSocialProps) {
    return (
        <footer className="border-t bg-gradient-to-b from-background via-background to-primary/5">
            <div className="container grid gap-12 px-4 py-16 md:grid-cols-2 lg:grid-cols-4">
                <div className="space-y-6">
                    <Link scroll={true} href={getPathname(lang,"/")} className="flex items-center gap-3" title={i18n.logoTitle}>
                        <img 
                            src="/favicon.ico" 
                            alt="Hypertension Logo" 
                            width="32" 
                            height="32" 
                            className="object-contain"
                        />
                        <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-gold">
                            {i18n.logo}
                        </span>
                    </Link>
                    <p className="text-sm text-muted-foreground leading-relaxed max-w-xs">
                        {i18n.description}
                    </p>
                </div>

                {i18n.sections.map((section) => (
                    <div key={section.title}>
                        <h3 className="mb-6 text-sm font-semibold tracking-wide uppercase">{section.title}</h3>
                        <ul className="space-y-4 text-sm">
                            {section.links.map((link) => (
                                <li key={link.label}>
                                    <Link
                                        href={link.href.startsWith('#') || link.href.startsWith('http') ? link.href : getPathname(lang, link.href)}
                                        className="text-muted-foreground hover:text-primary-gold transition-colors duration-200"
                                    >
                                        {link.label}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>
                ))}
            </div>

            <div className="border-t border-primary-gold/10">
                <div className="container flex flex-col gap-6 px-4 py-8 sm:flex-row sm:items-center sm:justify-between">
                    <p className="text-xs text-muted-foreground">
                        {i18n.copyright.replace('{year}', new Date().getFullYear().toString())}
                    </p>
                    <div className="flex gap-6">
                        {i18n.socials.map((social) => {
                            const Icon = SOCIAL_ICONS[social.name]
                            return (
                                <Link
                                    target="_blank"
                                    key={social.name}
                                    href={social.href}
                                    className="text-muted-foreground hover:text-primary-gold transition-colors duration-200"
                                >
                                    <span className="sr-only">{social.name}</span>
                                    <Icon className="h-5 w-5" />
                                </Link>
                            )
                        })}
                    </div>
                </div>
            </div>
        </footer>
    )
}
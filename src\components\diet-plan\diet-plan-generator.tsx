'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Loader2, ChefHat, Utensils, Calendar, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { DietPlan, DietPlanResponse } from '@/types/diet-plan'
import { DietPlanViewer } from './diet-plan-viewer'

interface DietPlanGeneratorProps {
  userId: string
  className?: string
  onPlanGenerated?: (dietPlan: DietPlan) => void // 新增回调函数
}

export function DietPlanGenerator({ userId, className = '', onPlanGenerated }: DietPlanGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [includeHerbalPlan, setIncludeHerbalPlan] = useState(false)
  const [dietPlan, setDietPlan] = useState<DietPlan | null>(null)
  const [error, setError] = useState<string>('')

  const handleGeneratePlan = async () => {
    if (!userId) {
      setError('用户ID不能为空')
      return
    }

    setIsGenerating(true)
    setError('')

    try {
      const response = await fetch('/api/generate-diet-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          includeHerbalPlan
        })
      })

      const result: DietPlanResponse = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '生成饮食计划失败')
      }

      if (result.success && result.dietPlan) {
        setDietPlan(result.dietPlan)
        setError('')
        // 通知父组件计划已生成
        if (onPlanGenerated) {
          onPlanGenerated(result.dietPlan)
        }
      } else {
        throw new Error(result.error || '生成饮食计划失败')
      }
    } catch (error) {
      console.error('生成饮食计划错误:', error)
      setError(error instanceof Error ? error.message : '生成饮食计划失败，请稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleRegenerate = () => {
    setDietPlan(null)
    handleGeneratePlan()
  }

  if (dietPlan) {
    return (
      <DietPlanViewer 
        dietPlan={dietPlan} 
        onRegenerate={handleRegenerate}
        isRegenerating={isGenerating}
        className={className}
      />
    )
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-blue-50">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <ChefHat className="w-8 h-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-800">
            AI 智能饮食计划生成
          </CardTitle>
          <CardDescription className="text-lg text-gray-600 max-w-2xl mx-auto">
            基于您的健康评估报告，为您量身定制专业的一周饮食计划
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 计划类型选择 */}
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Label htmlFor="herbal-plan" className="text-lg font-semibold text-gray-800">
                  中医膳食计划
                </Label>
                <p className="text-sm text-gray-600">
                  融入中医食疗理念，根据体质特点推荐药膳调理方案
                </p>
              </div>
              <Switch
                id="herbal-plan"
                checked={includeHerbalPlan}
                onCheckedChange={setIncludeHerbalPlan}
                className="scale-125"
              />
            </div>
          </div>

          {/* 计划特色说明 */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4 shadow-sm border">
              <div className="flex items-center space-x-3 mb-3">
                <Utensils className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-800">普通营养计划</h3>
              </div>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 科学营养配比</li>
                <li>• 现代饮食习惯</li>
                <li>• 健康减脂导向</li>
                <li>• 详细营养成分</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm border">
              <div className="flex items-center space-x-3 mb-3">
                <Calendar className="w-5 h-5 text-green-600" />
                <h3 className="font-semibold text-gray-800">中医膳食计划</h3>
              </div>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 药食同源理念</li>
                <li>• 体质调理配方</li>
                <li>• 传统食疗智慧</li>
                <li>• 四季养生搭配</li>
              </ul>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 生成按钮 */}
          <div className="flex justify-center pt-4">
            <Button
              onClick={handleGeneratePlan}
              disabled={isGenerating}
              size="lg"
              className="px-8 py-3 text-lg font-semibold bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 shadow-lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  AI 正在生成饮食计划...
                </>
              ) : (
                <>
                  <ChefHat className="mr-2 h-5 w-5" />
                  生成我的专属饮食计划
                </>
              )}
            </Button>
          </div>

          {/* 说明文字 */}
          <div className="text-center text-sm text-gray-500 bg-white rounded-lg p-4 shadow-sm border">
            <p>💡 生成过程需要耐心等待，我们正在根据您的健康数据精心制定个性化饮食方案</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 
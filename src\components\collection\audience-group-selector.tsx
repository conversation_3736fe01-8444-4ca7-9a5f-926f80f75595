'use client'

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Check, Users, Dumbbell, Briefcase } from 'lucide-react';
import { toast } from 'sonner';
import { LoginDialog } from '@/components/auth/LoginDialog';
import WeightLossForm from './weight-loss-form';
import FitnessHobbyForm from './fitness-hobby-form';
import UrbanWorkForm from './urban-work-form';

interface AudienceGroupSelectorProps {
  lang: string;
}

// 简化的i18n数据
const getSimpleI18n = (lang: string) => ({
  auth: {
    login: {
      title: lang === 'zh' ? '登录账户' : 'Sign In',
      googleButton: lang === 'zh' ? '使用Google登录' : 'Sign in with Google',
      orDivider: lang === 'zh' ? '或使用邮箱继续' : 'or continue with email',
      emailLabel: lang === 'zh' ? '邮箱' : 'Email',
      emailPlaceholder: lang === 'zh' ? '请输入邮箱地址' : 'Enter your email',
      passwordLabel: lang === 'zh' ? '密码' : 'Password',
      passwordPlaceholder: lang === 'zh' ? '请输入密码（至少6个字符）' : 'Enter your password (min. 6 characters)',
      loginButton: lang === 'zh' ? '登录' : 'Sign In',
      registerLink: lang === 'zh' ? '还没有账户？' : "Don't have an account?",
      registerButton: lang === 'zh' ? '立即注册' : 'Sign Up',
      forgotPassword: lang === 'zh' ? '忘记密码？' : 'Forgot Password?'
    },
    register: {
      title: lang === 'zh' ? '创建账户' : 'Create Account',
      googleButton: lang === 'zh' ? '使用Google注册' : 'Sign up with Google',
      orDivider: lang === 'zh' ? '或使用邮箱注册' : 'or sign up with email',
      emailLabel: lang === 'zh' ? '邮箱' : 'Email',
      emailPlaceholder: lang === 'zh' ? '请输入邮箱地址' : 'Enter your email',
      passwordLabel: lang === 'zh' ? '密码' : 'Password',
      passwordPlaceholder: lang === 'zh' ? '请输入密码（至少6个字符）' : 'Enter your password (min. 6 characters)',
      firstNameLabel: lang === 'zh' ? '名字' : 'First Name',
      firstNamePlaceholder: lang === 'zh' ? '请输入名字' : 'Enter your first name',
      lastNameLabel: lang === 'zh' ? '姓氏' : 'Last Name',
      lastNamePlaceholder: lang === 'zh' ? '请输入姓氏' : 'Enter your last name',
      registerButton: lang === 'zh' ? '注册' : 'Sign Up',
      loginLink: lang === 'zh' ? '已有账户？' : 'Already have an account?',
      loginButton: lang === 'zh' ? '立即登录' : 'Sign In'
    },
    errors: {
      emailRequired: lang === 'zh' ? '请输入邮箱地址' : 'Email is required',
      emailInvalid: lang === 'zh' ? '请输入有效的邮箱地址' : 'Please enter a valid email',
      passwordRequired: lang === 'zh' ? '请输入密码' : 'Password is required',
      passwordLength: lang === 'zh' ? '密码至少需要6个字符' : 'Password must be at least 6 characters',
      firstNameRequired: lang === 'zh' ? '请输入名字' : 'First name is required',
      lastNameRequired: lang === 'zh' ? '请输入姓氏' : 'Last name is required',
      loginFailed: lang === 'zh' ? '登录失败，请检查邮箱和密码' : 'Login failed, please check your email and password',
      registerFailed: lang === 'zh' ? '注册失败，请稍后重试' : 'Registration failed, please try again',
      googleLoginFailed: lang === 'zh' ? 'Google登录失败，请稍后重试' : 'Google login failed, please try again',
      userNotFound: lang === 'zh' ? '用户不存在' : 'User does not exist',
      invalidCredentials: lang === 'zh' ? '邮箱或密码错误' : 'Invalid email or password',
      accountDisabled: lang === 'zh' ? '账户已被禁用，请联系管理员' : 'Account is disabled, please contact administrator',
      networkError: lang === 'zh' ? '网络错误，请稍后重试' : 'Network error, please try again later'
    },
    success: {
      welcomeBack: lang === 'zh' ? '欢迎回来！' : 'Welcome back!',
      welcomeNew: lang === 'zh' ? '欢迎！您已获得10个积分' : "Welcome! You've received 10 credits"
    }
  }
});

export const audienceGroups = [
  {
    id: 'weight-loss',
    title: '希望健康瘦身人群',
    description: '适合想要通过健康饮食减轻体重的人群',
    icon: Users,
    color: 'from-blue-500 to-blue-600',
    infoCollectionPath: '/diet-plan/info-collection',
  },
  {
    id: 'fitness',
    title: '健身爱好者',
    description: '适合需要科学饮食配合锻炼的健身爱好者',
    icon: Dumbbell,
    color: 'from-emerald-500 to-emerald-600',
    infoCollectionPath: '/diet-plan/info-collection',
  },
  {
    id: 'white-collar',
    title: '都市白领',
    description: '适合久坐办公、缺乏运动的职场人士',
    icon: Briefcase,
    color: 'from-purple-500 to-purple-600',
    infoCollectionPath: '/diet-plan/info-collection',
  },
];

export default function AudienceGroupSelector({ lang }: AudienceGroupSelectorProps) {
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [showLoginDialog, setShowLoginDialog] = useState<boolean>(false);
  const { data: session, status } = useSession();
  const router = useRouter();
  
  // 获取i18n数据
  const i18n = getSimpleI18n(lang);

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroup(groupId);
    // 如果之前已经显示了表单，当用户重新选择群体时隐藏表单
    if (showForm) {
      setShowForm(false);
    }
  };

  const handleContinue = async () => {
    if (!selectedGroup) {
      toast.error('请选择一个受众群体');
      return;
    }

    // 检查用户是否已登录
    if (!session?.user) {
      // 用户未登录，显示登录对话框
      setShowLoginDialog(true);
      return;
    }

    try {
      // 用户已登录，保存选择并继续
      const selectedGroupData = audienceGroups.find(group => group.id === selectedGroup);
      
      if (selectedGroupData) {
        // 存储用户选择到localStorage
        localStorage.setItem('selected_audience_group', selectedGroup);
        
        // 显示表单组件，而不是导航到新页面
        setShowForm(true);
      }
    } catch (error) {
      console.error('保存用户选择时出错:', error);
      toast.error('保存失败，请稍后重试');
    }
  };

  // 处理从表单返回的回调
  const handleBackFromForm = () => {
    setShowForm(false);
  };

  // 监听登录状态变化
  useEffect(() => {
    // 当用户登录成功且有选择的群体时，自动继续到表单
    if (session?.user && selectedGroup && showLoginDialog) {
      setShowLoginDialog(false);
      localStorage.setItem('selected_audience_group', selectedGroup);
      setShowForm(true);
    }
  }, [session?.user, selectedGroup, showLoginDialog]);

  // 如果showForm为true，则根据选择的群体显示不同的表单组件，并传递onBack回调
  if (showForm) {
    switch (selectedGroup) {
      case 'weight-loss':
        return <WeightLossForm lang={lang} onBack={handleBackFromForm} />;
      case 'fitness':
        return <FitnessHobbyForm lang={lang} onBack={handleBackFromForm} />;
      case 'white-collar':
        return <UrbanWorkForm lang={lang} onBack={handleBackFromForm} />;
      default:
        return <WeightLossForm lang={lang} onBack={handleBackFromForm} />;
    }
  }

  return (
    <div className="w-full max-w-3xl mx-auto p-4 py-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">请选择您所属的群体</h2>
        <p className="text-slate-600">选择最适合您的群体，我们将提供最匹配的饮食计划</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {audienceGroups.map((group) => (
          <Card 
            key={group.id}
            className={`relative p-4 cursor-pointer transition-all hover:shadow-lg ${
              selectedGroup === group.id 
                ? 'ring-2 ring-blue-500 shadow-lg' 
                : 'hover:border-blue-200'
            }`}
            onClick={() => handleGroupSelect(group.id)}
          >
            {selectedGroup === group.id && (
              <div className="absolute top-2 right-2">
                <Check className="h-5 w-5 text-blue-500" />
              </div>
            )}
            <div className="flex flex-col items-center text-center">
              <div className={`w-16 h-16 rounded-full mb-4 flex items-center justify-center bg-gradient-to-r ${group.color} text-white`}>
                <group.icon size={24} />
              </div>
              <h3 className="font-medium text-lg mb-2">{group.title}</h3>
              <p className="text-sm text-slate-600">{group.description}</p>
            </div>
          </Card>
        ))}
      </div>

      <div className="flex justify-center">
        <Button 
          size="lg" 
          className="px-8"
          onClick={handleContinue}
          disabled={!selectedGroup}
        >
          继续
        </Button>
      </div>

      {/* 登录对话框 */}
      <LoginDialog 
        isOpen={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
        lang={lang}
        i18n={i18n}
      />
    </div>
  );
} 
'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group'
import { cn } from '@/lib/utils'
import { DatePicker } from '@/components/date-picker'
import { WeightGauge } from '@/components/weight-gauge'

// 表单验证 Schema
const weightLossFormSchema = z.object({
  // 身高
  height: z.string().min(1, { message: '请输入您的身高' }),
  heightUnit: z.enum(['cm', 'inch'], {
    required_error: '请选择身高单位',
  }),
  
  // 体重
  weight: z.string().min(1, { message: '请输入您的体重' }),
  weightUnit: z.enum(['kg', 'lb'], {
    required_error: '请选择体重单位',
  }),
  
  // 体脂率
  bodyFatPercentage: z.string().optional(),
  knowsBodyFat: z.enum(['yes', 'no'], {
    required_error: '请选择是否知道体脂率',
  }),
  
  // 肌肉量（健身爱好者特有）
  muscleMass: z.string().optional(),
  muscleMassUnit: z.enum(['kg', 'lb'], {
    required_error: '请选择肌肉量单位',
  }).optional(),
  knowsMuscleMass: z.enum(['yes', 'no'], {
    required_error: '请选择是否知道肌肉量',
  }).optional(),
  
  // 围度测量（健身爱好者特有）
  chestCircumference: z.string().optional(), // 胸围
  waistCircumference: z.string().optional(), // 腰围
  hipCircumference: z.string().optional(),   // 臀围
  armCircumference: z.string().optional(),   // 臂围
  thighCircumference: z.string().optional(), // 大腿围
  calfCircumference: z.string().optional(),  // 小腿围
  circumferenceUnit: z.enum(['cm', 'inch'], {
    required_error: '请选择围度单位',
  }).optional(),
  
  // 静息心率（健身爱好者特有）
  restingHeartRate: z.string().optional(),
  
  // 出生日期（替代年龄）
  birthDate: z.date({
    required_error: '请选择您的出生日期',
  }),
  
  // 性别
  gender: z.enum(['male', 'female', 'other'], {
    required_error: '请选择您的性别',
  }),
  
  // 过往体重变化情况 - 已移除
  weightChangeHistory: z.enum(['stable', 'increasing', 'fluctuating']).optional(),
  
  // 健康目标 - 期望减重数值（已移除）
  weightLossGoal: z.string().optional(),
  weightLossGoalUnit: z.enum(['kg', 'lb', 'bodyFat']).optional(),
  
  // 健康目标 - 期望达成时间（已移除）
  goalTimeframe: z.string().optional(),
  
  // 健身目标类型
  fitnessGoalType: z.enum(['muscleGain', 'bodyShaping', 'performanceImprovement'], {
    required_error: '请选择您的健身目标类型',
  }).optional(),
  
  // 增肌目标
  muscleGainGoal: z.string().optional(),
  muscleGainUnit: z.enum(['kg', 'lb']).optional(),
  muscleGainTimeframe: z.string().optional(),
  
  // 塑形目标
  bodyShapingAreas: z.array(z.string()).optional(),
  
  // 提升运动表现
  sportType: z.enum(['running', 'weightlifting', 'basketball', 'football', 'swimming', 'cycling', 'tennis', 'badminton', 'yoga', 'boxing', 'other']).optional(),
  performanceAspects: z.array(z.string()).optional(),
  
  // 运动习惯
  weeklyWorkoutFrequency: z.enum(['1-2', '3-4', '5-6', '7+']).optional(),
  workoutDuration: z.enum(['<30min', '30-60min', '60-90min', '>90min']).optional(),
  workoutIntensity: z.enum(['low', 'medium', 'high']).optional(),
  
  // 节食经历
  hasDietHistory: z.enum(['yes', 'no']).optional(),
  
  // 节食后反应（多选）
  dietReactions: z.array(z.string()).optional(),
  
  // 饮食偏好
  favoriteFoods: z.array(z.string()).optional(),
  dislikedFoods: z.array(z.string()).optional(),
  religiousDietaryRestrictions: z.enum(['none', 'halal', 'kosher', 'hindu', 'buddhist', 'other']).optional(),
  personalDietaryRestrictions: z.array(z.string()).optional(),
  dietaryRestrictions: z.array(z.string()).optional(),
  lowCalorieAcceptance: z.enum(['high', 'medium', 'low']).optional(),
  unhealthyFoodPreference: z.array(z.string()).optional(),
  eatingOutFrequency: z.enum(['0-1', '2-3', '4-5', '6+']).optional(),
  medicinalDietPlan: z.enum(['daily', 'threedays', 'weekly', 'monthly', 'none'], {
    required_error: '请选择您是否愿意加入药膳饮食计划',
  }),
  
  // 健身饮食偏好
  highProteinFoodPreference: z.enum(['high', 'medium', 'low'], {
    required_error: '请选择您对高蛋白食材的接受度',
  }).optional(),
  supplementUsage: z.enum(['yes', 'no'], {
    required_error: '请选择您是否有使用健身补剂的习惯',
  }).optional(),
  supplementTypes: z.array(z.string()).optional(),
  workoutMealPreference: z.enum(['preworkoutCarbs', 'postworkoutProtein', 'bothBalanced', 'noPreference']).optional(),
  
  // 健身饮食习惯
  preworkoutMeal: z.enum(['yes', 'no', 'sometimes'], {
    required_error: '请选择您是否有运动前加餐的习惯',
  }).optional(),
  postworkoutMeal: z.enum(['yes', 'no', 'sometimes'], {
    required_error: '请选择您是否有运动后加餐的习惯',
  }).optional(),
  workoutDayDietDifference: z.enum(['significant', 'slight', 'none'], {
    required_error: '请选择您运动日与非运动日的饮食差异需求',
  }).optional(),
  
  // 饮食习惯
  regularMeals: z.enum(['regular', 'irregular']).optional(),
  snackingHabit: z.enum(['yes', 'no']).optional(),
  snackTypes: z.array(z.string()).optional(),
  eatingSpeed: z.enum(['fast', 'medium', 'slow']).optional(),
  foodAllergies: z.array(z.string()).optional(),
  chronicDiseases: z.array(z.string()).optional(),
  medications: z.array(z.string()).optional(),
  sleepQuality: z.enum(['good', 'fair', 'poor', 'irregular']).optional(),
  bodyHeaviness: z.enum(['yes', 'no', 'sometimes']).optional(),
  temperatureSensitivity: z.enum(['heat', 'cold', 'normal']).optional(),
  oilySkin: z.enum(['yes', 'no', 'sometimes']).optional(),
  sweating: z.enum(['yes', 'no', 'onlyExercise']).optional(),
  backPain: z.enum(['yes', 'no', 'sometimes']).optional(),
  appetite: z.enum(['yes', 'no', 'normal']).optional(),
  hairCondition: z.enum(['yes', 'no', 'mild']).optional(),
  menstrualRegularity: z.enum(['yes', 'no', 'sometimes']).optional(),
})

// 定义表单数据类型
type WeightLossFormValues = z.infer<typeof weightLossFormSchema>

// 组件属性定义
interface WeightLossFormProps {
  lang: string;
  onBack?: () => void; // 返回上一级组件的回调函数
}

// 定义选项类型
interface FormOption {
  value: string;
  label: string;
  icon?: string;
}

// 定义表单字段类型
interface FormField {
  id: string;
  label: string;
  type: string;
  placeholder?: string;
  options?: FormOption[];
  description?: string;
}

// 定义表单部分类型
interface FormSection {
  title: string;
  fields: FormField[];
}

// 定义步骤配置类型
interface FormStep {
  id: string;
  title: string;
  type?: string;
  options?: FormOption[];
  units?: { value: string; label: string }[];
  placeholder?: string;
  description?: string;
  sections?: FormSection[];
  fields?: FormField[];
}

// 步骤配置
const formSteps: FormStep[] = [
  {
    id: 'gender',
    title: '您的性别是？',
    options: [
      { value: 'male', label: '男性', icon: '👨' },
      { value: 'female', label: '女性', icon: '👩' },
      { value: 'other', label: '其他/不便透露', icon: '🧑' },
    ]
  },
  {
    id: 'birthDate',
    title: '您的出生日期是？',
    type: 'birthDate',
    description: '我们将根据您的出生日期计算年龄'
  },
  {
    id: 'height',
    title: '您的身高是？',
    type: 'input',
    placeholder: '请输入身高',
    units: [
      { value: 'cm', label: '厘米 (cm)' },
      { value: 'inch', label: '英寸 (inch)' },
    ]
  },
  {
    id: 'weight',
    title: '您的体重是？',
    type: 'input',
    placeholder: '请输入体重',
    units: [
      { value: 'kg', label: '千克 (kg)' },
      { value: 'lb', label: '磅 (lb)' },
    ]
  },
  {
    id: 'knowsBodyFat',
    title: '您是否知道自己的体脂率？',
    options: [
      { value: 'yes', label: '是的，我知道', icon: '✅' },
      { value: 'no', label: '不，我不清楚', icon: '❓' },
    ]
  },
  {
    id: 'knowsMuscleMass',
    title: '您是否知道自己的肌肉量？',
    options: [
      { value: 'yes', label: '是的，我知道', icon: '💪' },
      { value: 'no', label: '不，我不清楚', icon: '❓' },
    ]
  },
  {
    id: 'restingHeartRate',
    title: '您的静息心率是？',
    type: 'input',
    placeholder: '请输入静息心率（次/分钟）',
    description: '静息心率是指在完全放松状态下的心跳次数，一般在60-100次/分钟之间'
  },
  {
    id: 'bodyCircumference',
    title: '您的主要健身部位围度是？',
    type: 'circumference',
    description: '请填写您知道的围度数据（可选填）',
    units: [
      { value: 'cm', label: '厘米 (cm)' },
      { value: 'inch', label: '英寸 (inch)' },
    ]
  },


  {
    id: 'fitnessGoalType',
    title: '您的健身目标是什么？',
    options: [
      { value: 'muscleGain', label: '增肌', icon: '💪' },
      { value: 'bodyShaping', label: '塑形', icon: '🏋️' },
      { value: 'performanceImprovement', label: '提升运动表现', icon: '🏃' },
    ],
    description: '选择您的主要健身目标，我们会根据您的目标提供个性化建议'
  },
  {
    id: 'muscleGainGoal',
    title: '您期望增加多少肌肉量？',
    type: 'input',
    placeholder: '请输入目标数值（如2-5）',
    units: [
      { value: 'kg', label: '千克 (kg)' },
      { value: 'lb', label: '磅 (lb)' },
    ],
    description: '设定一个合理的增肌目标有助于我们为您制定更科学的训练计划'
  },
  {
    id: 'muscleGainTimeframe',
    title: '您期望在多长时间内达成增肌目标？',
    type: 'timeframe',
    placeholder: '请选择时间范围（1-12个月）',
    description: '合理的增肌周期对于健康、可持续的肌肉增长至关重要'
  },
  {
    id: 'bodyShapingAreas',
    title: '您希望重点塑形的身体部位是？',
    type: 'multiSelect',
    options: [
      { value: 'abs', label: '腹部线条', icon: '🦵' },
      { value: 'legs', label: '腿部肌肉线条', icon: '🦵' },
      { value: 'arms', label: '手臂线条', icon: '💪' },
      { value: 'back', label: '背部线条', icon: '👤' },
      { value: 'chest', label: '胸部线条', icon: '👕' },
      { value: 'glutes', label: '臀部线条', icon: '🍑' },
      { value: 'overall', label: '全身线条', icon: '👤' },
    ],
    description: '了解您的塑形重点有助于我们为您定制更有针对性的训练计划'
  },
  {
    id: 'sportType',
    title: '您希望提升表现的运动项目是？',
    options: [
      { value: 'running', label: '跑步/马拉松', icon: '🏃' },
      { value: 'weightlifting', label: '举重/力量举', icon: '🏋️' },
      { value: 'basketball', label: '篮球', icon: '🏀' },
      { value: 'football', label: '足球', icon: '⚽' },
      { value: 'swimming', label: '游泳', icon: '🏊' },
      { value: 'cycling', label: '骑行', icon: '🚴' },
      { value: 'tennis', label: '网球', icon: '🎾' },
      { value: 'badminton', label: '羽毛球', icon: '🏸' },
      { value: 'yoga', label: '瑜伽', icon: '🧘' },
      { value: 'boxing', label: '拳击', icon: '🥊' },
      { value: 'other', label: '其他运动', icon: '🎯' },
    ],
    description: '针对特定运动项目的训练计划可以更有效地提升您的运动表现'
  },
  {
    id: 'performanceAspects',
    title: '您希望提升的运动表现方面是？',
    type: 'multiSelect',
    options: [
      { value: 'endurance', label: '耐力', icon: '🏃' },
      { value: 'strength', label: '力量', icon: '🏋️' },
      { value: 'speed', label: '速度', icon: '⚡' },
      { value: 'explosiveness', label: '爆发力', icon: '💥' },
      { value: 'agility', label: '敏捷性', icon: '🤸' },
      { value: 'flexibility', label: '柔韧性', icon: '🧘' },
      { value: 'balance', label: '平衡性', icon: '⚖️' },
      { value: 'coordination', label: '协调性', icon: '🤹' },
    ],
    description: '了解您希望提升的具体方面有助于我们为您定制更有针对性的训练计划'
  },
  {
    id: 'weeklyWorkoutFrequency',
    title: '您每周的运动次数是？',
    options: [
      { value: '1-2', label: '1-2次', icon: '1️⃣' },
      { value: '3-4', label: '3-4次', icon: '3️⃣' },
      { value: '5-6', label: '5-6次', icon: '5️⃣' },
      { value: '7+', label: '7次及以上', icon: '7️⃣' },
    ],
    description: '了解您的运动频率有助于我们为您制定合理的训练计划'
  },
  {
    id: 'workoutDuration',
    title: '您每次运动的时长是？',
    options: [
      { value: '<30min', label: '少于30分钟', icon: '⏱️' },
      { value: '30-60min', label: '30-60分钟', icon: '⏱️' },
      { value: '60-90min', label: '60-90分钟', icon: '⏱️' },
      { value: '>90min', label: '90分钟以上', icon: '⏱️' },
    ],
    description: '了解您的运动时长有助于我们为您安排合理的训练量'
  },
  {
    id: 'workoutIntensity',
    title: '您通常的运动强度是？',
    options: [
      { value: 'low', label: '低强度（能轻松交谈）', icon: '🚶' },
      { value: 'medium', label: '中等强度（能断续交谈）', icon: '🏃' },
      { value: 'high', label: '高强度（几乎无法交谈）', icon: '🏋️' },
    ],
    description: '了解您的运动强度有助于我们为您设计合适的训练计划'
  },

  // 步骤14：饮食偏好 - 药膳饮食
  {
    id: 'medicinalDietPlan',
    title: '您是否愿意加入药膳饮食计划？',
    options: [
      { value: 'daily', label: '一日一次', icon: '🌿' },
      { value: 'threedays', label: '三日一次', icon: '🍵' },
      { value: 'weekly', label: '一周一次', icon: '🌱' },
      { value: 'monthly', label: '一月一次', icon: '🍲' },
      { value: 'none', label: '不愿意', icon: '❌' },
    ],
    description: '药膳是将中药材与食材相结合，有助于调理身体机能'
  },
  // 饮食偏好 - 喜欢的食材
  {
    id: 'favoriteFoods',
    title: '您喜欢哪些食材？',
    type: 'multiSelect',
    options: [
      { value: 'meat', label: '肉类（牛肉、猪肉等）', icon: '🥩' },
      { value: 'poultry', label: '禽类（鸡肉、鸭肉等）', icon: '🍗' },
      { value: 'fish', label: '鱼类', icon: '🐟' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'dairy', label: '奶制品', icon: '🧀' },
      { value: 'vegetables', label: '蔬菜', icon: '🥦' },
      { value: 'fruits', label: '水果', icon: '🍎' },
      { value: 'grains', label: '谷物（米饭、面食等）', icon: '🍚' },
      { value: 'beans', label: '豆类', icon: '🫘' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
    ],
    description: '了解您喜欢的食材有助于我们为您定制更符合口味的饮食计划'
  },
  // 饮食偏好 - 不喜欢的食材
  {
    id: 'dislikedFoods',
    title: '您不喜欢哪些食材？',
    type: 'multiSelect',
    options: [
      { value: 'meat', label: '肉类（牛肉、猪肉等）', icon: '🥩' },
      { value: 'poultry', label: '禽类（鸡肉、鸭肉等）', icon: '🍗' },
      { value: 'fish', label: '鱼类', icon: '🐟' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'dairy', label: '奶制品', icon: '🧀' },
      { value: 'vegetables', label: '蔬菜', icon: '🥦' },
      { value: 'fruits', label: '水果', icon: '🍎' },
      { value: 'grains', label: '谷物（米饭、面食等）', icon: '🍚' },
      { value: 'beans', label: '豆类', icon: '🫘' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
      { value: 'none', label: '没有特别不喜欢的食材', icon: '✅' },
    ],
    description: '了解您不喜欢的食材有助于我们避免在饮食计划中包含这些食材'
  },
  // 饮食偏好 - 宗教饮食限制
  {
    id: 'religiousDietaryRestrictions',
    title: '您是否有宗教相关的饮食限制？',
    options: [
      { value: 'none', label: '无宗教饮食限制', icon: '✅' },
      { value: 'halal', label: '清真饮食 (伊斯兰教)', icon: '🌙' },
      { value: 'kosher', label: '洁食 (犹太教)', icon: '✡️' },
      { value: 'hindu', label: '印度教饮食限制', icon: '🕉️' },
      { value: 'buddhist', label: '佛教饮食限制', icon: '☸️' },
      { value: 'other', label: '其他宗教饮食限制', icon: '🙏' },
    ],
    description: '了解您的宗教饮食限制有助于我们为您提供符合要求的饮食计划'
  },
  // 饮食偏好 - 个人忌口
  {
    id: 'personalDietaryRestrictions',
    title: '您有哪些个人忌口？',
    type: 'multiSelect',
    options: [
      { value: 'vegetarian', label: '素食', icon: '🥗' },
      { value: 'vegan', label: '纯素', icon: '🌱' },
      { value: 'noBeef', label: '不吃牛肉', icon: '🐄' },
      { value: 'noPork', label: '不吃猪肉', icon: '🐖' },
      { value: 'noSeafood', label: '不吃海鲜', icon: '🦐' },
      { value: 'noSpicy', label: '不吃辛辣食物', icon: '🌶️' },
      { value: 'lowSalt', label: '低盐饮食', icon: '🧂' },
      { value: 'lowSugar', label: '低糖饮食', icon: '🍬' },
      { value: 'other', label: '其他', icon: '❓' },
      { value: 'none', label: '无特殊忌口', icon: '✅' },
    ],
    description: '了解您的个人忌口有助于我们为您提供更符合个人需求的饮食计划'
  },
  // 健身饮食偏好 - 高蛋白食材
  {
    id: 'highProteinFoodPreference',
    title: '您对高蛋白食材的接受度如何？',
    options: [
      { value: 'high', label: '非常喜欢（如鸡胸肉、鸡蛋、牛肉等）', icon: '🥩' },
      { value: 'medium', label: '一般接受（可以适量食用）', icon: '🍗' },
      { value: 'low', label: '不太喜欢（很少主动选择）', icon: '🙅‍♂️' },
    ],
    description: '高蛋白食材对肌肉生长和恢复非常重要'
  },
  // 健身饮食偏好 - 补剂使用
  {
    id: 'supplementUsage',
    title: '您是否有使用健身补剂的习惯？',
    options: [
      { value: 'yes', label: '是的，有使用', icon: '💊' },
      { value: 'no', label: '没有使用', icon: '🙅‍♂️' },
    ],
    description: '了解您的补剂使用情况有助于我们为您定制更合适的饮食计划'
  },
  // 健身饮食偏好 - 补剂类型
  {
    id: 'supplementTypes',
    title: '您使用哪些类型的健身补剂？',
    type: 'multiSelect',
    options: [
      { value: 'proteinPowder', label: '蛋白粉', icon: '🥛' },
      { value: 'creatine', label: '肌酸', icon: '💪' },
      { value: 'bcaa', label: '支链氨基酸(BCAA)', icon: '🧪' },
      { value: 'preworkout', label: '训练前补剂', icon: '⚡' },
      { value: 'vitamins', label: '维生素/矿物质', icon: '💊' },
      { value: 'fatBurner', label: '减脂产品', icon: '🔥' },
      { value: 'other', label: '其他补剂', icon: '❓' },
    ],
    description: '了解您使用的补剂类型有助于我们为您提供更全面的营养建议'
  },
  // 健身饮食偏好 - 运动饮食
  {
    id: 'workoutMealPreference',
    title: '您在运动前后有什么饮食偏好？',
    options: [
      { value: 'preworkoutCarbs', label: '运动前喜欢碳水食物（如香蕉、燕麦）', icon: '🍌' },
      { value: 'postworkoutProtein', label: '运动后喜欢高蛋白食物（如蛋白奶昔）', icon: '🥛' },
      { value: 'bothBalanced', label: '前后都注重均衡营养', icon: '⚖️' },
      { value: 'noPreference', label: '没有特别偏好', icon: '🤷' },
    ],
    description: '了解您的运动饮食偏好有助于我们为您提供更个性化的建议'
  },
  // 健身饮食习惯 - 运动前加餐
  {
    id: 'preworkoutMeal',
    title: '您是否有运动前加餐的习惯？',
    options: [
      { value: 'yes', label: '是的，总是', icon: '🍽️' },
      { value: 'sometimes', label: '有时候', icon: '🤔' },
      { value: 'no', label: '没有', icon: '🙅‍♂️' },
    ],
    description: '运动前适当加餐可以提供训练所需能量'
  },
  // 健身饮食习惯 - 运动后加餐
  {
    id: 'postworkoutMeal',
    title: '您是否有运动后加餐的习惯？',
    options: [
      { value: 'yes', label: '是的，总是', icon: '🍽️' },
      { value: 'sometimes', label: '有时候', icon: '🤔' },
      { value: 'no', label: '没有', icon: '🙅‍♂️' },
    ],
    description: '运动后及时补充营养有助于肌肉恢复和生长'
  },
  // 健身饮食习惯 - 运动日与非运动日饮食差异
  {
    id: 'workoutDayDietDifference',
    title: '您希望运动日与非运动日的饮食有何差异？',
    options: [
      { value: 'significant', label: '明显差异（如运动日高碳水）', icon: '📊' },
      { value: 'slight', label: '轻微差异（如运动日略增加热量）', icon: '📈' },
      { value: 'none', label: '无需差异（保持一致即可）', icon: '⚖️' },
    ],
    description: '根据训练与休息日的不同需求调整饮食可以优化身体成分和训练效果'
  },

  // 步骤19：健康状况采集 - 食物过敏史
  {
    id: 'foodAllergies',
    title: '您是否有食物过敏史？',
    type: 'multiSelect',
    options: [
      { value: 'dairy', label: '乳制品', icon: '🥛' },
      { value: 'gluten', label: '麸质', icon: '🌾' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'soy', label: '大豆', icon: '🫘' },
      { value: 'other', label: '其他', icon: '⚠️' },
      { value: 'none', label: '无过敏史', icon: '✅' },
    ],
    description: '了解您的食物过敏情况有助于我们避免在饮食计划中包含可能引起过敏的食材'
  },
  // 步骤20：健康状况采集 - 慢性疾病
  {
    id: 'chronicDiseases',
    title: '您是否患有以下慢性疾病？',
    type: 'multiSelect',
    options: [
      { value: 'hypertension', label: '高血压', icon: '🩸' },
      { value: 'diabetes', label: '糖尿病', icon: '🍬' },
      { value: 'heartDisease', label: '心脏疾病', icon: '❤️' },
      { value: 'thyroid', label: '甲状腺疾病', icon: '🦋' },
      { value: 'liver', label: '肝脏疾病', icon: '🫁' },
      { value: 'kidney', label: '肾脏疾病', icon: '🫘' },
      { value: 'other', label: '其他', icon: '🏥' },
      { value: 'none', label: '无慢性疾病', icon: '✅' },
    ],
    description: '了解您的慢性疾病情况有助于我们制定安全、合适的减重计划'
  },
  // 步骤21：健康状况采集 - 用药情况
  {
    id: 'medications',
    title: '您目前是否正在服用以下药物？',
    type: 'multiSelect',
    options: [
      { value: 'antihypertensive', label: '降压药', icon: '💊' },
      { value: 'antidiabetic', label: '降糖药', icon: '💉' },
      { value: 'steroid', label: '类固醇', icon: '🧪' },
      { value: 'antidepressant', label: '抗抑郁药', icon: '😊' },
      { value: 'thyroid', label: '甲状腺药物', icon: '🦋' },
      { value: 'other', label: '其他长期用药', icon: '💊' },
      { value: 'none', label: '无长期用药', icon: '✅' },
    ],
    description: '某些药物可能会影响体重变化，了解您的用药情况有助于我们调整减重计划'
  },
  // 步骤22：健康状况采集 - 睡眠质量
  {
    id: 'sleepQuality',
    title: '您的睡眠质量如何？',
    options: [
      { value: 'good', label: '良好（睡眠充足，深沉）', icon: '😴' },
      { value: 'fair', label: '一般（睡眠时间不足或质量一般）', icon: '😐' },
      { value: 'poor', label: '较差（经常失眠或睡眠浅）', icon: '😫' },
      { value: 'irregular', label: '不规律（作息时间不固定）', icon: '🔄' },
    ],
    description: '睡眠质量与代谢、饮食习惯和体重管理密切相关'
  },
  // 步骤23：中医体质相关症状 - 身体沉重感
  {
    id: 'bodyHeaviness',
    title: '您是否经常感到身体沉重、容易困倦？',
    options: [
      { value: 'yes', label: '是', icon: '😪' },
      { value: 'no', label: '否', icon: '😊' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '身体沉重、困倦可能与中医湿热体质相关'
  },
  // 步骤24：中医体质相关症状 - 温度敏感性
  {
    id: 'temperatureSensitivity',
    title: '您是否比常人更怕热或怕冷？',
    options: [
      { value: 'heat', label: '怕热明显', icon: '🔥' },
      { value: 'cold', label: '怕冷明显', icon: '❄️' },
      { value: 'normal', label: '无特殊', icon: '🌡️' },
    ],
    description: '温度敏感度可能与中医阴阳平衡状态相关'
  },
  // 步骤25：中医体质相关症状 - 油性皮肤
  {
    id: 'oilySkin',
    title: '您的皮肤是否容易出油、长痘？',
    options: [
      { value: 'yes', label: '是', icon: '💦' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '油性皮肤、易长痘可能与中医湿热体质有关'
  },
  // 步骤26：中医体质相关症状 - 出汗
  {
    id: 'sweating',
    title: '您是否容易出汗，且汗后感觉更累？',
    options: [
      { value: 'yes', label: '是', icon: '💦' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'onlyExercise', label: '仅运动后出汗', icon: '🏃' },
    ],
    description: '容易出汗且汗后乏力可能与中医气虚体质相关'
  },
  // 步骤27：中医体质相关症状 - 腰痛
  {
    id: 'backPain',
    title: '您是否经常感到腰部酸痛、四肢无力？',
    options: [
      { value: 'yes', label: '是', icon: '🤕' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '腰部酸痛、四肢无力可能与中医肾虚体质相关'
  },
  // 步骤28：中医体质相关症状 - 食欲
  {
    id: 'appetite',
    title: '您吃饭时是否容易没胃口，或吃一点就饱？',
    options: [
      { value: 'yes', label: '是', icon: '🍽️' },
      { value: 'no', label: '否', icon: '🙅‍♂️' },
      { value: 'normal', label: '正常', icon: '👋' },
    ],
    description: '食欲不振可能与中医脾胃虚弱相关'
  },
  // 步骤29：中医体质相关症状 - 头发状况
  {
    id: 'hairCondition',
    title: '您是否有头发油腻、脱发的情况？',
    options: [
      { value: 'yes', label: '是', icon: '👩‍🦲' },
      { value: 'no', label: '否', icon: '👩‍🦱' },
      { value: 'mild', label: '轻微', icon: '👩‍🦳' },
    ],
    description: '头发油腻、脱发可能与中医肝肾功能失调相关'
  },
  // 步骤30：中医体质相关症状 - 月经规律性（仅女性）
  {
    id: 'menstrualRegularity',
    title: '月经周期是否规律？',
    options: [
      { value: 'yes', label: '是', icon: '👩‍🦰' },
      { value: 'no', label: '否', icon: '👩‍🦰' },
      { value: 'sometimes', label: '偶尔', icon: '👩‍🦰' },
    ],
    description: '月经不规律可能与中医气血失调相关（此问题仅女性需要回答）'
  }
];

// 定义采集阶段
const COLLECTION_PHASES = {
  BODY_DATA: 'bodyData',
  FITNESS_GOALS: 'fitnessGoals',
  DIET_PREFERENCES: 'dietPreferences',
  HEALTH_STATUS: 'healthStatus',
  TCM_SYMPTOMS: 'tcmSymptoms'
} as const

type CollectionPhase = typeof COLLECTION_PHASES[keyof typeof COLLECTION_PHASES]

// 身体数据采集步骤（前8步：性别、出生日期、身高、体重、体脂率、肌肉量、静息心率、围度测量）
const bodyDataSteps = formSteps.slice(0, 8)
// 添加引导步骤和总结步骤
const bodyDataStepsWithSummary = [
  {
    id: 'bodyDataIntro',
    title: '身体数据采集',
    type: 'intro'
  },
  ...bodyDataSteps,
  {
    id: 'bodyDataSummary',
    title: '基于您的回答的个人总结',
    type: 'summary'
  }
]

// 健身目标采集步骤（第9-17步：健身目标类型、增肌目标、塑形部位、运动项目、运动表现、运动习惯等）
const fitnessGoalsStepsOriginal = formSteps.slice(8, 17)
// 添加引导步骤和总结步骤到健身目标采集
const fitnessGoalsSteps = [
  {
    id: 'fitnessGoalsIntro',
    title: '健身目标设定',
    type: 'intro'
  },
  ...fitnessGoalsStepsOriginal,
  {
    id: 'fitnessGoalsSummary',
    title: '您的健身目标总结',
    type: 'summary'
  }
]

// 饮食偏好采集步骤（第18-29步：药膳、喜欢的食材、不喜欢的食材、宗教限制、个人忌口、高蛋白偏好、补剂使用、运动饮食等）
const dietPreferencesStepsOriginal = formSteps.slice(17, 29)
// 添加引导步骤和总结步骤到饮食偏好采集
const dietPreferencesSteps = [
  {
    id: 'dietPreferencesIntro',
    title: '饮食偏好了解',
    type: 'intro'
  },
  ...dietPreferencesStepsOriginal,
  {
    id: 'dietPreferencesSummary',
    title: '您的饮食偏好总结',
    type: 'summary'
  }
]

// 健康状况采集步骤（食物过敏史、慢性疾病、用药情况、睡眠质量）
const healthStatusStepsOriginal = formSteps.filter(step =>
  step.id === 'foodAllergies' ||
  step.id === 'chronicDiseases' ||
  step.id === 'medications' ||
  step.id === 'sleepQuality'
)
// 添加引导步骤和总结步骤到健康状况采集
const healthStatusSteps = [
  {
    id: 'healthStatusIntro',
    title: '健康状况了解',
    type: 'intro'
  },
  ...healthStatusStepsOriginal,
  {
    id: 'healthStatusSummary',
    title: '您的健康状况总结',
    type: 'summary'
  }
]

// 中医体质相关症状采集步骤（步骤29-36：8个中医体质症状）
const tcmSymptomsStepsOriginal = formSteps.filter(step =>
  step.id === 'bodyHeaviness' ||
  step.id === 'temperatureSensitivity' ||
  step.id === 'oilySkin' ||
  step.id === 'sweating' ||
  step.id === 'backPain' ||
  step.id === 'appetite' ||
  step.id === 'hairCondition' ||
  step.id === 'menstrualRegularity'
)
// 添加引导步骤和总结步骤到中医体质相关症状采集
const tcmSymptomsSteps = [
  {
    id: 'tcmSymptomsIntro',
    title: '中医体质相关症状了解',
    type: 'intro'
  },
  ...tcmSymptomsStepsOriginal,
  {
    id: 'tcmSymptomsSummary',
    title: '您的中医体质相关症状总结',
    type: 'summary'
  }
]

export default function WeightLossForm({ lang, onBack }: WeightLossFormProps) {
  const [currentPhase, setCurrentPhase] = useState<CollectionPhase>(COLLECTION_PHASES.BODY_DATA)
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  const router = useRouter()

  // 生成用户数据摘要
  const generateUserDataSummary = (formData: any) => {
    // 计算BMI
    const bmi = formData.height && formData.weight
      ? (parseFloat(formData.weight) / Math.pow(parseFloat(formData.height) / 100, 2)).toFixed(1)
      : '未知'

    // 计算年龄
    const calculateAge = (birthDate: string) => {
      if (!birthDate) return '未提供'
      const birth = new Date(birthDate)
      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age.toString()
    }

    // 获取用户选择的群体并映射到中文名称
    const selectedGroup = typeof window !== 'undefined' ? localStorage.getItem('selected_audience_group') : null
    const getGroupIdentity = (groupId: string | null) => {
      switch(groupId) {
        case 'weight-loss':
          return '希望健康瘦身人群'
        case 'fitness':
          return '健身爱好者'
        case 'white-collar':
          return '都市白领'
        default:
          return '健身爱好者' // 默认值
      }
    }

    // 格式化体脂率信息
    const formatBodyFat = () => {
      if (formData.knowsBodyFat === 'yes' && formData.bodyFatPercentage) {
        return `${formData.bodyFatPercentage}%`
      }
      return '不清楚'
    }

    // 格式化肌肉量信息
    const formatMuscleMass = () => {
      if (formData.knowsMuscleMass === 'yes' && formData.muscleMass) {
        return `${formData.muscleMass}${formData.muscleMassUnit === 'kg' ? '千克' : '磅'}`
      }
      return '不清楚'
    }

    return `
基础数据信息：
- 身份：${getGroupIdentity(selectedGroup)}
- 性别：${formData.gender === 'male' ? '男' : '女'}
- 年龄：${calculateAge(formData.birthDate)}岁
- 身高：${formData.height || '未提供'}${formData.heightUnit || 'cm'}
- 体重：${formData.weight || '未提供'}${formData.weightUnit || 'kg'}
- BMI：${bmi}
- 体脂率：${formatBodyFat()}
- 肌肉量：${formatMuscleMass()}
- 静息心率：${formData.restingHeartRate ? `${formData.restingHeartRate}次/分钟` : '未填写'}

健身目标：
- 健身目标类型：${formData.fitnessGoalType === 'muscleGain' ? '增肌' :
                formData.fitnessGoalType === 'bodyShaping' ? '塑形' :
                formData.fitnessGoalType === 'performanceImprovement' ? '提升运动表现' : '未设定'}

运动习惯：
- 运动频率：${formData.weeklyWorkoutFrequency ? `每周${formData.weeklyWorkoutFrequency}次` : '未填写'}
- 运动时长：${formData.workoutDuration ? (() => {
  const durationMap: { [key: string]: string } = {
    '<30min': '少于30分钟',
    '30-60min': '30-60分钟',
    '60-90min': '60-90分钟',
    '>90min': '90分钟以上'
  };
  return durationMap[formData.workoutDuration] || formData.workoutDuration;
})() : '未填写'}
- 运动强度：${formData.workoutIntensity === 'low' ? '低强度（能轻松交谈）' :
            formData.workoutIntensity === 'medium' ? '中等强度（能断续交谈）' :
            formData.workoutIntensity === 'high' ? '高强度（几乎无法交谈）' : '未填写'}

饮食偏好：
- 药膳饮食计划：${formData.medicinalDietPlan === 'daily' ? '一日一次' :
                formData.medicinalDietPlan === 'threedays' ? '三日一次' :
                formData.medicinalDietPlan === 'weekly' ? '一周一次' :
                formData.medicinalDietPlan === 'monthly' ? '一月一次' :
                formData.medicinalDietPlan === 'none' ? '不愿意' : '未选择'}
- 高蛋白食材接受度：${formData.highProteinFoodPreference === 'high' ? '非常喜欢' :
                    formData.highProteinFoodPreference === 'medium' ? '一般接受' :
                    formData.highProteinFoodPreference === 'low' ? '不太喜欢' : '未选择'}
- 补剂使用情况：${formData.supplementUsage === 'yes' ? '有使用' :
                formData.supplementUsage === 'no' ? '没有使用' : '未选择'}
- 运动前加餐习惯：${formData.preworkoutMeal === 'yes' ? '总是' :
                  formData.preworkoutMeal === 'sometimes' ? '有时候' :
                  formData.preworkoutMeal === 'no' ? '没有' : '未填写'}
- 运动后加餐习惯：${formData.postworkoutMeal === 'yes' ? '总是' :
                  formData.postworkoutMeal === 'sometimes' ? '有时候' :
                  formData.postworkoutMeal === 'no' ? '没有' : '未填写'}

健康状况：
- 食物过敏史：${formData.foodAllergies && formData.foodAllergies.length > 0 ?
              (formData.foodAllergies.includes('none') ? '无过敏史' : formData.foodAllergies.join('、')) : '未填写'}
- 慢性疾病：${formData.chronicDiseases && formData.chronicDiseases.length > 0 ?
            (formData.chronicDiseases.includes('none') ? '无慢性疾病' : formData.chronicDiseases.join('、')) : '未填写'}
- 用药情况：${formData.medications && formData.medications.length > 0 ?
            (formData.medications.includes('none') ? '无长期用药' : formData.medications.join('、')) : '未填写'}
- 睡眠质量：${formData.sleepQuality === 'good' ? '良好' :
            formData.sleepQuality === 'fair' ? '一般' :
            formData.sleepQuality === 'poor' ? '较差' :
            formData.sleepQuality === 'irregular' ? '不规律' : '未填写'}

中医体质相关症状：
- 身体沉重感：${formData.bodyHeaviness === 'yes' ? '是，经常感到身体沉重、容易困倦' : formData.bodyHeaviness === 'sometimes' ? '偶尔感到身体沉重' : '否，身体感觉轻松'}
- 温度敏感性：${formData.temperatureSensitivity === 'heat' ? '怕热明显' : formData.temperatureSensitivity === 'cold' ? '怕冷明显' : '无特殊温度敏感'}
- 皮肤状况：${formData.oilySkin === 'yes' ? '是，皮肤容易出油、长痘' : formData.oilySkin === 'sometimes' ? '偶尔出油或长痘' : '否，皮肤状况正常'}
- 出汗情况：${formData.sweating === 'yes' ? '是，容易出汗且汗后感觉更累' : formData.sweating === 'onlyExercise' ? '仅运动后出汗' : '否，出汗正常'}
- 腰部状况：${formData.backPain === 'yes' ? '是，经常感到腰部酸痛、四肢无力' : formData.backPain === 'sometimes' ? '偶尔腰部酸痛' : '否，腰部状况正常'}
- 食欲状况：${formData.appetite === 'yes' ? '是，吃饭时容易没胃口或吃一点就饱' : '否，食欲正常'}
- 头发状况：${formData.hairCondition === 'yes' ? '是，头发油腻容易脱发' : formData.hairCondition === 'mild' ? '轻微油腻或脱发' : '否，头发状况正常'}
${formData.gender === 'female' ? `- 月经规律性：${formData.menstrualRegularity === 'yes' ? '是，月经周期规律' : '否，月经周期不规律'}` : ''}
    `.trim()
  }

  // 生成用户数据摘要对象（用于UI显示）
  const generateUserDataSummaryObject = (formData: any) => {
    const summaryData = {
      // 基本信息
      gender: formData.gender === 'male' ? '男性' : formData.gender === 'female' ? '女性' : '其他',
      age: formData.birthDate ? new Date().getFullYear() - new Date(formData.birthDate).getFullYear() : '未知',
      height: formData.height ? `${formData.height}${formData.heightUnit === 'cm' ? '厘米' : '英寸'}` : '未填写',
      weight: formData.weight ? `${formData.weight}${formData.weightUnit === 'kg' ? '千克' : '磅'}` : '未填写',

      // 体脂率
      bodyFat: formData.knowsBodyFat === 'yes' && formData.bodyFatPercentage ? `${formData.bodyFatPercentage}%` : '未知',

      // 肌肉量
      muscleMass: formData.knowsMuscleMass === 'yes' && formData.muscleMass ? `${formData.muscleMass}${formData.muscleMassUnit === 'kg' ? '千克' : '磅'}` : '未知',

      // 静息心率
      restingHeartRate: formData.restingHeartRate ? `${formData.restingHeartRate}次/分钟` : '未填写',

      // 健身目标
      fitnessGoalType: formData.fitnessGoalType === 'muscleGain' ? '增肌' :
                      formData.fitnessGoalType === 'bodyShaping' ? '塑形' :
                      formData.fitnessGoalType === 'performanceImprovement' ? '提升运动表现' : '未设定',

      // 运动习惯
      weeklyWorkoutFrequency: formData.weeklyWorkoutFrequency ? `每周${formData.weeklyWorkoutFrequency}次` : '未填写',
      workoutDuration: formData.workoutDuration ? formData.workoutDuration : '未填写',
      workoutIntensity: formData.workoutIntensity === 'low' ? '低强度' :
                       formData.workoutIntensity === 'medium' ? '中等强度' :
                       formData.workoutIntensity === 'high' ? '高强度' : '未填写',

      // 饮食偏好
      medicinalDietPlan: formData.medicinalDietPlan === 'daily' ? '一日一次' :
                        formData.medicinalDietPlan === 'threedays' ? '三日一次' :
                        formData.medicinalDietPlan === 'weekly' ? '一周一次' :
                        formData.medicinalDietPlan === 'monthly' ? '一月一次' :
                        formData.medicinalDietPlan === 'none' ? '不愿意' : '未选择',

      highProteinFoodPreference: formData.highProteinFoodPreference === 'high' ? '非常喜欢' :
                                formData.highProteinFoodPreference === 'medium' ? '一般接受' :
                                formData.highProteinFoodPreference === 'low' ? '不太喜欢' : '未选择',

      supplementUsage: formData.supplementUsage === 'yes' ? '有使用' :
                      formData.supplementUsage === 'no' ? '没有使用' : '未选择',

      // 健康状况
      foodAllergies: formData.foodAllergies && formData.foodAllergies.length > 0 ?
                    (formData.foodAllergies.includes('none') ? '无过敏史' : formData.foodAllergies.join('、')) : '未填写',

      chronicDiseases: formData.chronicDiseases && formData.chronicDiseases.length > 0 ?
                      (formData.chronicDiseases.includes('none') ? '无慢性疾病' : formData.chronicDiseases.join('、')) : '未填写',

      medications: formData.medications && formData.medications.length > 0 ?
                  (formData.medications.includes('none') ? '无长期用药' : formData.medications.join('、')) : '未填写',

      sleepQuality: formData.sleepQuality === 'good' ? '良好' :
                   formData.sleepQuality === 'fair' ? '一般' :
                   formData.sleepQuality === 'poor' ? '较差' :
                   formData.sleepQuality === 'irregular' ? '不规律' : '未填写'
    }

    return summaryData
  }

  const [currentStep, setCurrentStep] = useState(0)

  // 获取当前阶段的步骤数
  const getCurrentPhaseSteps = () => {
    switch(currentPhase) {
      case COLLECTION_PHASES.BODY_DATA:
        return bodyDataStepsWithSummary
      case COLLECTION_PHASES.FITNESS_GOALS:
        return fitnessGoalsSteps
      case COLLECTION_PHASES.DIET_PREFERENCES:
        return dietPreferencesSteps
      case COLLECTION_PHASES.HEALTH_STATUS:
        return healthStatusSteps
      case COLLECTION_PHASES.TCM_SYMPTOMS:
        return tcmSymptomsSteps
      default:
        return formSteps
    }
  }

  const currentPhaseSteps = getCurrentPhaseSteps()
  const totalSteps = currentPhaseSteps.length
  const [calculatedBMI, setCalculatedBMI] = useState<number | null>(null)
  const [bmiCategory, setBMICategory] = useState<string>('')
  const [heightUnit, setHeightUnit] = useState('cm')
  const [weightUnit, setWeightUnit] = useState('kg')
  const [selectedGender, setSelectedGender] = useState<string | null>(null)
  const [knowsBodyFatSelection, setKnowsBodyFatSelection] = useState<string | null>(null)
  const [knowsMuscleMassSelection, setKnowsMuscleMassSelection] = useState<string | null>(null)
  const [selectedWeightLossUnit, setSelectedWeightLossUnit] = useState<string | null>(null)

  
  // 添加本地状态来存储输入值，避免跨字段影响
  const [inputValues, setInputValues] = useState({
    height: '170',
    weight: '65',
    weightLossGoal: '5',
    goalTimeframe: '3',
    bodyFatPercentage: '',
    muscleMass: '',
    muscleGainGoal: '3',
    muscleGainTimeframe: '3',
    chestCircumference: '',
    waistCircumference: '',
    hipCircumference: '',
    armCircumference: '',
    thighCircumference: '',
    calfCircumference: '',
    restingHeartRate: ''
  });
  
  // 健身目标相关状态
  const [selectedFitnessGoalType, setSelectedFitnessGoalType] = useState<string | null>(null);
  const [selectedBodyShapingAreas, setSelectedBodyShapingAreas] = useState<string[]>([]);
  const [selectedSportType, setSelectedSportType] = useState<string | null>(null);
  const [selectedPerformanceAspects, setSelectedPerformanceAspects] = useState<string[]>([]);
  const [selectedWeeklyFrequency, setSelectedWeeklyFrequency] = useState<string | null>(null);
  const [selectedWorkoutDuration, setSelectedWorkoutDuration] = useState<string | null>(null);
  const [selectedWorkoutIntensity, setSelectedWorkoutIntensity] = useState<string | null>(null);
  const [selectedSupplementUsage, setSelectedSupplementUsage] = useState<string | null>(null);
  const [selectedSupplementTypes, setSelectedSupplementTypes] = useState<string[]>([]);
  const [selectedFavoriteFoods, setSelectedFavoriteFoods] = useState<string[]>([]);
  const [selectedDislikedFoods, setSelectedDislikedFoods] = useState<string[]>([]);
  const [selectedReligiousDietaryRestriction, setSelectedReligiousDietaryRestriction] = useState<string | null>(null);
  const [selectedPersonalDietaryRestrictions, setSelectedPersonalDietaryRestrictions] = useState<string[]>([]);
  const [selectedHighProteinPreference, setSelectedHighProteinPreference] = useState<string | null>(null);
  const [selectedWorkoutMealPreference, setSelectedWorkoutMealPreference] = useState<string | null>(null);
  const [selectedPreworkoutMeal, setSelectedPreworkoutMeal] = useState<string | null>(null);
  const [selectedPostworkoutMeal, setSelectedPostworkoutMeal] = useState<string | null>(null);
  const [selectedDietDifference, setSelectedDietDifference] = useState<string | null>(null);
  
  // 初始化表单
  const form = useForm<WeightLossFormValues>({
    resolver: zodResolver(weightLossFormSchema),
    defaultValues: {
      heightUnit: 'cm',
      weightUnit: 'kg',
      knowsBodyFat: 'no',
      gender: 'male',
      birthDate: new Date(new Date().setFullYear(new Date().getFullYear() - 30)),
      height: inputValues.height,
      weight: inputValues.weight,
      // weightChangeHistory已移除
    weightChangeHistory: 'stable' as any,
      weightLossGoal: inputValues.weightLossGoal,
      weightLossGoalUnit: 'kg',
      goalTimeframe: inputValues.goalTimeframe,
      hasDietHistory: 'no',
      dietReactions: ['none'], // 默认设置为"没有明显反应"
      
      // 健身爱好者特有字段
      knowsMuscleMass: 'no',
      muscleMassUnit: 'kg',
      circumferenceUnit: 'cm',
      muscleMass: '',
      chestCircumference: '',
      waistCircumference: '',
      hipCircumference: '',
      armCircumference: '',
      thighCircumference: '',
      calfCircumference: '',
      restingHeartRate: '',
      
      // 健身目标相关字段
      fitnessGoalType: 'muscleGain',
      muscleGainGoal: inputValues.muscleGainGoal,
      muscleGainUnit: 'kg',
      muscleGainTimeframe: inputValues.muscleGainTimeframe,
      bodyShapingAreas: [],
      sportType: 'running',
      performanceAspects: [],
      weeklyWorkoutFrequency: '3-4',
      workoutDuration: '30-60min',
      workoutIntensity: 'medium',
      favoriteFoods: [],
      dislikedFoods: [],
      religiousDietaryRestrictions: 'none',
      personalDietaryRestrictions: [],
      dietaryRestrictions: [],
      lowCalorieAcceptance: 'medium',
      unhealthyFoodPreference: [],
      eatingOutFrequency: '2-3',
      medicinalDietPlan: 'none',
      
      // 健身饮食偏好
      highProteinFoodPreference: 'medium',
      supplementUsage: 'no',
      supplementTypes: [],
      workoutMealPreference: 'noPreference',
      
      // 健身饮食习惯
      preworkoutMeal: 'sometimes',
      postworkoutMeal: 'sometimes',
      workoutDayDietDifference: 'slight',
      regularMeals: 'regular',
      snackingHabit: 'no',
      snackTypes: [],
      eatingSpeed: 'medium',
      foodAllergies: [],
      chronicDiseases: [],
      medications: [],
      sleepQuality: 'fair',
      bodyHeaviness: 'no',
      temperatureSensitivity: 'normal',
      oilySkin: 'no',
      sweating: 'no',
      backPain: 'no',
      appetite: 'normal',
      hairCondition: 'yes',
      menstrualRegularity: 'yes',
    },
  })
  
  // 监听单位变化
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.heightUnit) setHeightUnit(value.heightUnit);
      if (value.weightUnit) setWeightUnit(value.weightUnit);
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);
  
  // 监听表单值变化，计算BMI
  useEffect(() => {
    // 确保有身高和体重值时计算BMI
    if (inputValues.height && inputValues.weight) {
      const heightUnit = form.getValues('heightUnit');
      const weightUnit = form.getValues('weightUnit');
      if (heightUnit && weightUnit) {
        calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
      }
    }
  }, [inputValues.height, inputValues.weight]);
  
  // 监听表单单位变化
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if ((name === 'heightUnit' || name === 'weightUnit') 
          && inputValues.height && inputValues.weight) {
        const heightUnit = name === 'heightUnit' ? value.heightUnit : form.getValues('heightUnit');
        const weightUnit = name === 'weightUnit' ? value.weightUnit : form.getValues('weightUnit');
        if (heightUnit && weightUnit) {
          calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, inputValues.height, inputValues.weight]);
  
  // 监听体脂率输入值变化，自动清除错误
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      // 当体脂率字段变化且有值时
      if (name === 'bodyFatPercentage' && value.bodyFatPercentage) {
        const bodyFatValue = value.bodyFatPercentage;
        // 如果输入了有效的体脂率数值，清除错误
        const numericValue = parseFloat(bodyFatValue);
        if (!isNaN(numericValue) && numericValue > 0 && numericValue <= 100) {
          if (form.formState.errors.bodyFatPercentage) {
            form.clearErrors('bodyFatPercentage');
          }
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);
  

  
  // 计算BMI
  const calculateBMI = (height: string, heightUnit: string, weight: string, weightUnit: string) => {
    try {
      let heightInMeters: number
      let weightInKg: number
      
      // 转换身高为米
      if (heightUnit === 'cm') {
        heightInMeters = parseFloat(height) / 100
      } else {
        heightInMeters = parseFloat(height) * 0.0254 // 英寸转米
      }
      
      // 转换体重为千克
      if (weightUnit === 'kg') {
        weightInKg = parseFloat(weight)
      } else {
        weightInKg = parseFloat(weight) * 0.453592 // 磅转千克
      }
      
      if (heightInMeters <= 0 || weightInKg <= 0) {
        setCalculatedBMI(null)
        setBMICategory('')
        return
      }
      
      // 计算BMI: 体重(kg) / 身高(m)的平方
      const bmi = weightInKg / (heightInMeters * heightInMeters)
      setCalculatedBMI(parseFloat(bmi.toFixed(1)))
      
      // 确定BMI类别
      if (bmi < 18.5) {
        setBMICategory('体重过轻')
      } else if (bmi >= 18.5 && bmi < 24) {
        setBMICategory('正常范围')
      } else if (bmi >= 24 && bmi < 28) {
        setBMICategory('超重')
      } else {
        setBMICategory('肥胖')
      }
    } catch (error) {
      setCalculatedBMI(null)
      setBMICategory('')
    }
  }
  
  // 处理下一步
  const handleNext = () => {
    const currentStepId = currentPhaseSteps[currentStep].id
    
    // 对于体脂率特殊处理
    if (currentStepId === 'knowsBodyFat' && form.getValues('knowsBodyFat') === 'yes') {
      // 如果用户知道体脂率，需要先填写体脂率后才能进入下一步
      const bodyFatValue = form.getValues('bodyFatPercentage');
      // 检查输入值是否存在且不为空字符串
      if (!bodyFatValue || bodyFatValue.trim() === '') {
        form.setError('bodyFatPercentage', {
          type: 'manual',
          message: '请输入您的体脂率'
        });
        return;
      }
      
      // 检查输入值是否为有效数字
      const numericValue = parseFloat(bodyFatValue);
      if (isNaN(numericValue) || numericValue <= 0 || numericValue > 100) {
        form.setError('bodyFatPercentage', {
          type: 'manual',
          message: '请输入有效的体脂率数值（1-100之间）'
        });
        return;
      }
    }
    
    // 对于肌肉量特殊处理
    if (currentStepId === 'knowsMuscleMass' && form.getValues('knowsMuscleMass') === 'yes') {
      // 如果用户知道肌肉量，需要先填写肌肉量后才能进入下一步
      const muscleMassValue = form.getValues('muscleMass');
      // 检查输入值是否存在且不为空字符串
      if (!muscleMassValue || muscleMassValue.trim() === '') {
        form.setError('muscleMass', {
          type: 'manual',
          message: '请输入您的肌肉量'
        });
        return;
      }
      
      // 检查输入值是否为有效数字
      const numericValue = parseFloat(muscleMassValue);
      if (isNaN(numericValue) || numericValue <= 0) {
        form.setError('muscleMass', {
          type: 'manual',
          message: '请输入有效的肌肉量数值'
        });
        return;
      }
    }
    
    // 对于静息心率特殊处理
    if (currentStepId === 'restingHeartRate') {
      const heartRateValue = form.getValues('restingHeartRate');
      if (heartRateValue && heartRateValue.trim() !== '') {
        // 如果填写了心率，检查是否为有效数字
        const numericValue = parseFloat(heartRateValue);
        if (isNaN(numericValue) || numericValue <= 0 || numericValue > 200) {
          form.setError('restingHeartRate', {
          type: 'manual',
            message: '请输入有效的心率数值（一般在40-120次/分钟之间）'
        });
        return;
        }
      }
    }
    
    // 减重目标步骤已移除
    
    // 对于健身目标类型特殊处理
    if (currentStepId === 'fitnessGoalType') {
      const goalType = form.getValues('fitnessGoalType');
      if (!goalType) {
        form.setError('fitnessGoalType', {
          type: 'manual',
          message: '请选择您的健身目标类型'
        });
        return;
      }
      
      // 根据选择的健身目标类型，跳转到对应的步骤
      if (goalType === 'muscleGain') {
        // 如果选择了增肌，跳转到增肌目标步骤
        const muscleGainGoalIndex = currentPhaseSteps.findIndex(step => step.id === 'muscleGainGoal');
        if (muscleGainGoalIndex !== -1) {
          setCurrentStep(muscleGainGoalIndex);
          return;
        }
      } else if (goalType === 'bodyShaping') {
        // 如果选择了塑形，跳转到塑形部位步骤
        const bodyShapingAreasIndex = currentPhaseSteps.findIndex(step => step.id === 'bodyShapingAreas');
        if (bodyShapingAreasIndex !== -1) {
          setCurrentStep(bodyShapingAreasIndex);
          return;
        }
      } else if (goalType === 'performanceImprovement') {
        // 如果选择了提升运动表现，跳转到运动项目步骤
        const sportTypeIndex = currentPhaseSteps.findIndex(step => step.id === 'sportType');
        if (sportTypeIndex !== -1) {
          setCurrentStep(sportTypeIndex);
          return;
        }
      }
    }
    
    // 对于增肌目标特殊处理
    if (currentStepId === 'muscleGainGoal') {
      if (!form.getValues('muscleGainGoal')) {
        form.setError('muscleGainGoal', {
          type: 'manual',
          message: '请输入您期望增加的肌肉量'
        });
        return;
      }
    }
    
    // 对于塑形部位特殊处理
    if (currentStepId === 'bodyShapingAreas') {
      const areas = form.getValues('bodyShapingAreas');
      if (!areas || areas.length === 0) {
        form.setError('bodyShapingAreas', {
          type: 'manual',
          message: '请至少选择一个希望塑形的身体部位'
        });
        return;
      }
      
      // 塑形部位选择后，跳转到运动习惯步骤
      const weeklyWorkoutFrequencyIndex = currentPhaseSteps.findIndex(step => step.id === 'weeklyWorkoutFrequency');
      if (weeklyWorkoutFrequencyIndex !== -1) {
        setCurrentStep(weeklyWorkoutFrequencyIndex);
        return;
      }
    }
    
    // 对于运动项目特殊处理
    if (currentStepId === 'sportType') {
      if (!form.getValues('sportType')) {
        form.setError('sportType', {
          type: 'manual',
          message: '请选择您希望提升表现的运动项目'
        });
        return;
      }
    }
    
    // 对于运动表现方面特殊处理
    if (currentStepId === 'performanceAspects') {
      const aspects = form.getValues('performanceAspects');
      if (!aspects || aspects.length === 0) {
        form.setError('performanceAspects', {
          type: 'manual',
          message: '请至少选择一个希望提升的运动表现方面'
        });
        return;
      }
      
      // 运动表现方面选择后，跳转到运动习惯步骤
      const weeklyWorkoutFrequencyIndex = currentPhaseSteps.findIndex(step => step.id === 'weeklyWorkoutFrequency');
      if (weeklyWorkoutFrequencyIndex !== -1) {
        setCurrentStep(weeklyWorkoutFrequencyIndex);
        return;
      }
    }
    
    // 对于肌肉增长时间特殊处理
    if (currentStepId === 'muscleGainTimeframe') {
      if (!form.getValues('muscleGainTimeframe')) {
        form.setError('muscleGainTimeframe', {
          type: 'manual',
          message: '请选择您期望达成增肌目标的时间'
        });
        return;
      }
      
      // 增肌时间选择后，跳转到运动习惯步骤
      const weeklyWorkoutFrequencyIndex = currentPhaseSteps.findIndex(step => step.id === 'weeklyWorkoutFrequency');
      if (weeklyWorkoutFrequencyIndex !== -1) {
        setCurrentStep(weeklyWorkoutFrequencyIndex);
        return;
      }
    }
    
    // 对于补剂使用特殊处理
    if (currentStepId === 'supplementUsage') {
      // 如果用户选择不使用补剂，跳过补剂类型步骤
      if (form.getValues('supplementUsage') === 'no') {
        // 查找补剂类型步骤的索引
        const supplementTypesIndex = currentPhaseSteps.findIndex(step => step.id === 'supplementTypes');
        if (supplementTypesIndex !== -1 && currentStep < currentPhaseSteps.length - 1) {
          // 跳过补剂类型步骤
          setCurrentStep(supplementTypesIndex + 1);
          return;
        }
      }
    }
    
    // 对于运动饮食偏好特殊处理
    if (currentStepId === 'workoutMealPreference') {
      // 确保选择了一个选项
      if (!form.getValues('workoutMealPreference')) {
        form.setError('workoutMealPreference', {
          type: 'manual',
          message: '请选择您的运动饮食偏好'
        });
        return;
      }
    }
    

    
    // 特殊处理引导步骤
    if (currentStepId === 'bodyDataIntro' || currentStepId === 'fitnessGoalsIntro' || currentStepId === 'dietPreferencesIntro' || currentStepId === 'healthStatusIntro' || currentStepId === 'tcmSymptomsIntro') {
      setCurrentStep(prev => prev + 1);
      return;
    }

    // 特殊处理总结步骤
    if (currentStepId === 'bodyDataSummary') {
      // 直接进入健身目标采集阶段
      setCurrentPhase(COLLECTION_PHASES.FITNESS_GOALS);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'fitnessGoalsSummary') {
      // 健身目标采集完成，直接进入饮食偏好采集阶段
      setCurrentPhase(COLLECTION_PHASES.DIET_PREFERENCES);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'dietPreferencesSummary') {
      // 饮食偏好采集完成，直接进入健康状况采集阶段
      setCurrentPhase(COLLECTION_PHASES.HEALTH_STATUS);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'healthStatusSummary') {
      // 健康状况采集完成，直接进入中医体质相关症状采集阶段
      setCurrentPhase(COLLECTION_PHASES.TCM_SYMPTOMS);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'tcmSymptomsSummary') {
      // 所有阶段完成，提交表单
      onSubmit(form.getValues());
      return;
    }

    // 如果当前步骤是hairCondition，且用户性别不是女性，则跳过menstrualRegularity步骤
    if (currentStepId === 'hairCondition' && form.getValues('gender') !== 'female') {
      // 设置默认值并跳过月经规律性问题
      form.setValue('menstrualRegularity', 'yes');
      if (currentStep < totalSteps - 2) {
        setCurrentStep(prev => prev + 2); // 跳过月经规律性步骤
        return;
      }
    }
    
    // 处理其他步骤
    // 对于bodyCircumference步骤特殊处理，因为所有字段都是可选的
    if (currentStepId === 'bodyCircumference') {
      // 身体数据采集阶段的最后一步，进入总结步骤
      if (currentStep < totalSteps - 1) {
        setCurrentStep(prev => prev + 1);
      } else {
        // 当前阶段完成
        // 提交表单
        onSubmit(form.getValues());
      }
      return;
    }
    
    const stepValue = form.getValues(currentStepId as keyof WeightLossFormValues);
    if (stepValue !== undefined) {
      if (currentStep < totalSteps - 1) {
        setCurrentStep(prev => prev + 1);
      } else {
        // 当前阶段完成
        // 提交表单
        onSubmit(form.getValues());
      }
    } else {
      // 显示必填项提示
      form.trigger(currentStepId as any);
    }
  }
  
  // 处理上一步
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    } else {
      // 当前是各阶段的第一步，需要跨阶段返回
      if (currentPhase === COLLECTION_PHASES.FITNESS_GOALS) {
        // 从健身目标采集阶段返回到身体数据采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.BODY_DATA);
        setCurrentStep(bodyDataStepsWithSummary.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        // 从饮食偏好采集阶段返回到健身目标采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.FITNESS_GOALS);
        setCurrentStep(fitnessGoalsSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        // 从健康状况采集阶段返回到饮食偏好采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.DIET_PREFERENCES);
        setCurrentStep(dietPreferencesSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        // 从中医体质相关症状采集阶段返回到健康状况采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.HEALTH_STATUS);
        setCurrentStep(healthStatusSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.BODY_DATA && onBack) {
        // 从身体数据采集阶段返回到群体选择组件
        onBack()
      }
    }
  }

  // 生成健康评估报告
  const generateHealthReport = async () => {
    setIsGeneratingReport(true)

    try {
      const formData = form.getValues()
      const userDataSummary = generateUserDataSummary(formData)

      // 获取选择的群体信息
      const selectedGroup = typeof window !== 'undefined' ? localStorage.getItem('selected_audience_group') : null

      const response = await fetch('/api/generate-health-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userData: userDataSummary,
          formData: {
            ...formData,
            selectedGroup: selectedGroup
          }
        }),
      })

      if (!response.ok) {
        throw new Error('生成报告失败，请稍后重试')
      }

      const data = await response.json()

      // 健康评估完成后，标记用户已完成信息填写，避免再次弹出对话框
      if (typeof window !== 'undefined') {
        localStorage.setItem('profile_completed', 'true')
      }

      // 重定向到主页，添加时间戳参数强制刷新
      router.push(`/?refresh=${Date.now()}`)

    } catch (error) {
      console.error('生成健康报告时出错:', error)
      // 这里可以添加错误处理，比如显示错误消息
    } finally {
      setIsGeneratingReport(false)
    }
  }

  // 提交表单
  function onSubmit(data: WeightLossFormValues) {
    console.log('表单数据:', data)
    // 生成健康评估报告
    generateHealthReport()
  }

  // 如果正在生成报告，显示加载状态
  if (isGeneratingReport) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <Card className="w-full max-w-md">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              正在生成您的健康评估报告
            </h3>
            <p className="text-gray-600 mb-4">
              我们的健身专家正在根据您的信息制定个性化健身方案...
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-gradient-to-r from-blue-400 to-purple-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
            <p className="text-sm text-gray-500 mt-2">预计需要 30-60 秒</p>
          </div>
        </Card>
      </div>
    )
  }

  // 渲染当前步骤
  const renderCurrentStep = () => {
    const currentStepConfig = currentPhaseSteps[currentStep]

    // 如果是引导步骤，渲染引导内容
    if (currentStepConfig.type === 'intro') {
      // 根据当前阶段显示不同的引导内容
      if (currentPhase === COLLECTION_PHASES.BODY_DATA) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-blue-100 to-green-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-green-500 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-blue-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">💪</span>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-gray-800">
                  身体数据采集
                </h3>
                <p className="text-lg text-gray-600 max-w-md mx-auto leading-relaxed">
                  为了给您制定最适合的健身计划，我们需要了解您的基本身体数据。
                </p>
                <p className="text-base text-gray-500 max-w-lg mx-auto">
                  包括身高、体重、体脂率、肌肉量等关键指标，这些数据将帮助我们更精准地评估您的身体状况。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.FITNESS_GOALS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-yellow-100 to-orange-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-red-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-red-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🎯</span>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-gray-800">
                  健身目标设定
                </h3>
                <p className="text-lg text-gray-600 max-w-md mx-auto leading-relaxed">
                  明确的健身目标是成功的关键，让我们一起制定您的专属健身计划。
                </p>
                <p className="text-base text-gray-500 max-w-lg mx-auto">
                  无论是增肌、塑形还是提升运动表现，我们都会为您量身定制最合适的训练方案。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-green-400 to-blue-500 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-green-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🍽️</span>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-gray-800">
                  饮食偏好了解
                </h3>
                <p className="text-lg text-gray-600 max-w-md mx-auto leading-relaxed">
                  合理的营养搭配是健身成功的重要基础。
                </p>
                <p className="text-base text-gray-500 max-w-lg mx-auto">
                  接下来我们将了解您的饮食偏好和习惯，为您定制专属的健身营养计划。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-red-100 to-pink-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-red-400 to-pink-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-pink-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🏥</span>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-gray-800">
                  健康状况了解
                </h3>
                <p className="text-lg text-gray-600 max-w-md mx-auto leading-relaxed">
                  了解您的健康状况有助于我们制定更安全、更有效的健身计划。
                </p>
                <p className="text-base text-gray-500 max-w-lg mx-auto">
                  包括过敏史、慢性疾病、用药情况等，确保您的健身过程安全无忧。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-purple-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🌿</span>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-gray-800">
                  中医体质相关症状了解
                </h3>
                <p className="text-lg text-gray-600 max-w-md mx-auto leading-relaxed">
                  结合中医理论，了解您的体质特点。
                </p>
                <p className="text-base text-gray-500 max-w-lg mx-auto">
                  通过中医体质分析，为您提供更全面的健康评估和个性化的调理建议。
                </p>
              </div>
            </div>
          </div>
        );
      }
    }

    // 如果是总结步骤，渲染总结内容
    if (currentStepConfig.type === 'summary') {
      const userSummary = generateUserDataSummaryObject(form.getValues());

      // 根据当前阶段显示不同的总结内容
      if (currentPhase === COLLECTION_PHASES.BODY_DATA) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">📊</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">您的身体数据总结</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-blue-500">👤</span>
                    <span className="font-medium text-gray-700">基本信息</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>性别：{userSummary.gender}</p>
                    <p>年龄：{userSummary.age}岁</p>
                    <p>身高：{userSummary.height}</p>
                    <p>体重：{userSummary.weight}</p>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-green-500">💪</span>
                    <span className="font-medium text-gray-700">身体成分</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>体脂率：{userSummary.bodyFat}</p>
                    <p>肌肉量：{userSummary.muscleMass}</p>
                    <p>静息心率：{userSummary.restingHeartRate}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-100 rounded-lg">
                <p className="text-sm text-blue-800">
                  基于您提供的身体数据，我们将为您制定个性化的健身计划。接下来让我们了解您的健身目标。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.FITNESS_GOALS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-red-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🎯</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">您的健身目标总结</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-orange-500">🎯</span>
                    <span className="font-medium text-gray-700">健身目标</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>目标类型：{userSummary.fitnessGoalType}</p>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-red-500">🏋️</span>
                    <span className="font-medium text-gray-700">运动习惯</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>运动频率：{userSummary.weeklyWorkoutFrequency}</p>
                    <p>运动时长：{userSummary.workoutDuration}</p>
                    <p>运动强度：{userSummary.workoutIntensity}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-orange-100 rounded-lg">
                <p className="text-sm text-orange-800">
                  基于您的健身目标，我们将为您制定科学的训练计划。接下来让我们了解您的饮食偏好。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-blue-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🍽️</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">您的饮食偏好总结</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-green-500">🌿</span>
                    <span className="font-medium text-gray-700">药膳偏好</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>药膳计划：{userSummary.medicinalDietPlan}</p>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-blue-500">💪</span>
                    <span className="font-medium text-gray-700">健身饮食</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>高蛋白接受度：{userSummary.highProteinFoodPreference}</p>
                    <p>补剂使用：{userSummary.supplementUsage}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-green-100 rounded-lg">
                <p className="text-sm text-green-800">
                  基于您的饮食偏好，我们将为您定制个性化的营养计划。接下来让我们了解您的健康状况。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-red-400 to-pink-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🏥</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">您的健康状况总结</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-red-500">⚠️</span>
                    <span className="font-medium text-gray-700">过敏与疾病</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>食物过敏：{userSummary.foodAllergies}</p>
                    <p>慢性疾病：{userSummary.chronicDiseases}</p>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-pink-500">💊</span>
                    <span className="font-medium text-gray-700">用药与睡眠</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>用药情况：{userSummary.medications}</p>
                    <p>睡眠质量：{userSummary.sleepQuality}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-red-100 rounded-lg">
                <p className="text-sm text-red-800">
                  基于您的健康状况，我们将确保为您制定安全的健身计划。接下来让我们了解您的中医体质相关症状。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🌿</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">您的中医体质相关症状总结</h3>

              <div className="bg-white rounded-lg p-6 shadow-sm text-left max-w-2xl mx-auto">
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-purple-500">🌿</span>
                  <span className="font-medium text-gray-700">体质特征分析</span>
                </div>
                <div className="text-sm text-gray-600 space-y-2">
                  <p>我们已收集了您的中医体质相关症状信息，包括身体沉重感、温度敏感性、皮肤状况、出汗情况、腰痛状况、食欲情况、头发状况等。</p>
                  <p>这些信息将帮助我们从中医角度分析您的体质特点，为您提供更全面的健康评估和个性化的调理建议。</p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-purple-100 rounded-lg">
                <p className="text-sm text-purple-800">
                  信息采集已完成！我们将综合您的所有信息，为您生成专属的健身计划和健康建议。
                </p>
              </div>
            </div>
          </div>
        );
      }
    }

    // 月经规律性问题只对女性显示
    if (currentStepConfig.id === 'menstrualRegularity' && form.getValues('gender') !== 'female') {
      return (
        <div className="text-center text-gray-500 italic">
          <p>此问题仅适用于女性用户，系统已自动跳过。</p>
        </div>
      );
    }
    
    // 健身目标类型选择
    if (currentStepConfig.id === 'fitnessGoalType') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedFitnessGoalType === option.value) || form.getValues('fitnessGoalType') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('fitnessGoalType', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedFitnessGoalType(option.value);
                  
                  // 选择后自动前进到下一步
                  setTimeout(() => {
                    handleNext();
                  }, 300);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 增肌目标输入
    if (currentStepConfig.id === 'muscleGainGoal') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="muscleGainGoal"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        placeholder={currentStepConfig.placeholder}
                        value={inputValues.muscleGainGoal}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setInputValues(prev => ({
                            ...prev,
                            muscleGainGoal: newValue
                          }));
                          form.setValue('muscleGainGoal', newValue, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleNext();
                          }
                        }}
                        className="flex-1"
                      />
                      <div className="flex gap-2">
                        {currentStepConfig.units?.map((unit) => (
                          <Button
                            key={unit.value}
                            type="button"
                            variant={form.getValues('muscleGainUnit') === unit.value ? "default" : "outline"}
                            onClick={() => {
                              form.setValue('muscleGainUnit', unit.value as any, {
                                shouldValidate: true,
                                shouldDirty: true,
                                shouldTouch: true
                              });
                            }}
                          >
                            {unit.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  {currentStepConfig.description}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )
    }
    
    // 塑形部位选择
    if (currentStepConfig.id === 'bodyShapingAreas') {
      return (
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            {currentStepConfig.options?.map((option) => {
              const isSelected = selectedBodyShapingAreas.includes(option.value) || 
                                 (form.getValues('bodyShapingAreas')?.includes(option.value));
              
              return (
                <Card
                  key={option.value}
                  className={cn(
                    "p-4 cursor-pointer transition-all hover:shadow-md",
                    isSelected
                      ? "ring-2 ring-blue-500 shadow-md"
                      : "hover:border-blue-200"
                  )}
                  onClick={() => {
                    let currentSelections = [...selectedBodyShapingAreas];
                    
                    if (isSelected) {
                      // 如果已选中，则移除
                      currentSelections = currentSelections.filter(item => item !== option.value);
          } else {
                      // 如果未选中，则添加
                      currentSelections.push(option.value);
                    }
                    
                    setSelectedBodyShapingAreas(currentSelections);
                    form.setValue('bodyShapingAreas', currentSelections, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true
                    });
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div className="text-xl">{option.icon}</div>
                    <span>{option.label}</span>
                  </div>
                </Card>
              );
            })}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
          
          <FormMessage />
        </div>
      )
    }
    
    // 运动项目选择
    if (currentStepConfig.id === 'sportType') {
      return (
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedSportType === option.value) || form.getValues('sportType') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('sportType', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedSportType(option.value);
                }}
              >
                <div className="flex items-center gap-2">
                  <div className="text-xl">{option.icon}</div>
                  <span className="text-sm">{option.label}</span>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 运动表现方面选择
    if (currentStepConfig.id === 'performanceAspects') {
      return (
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            {currentStepConfig.options?.map((option) => {
              const isSelected = selectedPerformanceAspects.includes(option.value) || 
                                 (form.getValues('performanceAspects')?.includes(option.value));
              
              return (
                <Card
                  key={option.value}
                  className={cn(
                    "p-4 cursor-pointer transition-all hover:shadow-md",
                    isSelected
                      ? "ring-2 ring-blue-500 shadow-md"
                      : "hover:border-blue-200"
                  )}
                  onClick={() => {
                    let currentSelections = [...selectedPerformanceAspects];
                    
                    if (isSelected) {
                      // 如果已选中，则移除
                      currentSelections = currentSelections.filter(item => item !== option.value);
          } else {
                      // 如果未选中，则添加
                      currentSelections.push(option.value);
                    }
                    
                    setSelectedPerformanceAspects(currentSelections);
                    form.setValue('performanceAspects', currentSelections, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true
                    });
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div className="text-xl">{option.icon}</div>
                    <span>{option.label}</span>
                  </div>
                </Card>
              );
            })}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
          
          <FormMessage />
        </div>
      )
    }
    
    // 每周运动次数选择
    if (currentStepConfig.id === 'weeklyWorkoutFrequency') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedWeeklyFrequency === option.value) || form.getValues('weeklyWorkoutFrequency') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('weeklyWorkoutFrequency', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedWeeklyFrequency(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 每次运动时长选择
    if (currentStepConfig.id === 'workoutDuration') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedWorkoutDuration === option.value) || form.getValues('workoutDuration') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('workoutDuration', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedWorkoutDuration(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 运动强度选择
    if (currentStepConfig.id === 'workoutIntensity') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedWorkoutIntensity === option.value) || form.getValues('workoutIntensity') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('workoutIntensity', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedWorkoutIntensity(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 喜欢的食材选择（多选）
    if (currentStepConfig.id === 'favoriteFoods') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="favoriteFoods"
            render={({ field }) => (
              <FormItem>
                <div className="grid grid-cols-2 gap-3">
                  {currentStepConfig.options?.map((option) => {
                    const isSelected = field.value?.includes(option.value);
                    
                    return (
                      <Card
                        key={option.value}
                        className={cn(
                          "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                          isSelected
                            ? "ring-2 ring-blue-500 shadow-md"
                            : "hover:border-blue-200"
                        )}
                        onClick={() => {
                          const currentValues = Array.isArray(field.value) ? [...field.value] : [];
                          let newValues: string[];
                          
                          if (isSelected) {
                            // 取消选择
                            newValues = currentValues.filter(val => val !== option.value);
          } else {
                            // 添加选择
                            newValues = [...currentValues, option.value];
                          }
                          
                          // 更新表单值
                          form.setValue('favoriteFoods', newValues, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          
                          setSelectedFavoriteFoods(newValues);
                        }}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div className="text-xl">{option.icon}</div>
                          <span className="text-sm">{option.label}</span>
                        </div>
                      </Card>
                    );
                  })}
                </div>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="mt-3">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 不喜欢的食材选择（多选）
    if (currentStepConfig.id === 'dislikedFoods') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="dislikedFoods"
            render={({ field }) => (
              <FormItem>
                <div className="grid grid-cols-2 gap-3">
                  {currentStepConfig.options?.map((option) => {
                    const isSelected = field.value?.includes(option.value);
                    
                    return (
                      <Card
                        key={option.value}
                        className={cn(
                          "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                          isSelected
                            ? "ring-2 ring-blue-500 shadow-md"
                            : "hover:border-blue-200"
                        )}
                        onClick={() => {
                          const currentValues = Array.isArray(field.value) ? [...field.value] : [];
                          let newValues: string[] = [];
                          
                          if (option.value === 'none') {
                            // 如果选择"没有特别不喜欢的食材"，清除其他选项
                            newValues = isSelected ? [] : ['none'];
                          } else {
                            // 如果选择其他选项，移除"没有特别不喜欢的食材"
                            newValues = isSelected
                              ? currentValues.filter(val => val !== option.value)
                              : [...currentValues.filter(val => val !== 'none'), option.value];
                          }
                          
                          // 更新表单值
                          form.setValue('dislikedFoods', newValues, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          
                          setSelectedDislikedFoods(newValues);
                        }}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div className="text-xl">{option.icon}</div>
                          <span className="text-sm">{option.label}</span>
                        </div>
                      </Card>
                    );
                  })}
                </div>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="mt-3">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
        </div>
      )
    }
    
    // 宗教饮食限制选择
    if (currentStepConfig.id === 'religiousDietaryRestrictions') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedReligiousDietaryRestriction === option.value) || form.getValues('religiousDietaryRestrictions') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('religiousDietaryRestrictions', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedReligiousDietaryRestriction(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 个人忌口选择（多选）
    if (currentStepConfig.id === 'personalDietaryRestrictions') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="personalDietaryRestrictions"
            render={({ field }) => (
              <FormItem>
                <div className="grid grid-cols-2 gap-3">
                  {currentStepConfig.options?.map((option) => {
                    const isSelected = field.value?.includes(option.value);
                    
                    return (
                      <Card
                        key={option.value}
                        className={cn(
                          "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                          isSelected
                            ? "ring-2 ring-blue-500 shadow-md"
                            : "hover:border-blue-200"
                        )}
                        onClick={() => {
                          const currentValues = Array.isArray(field.value) ? [...field.value] : [];
                          let newValues: string[] = [];
                          
                          if (option.value === 'none') {
                            // 如果选择"无特殊忌口"，清除其他选项
                            newValues = isSelected ? [] : ['none'];
                          } else {
                            // 如果选择其他选项，移除"无特殊忌口"
                            newValues = isSelected
                              ? currentValues.filter(val => val !== option.value)
                              : [...currentValues.filter(val => val !== 'none'), option.value];
                          }
                          
                          // 更新表单值
                          form.setValue('personalDietaryRestrictions', newValues, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          
                          setSelectedPersonalDietaryRestrictions(newValues);
                        }}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div className="text-xl">{option.icon}</div>
                          <span className="text-sm">{option.label}</span>
                        </div>
                      </Card>
                    );
                  })}
                </div>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="mt-3">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 高蛋白食材偏好选择
    if (currentStepConfig.id === 'highProteinFoodPreference') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedHighProteinPreference === option.value) || form.getValues('highProteinFoodPreference') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('highProteinFoodPreference', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedHighProteinPreference(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
        </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 补剂使用习惯选择
    if (currentStepConfig.id === 'supplementUsage') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedSupplementUsage === option.value) || form.getValues('supplementUsage') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('supplementUsage', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedSupplementUsage(option.value);
                  
                  // 如果选择不使用补剂，自动前进到下一步
                  if (option.value === 'no') {
                    setTimeout(() => {
                      handleNext();
                    }, 300);
                  }
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
        </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 补剂类型选择
    if (currentStepConfig.id === 'supplementTypes') {
      return (
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            {currentStepConfig.options?.map((option) => {
              const isSelected = selectedSupplementTypes.includes(option.value) || 
                                 (form.getValues('supplementTypes')?.includes(option.value));
              
              return (
                <Card
                  key={option.value}
                  className={cn(
                    "p-4 cursor-pointer transition-all hover:shadow-md",
                    isSelected
                      ? "ring-2 ring-blue-500 shadow-md"
                      : "hover:border-blue-200"
                  )}
                  onClick={() => {
                    let currentSelections = [...selectedSupplementTypes];
                    
                    if (isSelected) {
                      // 如果已选中，则移除
                      currentSelections = currentSelections.filter(item => item !== option.value);
                    } else {
                      // 如果未选中，则添加
                      currentSelections.push(option.value);
                    }
                    
                    setSelectedSupplementTypes(currentSelections);
                    form.setValue('supplementTypes', currentSelections, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true
                    });
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div className="text-xl">{option.icon}</div>
                    <span>{option.label}</span>
                  </div>
                </Card>
              );
            })}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 运动饮食偏好选择
    if (currentStepConfig.id === 'workoutMealPreference') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedWorkoutMealPreference === option.value) || form.getValues('workoutMealPreference') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('workoutMealPreference', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedWorkoutMealPreference(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 运动前加餐习惯选择
    if (currentStepConfig.id === 'preworkoutMeal') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedPreworkoutMeal === option.value) || form.getValues('preworkoutMeal') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('preworkoutMeal', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedPreworkoutMeal(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 运动后加餐习惯选择
    if (currentStepConfig.id === 'postworkoutMeal') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedPostworkoutMeal === option.value) || form.getValues('postworkoutMeal') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('postworkoutMeal', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedPostworkoutMeal(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 运动日与非运动日饮食差异选择
    if (currentStepConfig.id === 'workoutDayDietDifference') {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (selectedDietDifference === option.value) || form.getValues('workoutDayDietDifference') === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue('workoutDayDietDifference', option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setSelectedDietDifference(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    // 出生日期选择
    if (currentStepConfig.type === 'birthDate') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col items-center">
                    <DatePicker
                      value={field.value}
                      onChange={(date) => {
                        field.onChange(date);
                      }}
                      minYear={1940}
                      maxYear={new Date().getFullYear()}
                    />
                    <div className="mt-4 text-center text-gray-500">
                      <p>您的年龄: {new Date().getFullYear() - field.value.getFullYear()} 岁</p>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="text-center mt-4">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 体重选择
    if (currentStepConfig.id === 'weight') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="weight"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col items-center">
                    <WeightGauge
                      value={parseFloat(inputValues.weight) || 65}
                      onChange={(value) => {
                        const newValue = value.toString();
                        // 更新本地状态
                        setInputValues(prev => ({
                          ...prev,
                          weight: newValue
                        }));
                        
                        // 同时更新表单值
                        field.onChange(newValue);
                        
                        // 重新计算BMI
                        if (inputValues.height) {
                          calculateBMI(inputValues.height, form.getValues('heightUnit'), newValue, form.getValues('weightUnit'));
                        }
                      }}
                      minWeight={form.getValues('weightUnit') === 'kg' ? 30 : 66}
                      maxWeight={form.getValues('weightUnit') === 'kg' ? 200 : 440}
                      label={form.getValues('weightUnit') === 'kg' ? '千克' : '磅'}
                      rangeFormat={`范围: ${form.getValues('weightUnit') === 'kg' ? '30 - 200' : '66 - 440'} ${form.getValues('weightUnit')}`}
                    />
                    
                    {/* 单位选择 */}
                    <div className="flex gap-2 mt-6">
                      {currentStepConfig.units?.map((unit) => (
                        <Button
                          key={unit.value}
                          type="button"
                          variant={form.getValues('weightUnit') === unit.value ? "default" : "outline"}
                          onClick={() => {
                            const currentUnit = form.getValues('weightUnit');
                            const newUnit = unit.value;
                            const currentValue = inputValues.weight;
                            
                            // 只有当单位发生变化且有当前值时才进行转换
                            if (currentUnit !== newUnit && currentValue) {
                              let newValue = '';
                              
                                                          // 体重单位转换
                            if (currentUnit === 'kg' && newUnit === 'lb') {
                              // 千克转磅 (1千克 ≈ 2.2046磅)
                              const lbValue = parseFloat(currentValue) * 2.2046;
                              // 直接使用转换值，不强制最小值
                              newValue = lbValue.toFixed(1);
                            } else if (currentUnit === 'lb' && newUnit === 'kg') {
                              // 磅转千克 (1磅 ≈ 0.4536千克)
                              const kgValue = parseFloat(currentValue) * 0.4536;
                              // 确保转换后的值在新的范围内
                              const minKg = 30;
                              const maxKg = 200;
                              const adjustedValue = Math.max(minKg, Math.min(maxKg, kgValue));
                              newValue = adjustedValue.toFixed(1);
                              }
                              
                              if (newValue) {
                                                              // 更新本地状态
                              setInputValues(prev => ({
                                ...prev,
                                weight: newValue
                              }));
                              
                              // 更新表单值
                              form.setValue('weight', newValue);
                              
                              // 强制刷新组件以更新仪表盘
                              setTimeout(() => {
                                form.trigger('weight');
                              }, 0);
                            }
                          }
                          
                          // 更新单位
                          form.setValue('weightUnit', unit.value as any);
                          
                          // 如果有身高值，立即重新计算BMI
                          if (inputValues.height) {
                            calculateBMI(inputValues.height, form.getValues('heightUnit'), inputValues.weight, unit.value);
                          }
                          }}
                          className="min-w-[100px]"
                        >
                          {unit.label}
                        </Button>
                      ))}
                    </div>
                    
                    {/* BMI显示 */}
                    {calculatedBMI !== null && (
                      <div className="bg-muted p-4 rounded-md mt-6 w-full max-w-md">
                        <h4 className="font-medium mb-1">您的BMI指数</h4>
                        <div className="flex items-center justify-between">
                          <p className="text-2xl font-bold">{calculatedBMI}</p>
                          <p className={`text-sm font-medium ${
                            bmiCategory === '正常范围' ? 'text-green-600' : 
                            bmiCategory === '体重过轻' ? 'text-yellow-600' : 
                            'text-red-600'
                          }`}>
                            {bmiCategory}
                          </p>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          BMI是体重指数(Body Mass Index)的缩写，根据身高和体重来计算
                        </p>
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 时间范围选择
    if (currentStepConfig.type === 'timeframe') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name={currentStepConfig.id as any}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <div className="grid grid-cols-4 gap-2">
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                      <Button
                        key={month}
                        type="button"
                        variant={inputValues.goalTimeframe === month.toString() ? "default" : "outline"}
                        onClick={() => {
                          setInputValues(prev => ({
                            ...prev,
                            goalTimeframe: month.toString()
                          }));
                          form.setValue(currentStepConfig.id as any, month.toString());
                        }}
                        className="h-12"
                      >
                        {month} 个月
                      </Button>
                    ))}
                  </div>
                </FormControl>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription>{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
          
          {/* 健康目标时间建议已移除 */}
        </div>
      )
    }
    
    // 多选功能
    if (currentStepConfig.type === 'multiSelect') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name={currentStepConfig.id as any}
            render={({ field }) => (
              <FormItem>
                <div className="grid grid-cols-2 gap-3">
                  {currentStepConfig.options?.map((option) => {
                    // 使用表单值确定是否选中
                    const isSelected = field.value?.includes(option.value);
                    
                    return (
                      <Card
                        key={option.value}
                        className={cn(
                          "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                          isSelected
                            ? "ring-2 ring-blue-500 shadow-md"
                            : "hover:border-blue-200"
                        )}
                        onClick={() => {
                          const currentValues = Array.isArray(field.value) ? [...field.value] : [];
                          let newValues: string[] = [];
                          
                          if (option.value === 'none') {
                            // 如果选择"没有明显反应"，清除其他选项
                            newValues = isSelected ? [] : ['none'];
                          } else {
                            // 如果选择其他选项，移除"没有明显反应"
                            newValues = isSelected
                              ? currentValues.filter(val => val !== option.value)
                              : [...currentValues.filter(val => val !== 'none'), option.value];
                          }
                          
                          // 更新表单值
                          form.setValue(currentStepConfig.id as any, newValues, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          

                        }}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div className="text-xl">{option.icon}</div>
                          <span className="text-sm">{option.label}</span>
                        </div>
                      </Card>
                    );
                  })}
                </div>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="mt-3">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
          

            </div>
      );
    }
    
    // 静息心率输入
    if (currentStepConfig.id === 'restingHeartRate') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="restingHeartRate"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex items-center justify-center">
                    <div className="relative w-40 h-40">
                      <Input 
                        type="number" 
                        placeholder="60-100"
                        className="absolute inset-0 w-full h-full rounded-full text-center text-3xl font-bold border-4 border-red-400 focus:border-red-500"
                        value={inputValues.restingHeartRate}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setInputValues(prev => ({
                            ...prev,
                            restingHeartRate: newValue
                          }));
                          form.setValue('restingHeartRate', newValue, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          
                          if (form.formState.errors.restingHeartRate) {
                            form.clearErrors('restingHeartRate');
                          }
                        }}
                      />
                      <div className="absolute bottom-2 left-0 right-0 text-center text-xs text-gray-500">
                        次/分钟
            </div>
                    </div>
                  </div>
                </FormControl>
                <FormDescription className="text-center mt-6">
                  {currentStepConfig.description}
                </FormDescription>
                <FormMessage className="text-center" />
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    if (currentStepConfig.type === 'input') {
      return (
        <FormField
          control={form.control}
          name={currentStepConfig.id as any}
          render={({ field }) => (
            <FormItem className="w-full">
              <FormControl>
                <div className="flex gap-2">
                  <Input 
                    type="number" 
                    placeholder={currentStepConfig.placeholder}
                    className="text-lg p-6"
                    value={inputValues[currentStepConfig.id as keyof typeof inputValues] || ''}
                    onChange={(e) => {
                      // 更新本地状态
                      setInputValues(prev => ({
                        ...prev,
                        [currentStepConfig.id]: e.target.value
                      }));
                      
                      // 同时更新表单值
                      form.setValue(currentStepConfig.id as any, e.target.value);
                    }}
                    onBlur={field.onBlur}
                    name={field.name}
                    ref={field.ref}
                  />
                  {currentStepConfig.units && (
                    <div className="flex gap-2">
                      {currentStepConfig.units.map((unit) => (
                        <Button
                          key={unit.value}
                          type="button"
                          variant={
                            form.getValues(`${currentStepConfig.id}Unit` as any) === unit.value 
                              ? "default" 
                              : "outline"
                          }
                          onClick={() => {
                            const currentUnit = form.getValues(`${currentStepConfig.id}Unit` as any);
                            const newUnit = unit.value;
                            const currentValue = inputValues[currentStepConfig.id as keyof typeof inputValues];
                            
                            // 减重目标单位处理已移除
                            
                            // 只有当单位发生变化且有当前值时才进行转换
                            if (currentUnit !== newUnit && currentValue) {
                              let newValue = '';
                              
                              // 身高单位转换
                              if (currentStepConfig.id === 'height') {
                                if (currentUnit === 'cm' && newUnit === 'inch') {
                                  // 厘米转英寸 (1厘米 ≈ 0.3937英寸)
                                  newValue = (parseFloat(currentValue) * 0.3937).toFixed(1);
                                } else if (currentUnit === 'inch' && newUnit === 'cm') {
                                  // 英寸转厘米 (1英寸 ≈ 2.54厘米)
                                  newValue = (parseFloat(currentValue) * 2.54).toFixed(1);
                                }
                              }
                              // 体重单位转换
                              else if (currentStepConfig.id === 'weight') {
                                if (currentUnit === 'kg' && newUnit === 'lb') {
                                  // 千克转磅 (1千克 ≈ 2.2046磅)
                                  newValue = (parseFloat(currentValue) * 2.2046).toFixed(1);
                                } else if (currentUnit === 'lb' && newUnit === 'kg') {
                                  // 磅转千克 (1磅 ≈ 0.4536千克)
                                  newValue = (parseFloat(currentValue) * 0.4536).toFixed(1);
                                } else if ((currentUnit === 'kg' || currentUnit === 'lb') && newUnit === 'bodyFat') {
                                  // 切换到体脂率时，设置默认值为5%
                                  newValue = '5';
                                } else if (newUnit === 'kg' && currentUnit === 'bodyFat') {
                                  // 从体脂率切换到千克，设置默认值为5千克
                                  newValue = '5';
                                } else if (newUnit === 'lb' && currentUnit === 'bodyFat') {
                                  // 从体脂率切换到磅，设置默认值为10磅
                                  newValue = '11';
                                }
                              }
                              
                              if (newValue) {
                                // 更新本地状态
                                setInputValues(prev => ({
                                  ...prev,
                                  [currentStepConfig.id]: newValue
                                }));
                                
                                // 更新表单值
                                form.setValue(currentStepConfig.id as any, newValue, {
                                  shouldValidate: true,
                                  shouldDirty: true,
                                  shouldTouch: true
                                });
                              }
                            }
                            
                            // 更新单位
                            form.setValue(`${currentStepConfig.id}Unit` as any, unit.value as any, {
                              shouldValidate: true,
                              shouldDirty: true,
                              shouldTouch: true
                            });
                            
                            // 如果是身高或体重单位改变，立即重新计算BMI
                            if ((currentStepConfig.id === 'height' || currentStepConfig.id === 'weight') && 
                                inputValues.height && inputValues.weight) {
                              const heightUnit = currentStepConfig.id === 'height' ? newUnit : form.getValues('heightUnit');
                              const weightUnit = currentStepConfig.id === 'weight' ? newUnit : form.getValues('weightUnit');
                              calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
                            }
                          }}
                        >
                          {unit.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )
    }
    
    // 渲染体脂率输入（当用户选择"知道体脂率"时）
    if (currentStepConfig.id === 'knowsBodyFat' && (knowsBodyFatSelection === 'yes' || form.getValues('knowsBodyFat') === 'yes')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsBodyFatSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                                  onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsBodyFatSelection(option.value);
                  
                  // 因为知道体脂率选择"是"后不需要立即前进，所以这里不添加自动跳转
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormField
            control={form.control}
            name="bodyFatPercentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>体脂率 (%)</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入您的体脂率" 
                    value={inputValues.bodyFatPercentage}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      setInputValues(prev => ({
                        ...prev,
                        bodyFatPercentage: newValue
                      }));
                      // 使用正确的参数设置表单值，确保触发验证
                      form.setValue('bodyFatPercentage', newValue, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true
                      });
                      
                      // 如果有错误，清除错误
                      if (form.formState.errors.bodyFatPercentage) {
                        form.clearErrors('bodyFatPercentage');
                      }
                    }}
                    onKeyDown={(e) => {
                      // 当用户按下回车键时，如果输入有效，自动进入下一步
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const value = inputValues.bodyFatPercentage;
                        if (value && parseFloat(value) > 0 && parseFloat(value) <= 100) {
                          handleNext();
                        }
                      }
                    }}
                    onBlur={(e) => {
                      field.onBlur();
                      // 在失去焦点时也验证一次
                      const value = e.target.value;
                      if (!value || value.trim() === '') {
                        form.setError('bodyFatPercentage', {
                          type: 'manual',
                          message: '请输入您的体脂率'
                        });
                      } else {
                        const numericValue = parseFloat(value);
                        if (isNaN(numericValue) || numericValue <= 0 || numericValue > 100) {
                          form.setError('bodyFatPercentage', {
                            type: 'manual',
                            message: '请输入有效的体脂率数值（1-100之间）'
                          });
                        } else {
                          form.clearErrors('bodyFatPercentage');
                        }
                      }
                    }}
                    name={field.name}
                    ref={field.ref}
                  />
                </FormControl>
                <FormDescription>
                  体脂率是指体内脂肪重量在总体重中所占的百分比
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )
    }
    
    // 为"不知道体脂率"选项提供链接
    if (currentStepConfig.id === 'knowsBodyFat' && (knowsBodyFatSelection === 'no' || form.getValues('knowsBodyFat') === 'no')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsBodyFatSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsBodyFatSelection(option.value);
                  
                  // 为了更好的用户体验，选择后自动前进到下一步
                  setTimeout(() => {
                    handleNext();
                  }, 300);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <div className="text-sm text-blue-600">
            <Link href="#" className="underline">
              了解如何测量体脂率
            </Link>
          </div>
        </div>
      )
    }
    
    // 肌肉量输入（当用户选择"知道肌肉量"时）
    if (currentStepConfig.id === 'knowsMuscleMass' && (knowsMuscleMassSelection === 'yes' || form.getValues('knowsMuscleMass') === 'yes')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsMuscleMassSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsMuscleMassSelection(option.value);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormField
            control={form.control}
            name="muscleMass"
            render={({ field }) => (
              <FormItem>
                <FormLabel>肌肉量</FormLabel>
                <FormControl>
                  <div className="flex gap-2">
                    <Input 
                      type="number" 
                      placeholder="请输入您的肌肉量" 
                      value={inputValues.muscleMass}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setInputValues(prev => ({
                          ...prev,
                          muscleMass: newValue
                        }));
                        form.setValue('muscleMass', newValue, {
                          shouldValidate: true,
                          shouldDirty: true,
                          shouldTouch: true
                        });
                        
                        if (form.formState.errors.muscleMass) {
                          form.clearErrors('muscleMass');
                        }
                      }}
                      name={field.name}
                      ref={field.ref}
                    />
                    <div className="flex gap-2">
                      {['kg', 'lb'].map((unit) => (
                        <Button
                          key={unit}
                          type="button"
                          variant={form.getValues('muscleMassUnit') === unit ? "default" : "outline"}
                          onClick={() => {
                            form.setValue('muscleMassUnit', unit as any, {
                              shouldValidate: true,
                              shouldDirty: true,
                              shouldTouch: true
                            });
                          }}
                        >
                          {unit === 'kg' ? '千克 (kg)' : '磅 (lb)'}
                        </Button>
                      ))}
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  肌肉量是指身体中肌肉组织的总重量
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
         
        </div>
      )
    }
    
    // 为"不知道肌肉量"选项提供链接
    if (currentStepConfig.id === 'knowsMuscleMass' && (knowsMuscleMassSelection === 'no' || form.getValues('knowsMuscleMass') === 'no')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsMuscleMassSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsMuscleMassSelection(option.value);
                  
                  // 为了更好的用户体验，选择后自动前进到下一步
                  setTimeout(() => {
                    handleNext();
                  }, 300);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <div className="text-sm text-blue-600">
            <Link href="#" className="underline">
              了解如何测量肌肉量
            </Link>
          </div>
        </div>
      )
    }
    
    // 围度测量输入
    if (currentStepConfig.id === 'bodyCircumference') {
      return (
        <div className="space-y-6">
          <div className="flex justify-end mb-4">
            <div className="flex gap-2">
              {currentStepConfig.units?.map((unit) => (
                <Button
                  key={unit.value}
                  type="button"
                  variant={form.getValues('circumferenceUnit') === unit.value ? "default" : "outline"}
                  onClick={() => {
                    form.setValue('circumferenceUnit', unit.value as any, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true
                    });
                  }}
                >
                  {unit.label}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 胸围 */}
            <FormField
              control={form.control}
              name="chestCircumference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>胸围</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="请输入胸围" 
                      value={inputValues.chestCircumference}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setInputValues(prev => ({
                          ...prev,
                          chestCircumference: newValue
                        }));
                        form.setValue('chestCircumference', newValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* 腰围 */}
            <FormField
              control={form.control}
              name="waistCircumference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>腰围</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="请输入腰围" 
                      value={inputValues.waistCircumference}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setInputValues(prev => ({
                          ...prev,
                          waistCircumference: newValue
                        }));
                        form.setValue('waistCircumference', newValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* 臀围 */}
            <FormField
              control={form.control}
              name="hipCircumference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>臀围</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="请输入臀围" 
                      value={inputValues.hipCircumference}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setInputValues(prev => ({
                          ...prev,
                          hipCircumference: newValue
                        }));
                        form.setValue('hipCircumference', newValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* 臂围 */}
            <FormField
              control={form.control}
              name="armCircumference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>臂围</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="请输入臂围" 
                      value={inputValues.armCircumference}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setInputValues(prev => ({
                          ...prev,
                          armCircumference: newValue
                        }));
                        form.setValue('armCircumference', newValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* 大腿围 */}
            <FormField
              control={form.control}
              name="thighCircumference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>大腿围</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="请输入大腿围" 
                      value={inputValues.thighCircumference}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setInputValues(prev => ({
                          ...prev,
                          thighCircumference: newValue
                        }));
                        form.setValue('thighCircumference', newValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* 小腿围 */}
            <FormField
              control={form.control}
              name="calfCircumference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>小腿围</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="请输入小腿围" 
                      value={inputValues.calfCircumference}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setInputValues(prev => ({
                          ...prev,
                          calfCircumference: newValue
                        }));
                        form.setValue('calfCircumference', newValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormDescription className="mt-4">
            {currentStepConfig.description}
          </FormDescription>
        </div>
      )
    }
    
    return (
      <div className="w-full flex flex-col gap-3">
        {currentStepConfig.options?.map((option) => (
          <Card
            key={option.value}
            className={cn(
              "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
              // 对于性别选择，使用本地状态来确定是否选中，否则使用表单值
              (currentStepConfig.id === 'gender' && selectedGender === option.value) || 
              form.getValues(currentStepConfig.id as any) === option.value
                ? "ring-2 ring-blue-500 shadow-md"
                : "hover:border-blue-200"
            )}
            onClick={() => {
              // 更新表单值
              form.setValue(currentStepConfig.id as any, option.value as any, {
                shouldValidate: true, // 立即触发验证
                shouldDirty: true,    // 标记为已修改
                shouldTouch: true     // 标记为已交互
              });
              
              // 添加视觉反馈并触发表单验证
              const selectedValue = form.getValues(currentStepConfig.id as any);
              console.log("选择了:", option.value, "当前值:", selectedValue);
              
              // 如果是性别选择，更新本地状态以确保UI响应
              if (currentStepConfig.id === 'gender') {
                setSelectedGender(option.value);
              }
              
              // 为了解决点击不响应问题，强制更新表单状态
              setTimeout(() => {
                // 如果是性别选择，可以自动前进到下一步
                if (currentStepConfig.id === 'gender') {
                  handleNext();
                }
              }, 200);
            }}
          >
            <div className="flex items-center gap-4 w-full justify-between">
              <div className="flex items-center gap-4">
                <div className="text-2xl">{option.icon}</div>
                <span className="text-base">{option.label}</span>
              </div>
            </div>
          </Card>
        ))}
      </div>
    )
  }
  
  // 计算进度百分比
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100
  
  return (
    <div className="w-full max-w-xl mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <span>健身爱好者信息采集</span>
        </h2>
        <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-md inline-block mt-1 mb-2 text-sm font-medium">
          {currentPhase === COLLECTION_PHASES.BODY_DATA ? '身体数据采集' :
           currentPhase === COLLECTION_PHASES.FITNESS_GOALS ? '健身目标采集' :
           currentPhase === COLLECTION_PHASES.DIET_PREFERENCES ? '饮食偏好采集' :
           currentPhase === COLLECTION_PHASES.HEALTH_STATUS ? '健康状况采集' :
           currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS ? '中医体质相关症状采集' :
           '信息采集完成'}
        </div>
        <p className="text-gray-600 mt-2">
          {currentPhase === COLLECTION_PHASES.BODY_DATA
            ? '请提供以下身体数据，以便我们为您制定个性化的健身营养计划。'
            : currentPhase === COLLECTION_PHASES.FITNESS_GOALS
                ? '请设置您的健身目标，以便我们为您制定科学合理的训练计划。'
            : currentPhase === COLLECTION_PHASES.DIET_PREFERENCES
            ? '请提供您的饮食偏好信息，以便我们为您定制更合适的营养计划。'
            : currentPhase === COLLECTION_PHASES.HEALTH_STATUS
            ? '请提供您的健康状况信息，以便我们为您制定更安全、更有效的训练计划。'
            : currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS
            ? '请提供您的中医体质相关症状信息，以便我们为您提供更全面的健康评估和中医体质分析。'
            : '信息采集已完成，感谢您的配合。'
          }
        </p>
      </div>
      
      {/* 进度指示器 */}
      <div className="mb-12">
        <h3 className="text-base font-medium text-gray-700 mb-2">
          {currentPhase === COLLECTION_PHASES.BODY_DATA ? '身体数据采集进度' :
           currentPhase === COLLECTION_PHASES.FITNESS_GOALS ? '健身目标采集进度' :
           currentPhase === COLLECTION_PHASES.DIET_PREFERENCES ? '饮食偏好采集进度' :
           currentPhase === COLLECTION_PHASES.HEALTH_STATUS ? '健康状况采集进度' :
           currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS ? '中医体质相关症状采集进度' :
           '信息采集进度'}
        </h3>
        <div className="flex justify-between items-center mb-1 text-sm">
          <span>步骤 {currentStep + 1} / {totalSteps}</span>
          <span>{progressPercentage.toFixed(0)}%</span>
        </div>
        <div className="mb-1 text-xs text-gray-500">
          <span>点击下方进度条可快速跳转到任意步骤</span>
        </div>
        <div className="relative w-full h-8 py-3">
          <div className="relative w-full bg-gray-200 h-2 rounded-full overflow-hidden cursor-pointer hover:bg-gray-300 transition-colors"
            title="点击跳转到相应步骤"
            onClick={(e) => {
              // 获取点击位置相对于进度条的百分比
              const rect = e.currentTarget.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const percentPosition = x / rect.width;
              
              // 将百分比位置转换为步骤索引
              const stepIndex = Math.min(
                Math.max(Math.floor(percentPosition * totalSteps), 0), 
                totalSteps - 1
              );
              
              // 允许跳转到任意步骤
              setCurrentStep(stepIndex);
            }}
          >
          <div 
            className="h-full bg-gradient-to-r from-blue-500 to-green-400"
            style={{ width: `${progressPercentage}%` }}
          ></div>
            
            {/* 添加步骤标记 */}
            <div className="absolute top-0 left-0 w-full h-full pointer-events-none flex items-center">
              {/* 显示所有步骤的点 */}
              {Array.from({ length: totalSteps }).map((_, index) => {
                const isCompleted = index < currentStep;
                const isCurrent = index === currentStep;
                // 主要步骤位置
                const isMainStep = [0, 8, 17, 29, 33, totalSteps-1].includes(index);
                
                return (
                  <div 
                    key={index}
                    className={`absolute rounded-full transform -translate-y-1/2 -translate-x-1/2 ${
                      isCurrent 
                        ? 'bg-blue-600 ring-2 ring-blue-300' 
                        : isCompleted 
                          ? 'bg-green-500 hover:ring-1 hover:ring-green-200' 
                          : 'bg-gray-400 hover:ring-1 hover:ring-gray-200'
                    } transition-all`}
                    style={{ 
                      left: `${(index / (totalSteps - 1)) * 100}%`, 
                      top: '50%',
                      width: isMainStep ? '12px' : '8px',
                      height: isMainStep ? '12px' : '8px'
                    }}
                    title={`步骤 ${index + 1}`}
                  ></div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      
      <Form {...form}>
        <form className="space-y-8">
          {/* 问题标题 */}
          <h3 className="text-xl font-medium text-center">
            {currentPhaseSteps[currentStep]?.title || '加载中...'}
          </h3>
          
          {/* 当前步骤内容 */}
          {renderCurrentStep()}
          
          {/* 导航按钮 */}
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              className="flex items-center gap-1"
            >
              <ChevronLeft size={16} /> 上一步
            </Button>
            
            <Button
              type="button"
              onClick={handleNext}
              className="flex items-center gap-1"
            >
              {currentPhaseSteps[currentStep]?.type === 'intro'
                ? '开始'
                : currentStep === totalSteps - 1 && currentPhaseSteps[currentStep]?.type === 'summary' && currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS
                  ? '开始评估'
                  : currentStep === totalSteps - 1 && currentPhaseSteps[currentStep]?.type === 'summary'
                    ? '继续'
                    : currentStep === totalSteps - 1
                      ? '完成'
                      : '下一步'} <ChevronRight size={16} />
            </Button>
          </div>

          {currentPhaseSteps[currentStep]?.type === 'intro' && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
              <p>
                {currentPhase === COLLECTION_PHASES.BODY_DATA
                  ? '点击"开始"按钮开始填写您的身体数据。'
                  : currentPhase === COLLECTION_PHASES.FITNESS_GOALS
                  ? '点击"开始"按钮开始设定您的健身目标。'
                  : currentPhase === COLLECTION_PHASES.DIET_PREFERENCES
                  ? '点击"开始"按钮开始了解您的饮食偏好。'
                  : currentPhase === COLLECTION_PHASES.HEALTH_STATUS
                  ? '点击"开始"按钮开始了解您的健康状况。'
                  : '点击"开始"按钮开始了解您的中医体质相关症状。'
                }
              </p>
            </div>
          )}

          {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.BODY_DATA && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
              <p>这是您的身体数据总结，点击"继续"进入下一阶段。</p>
            </div>
          )}

          {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.FITNESS_GOALS && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
              <p>这是您的健身目标总结，点击"继续"进入下一阶段。</p>
            </div>
          )}

          {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.DIET_PREFERENCES && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
                <p>这是您的饮食偏好总结，点击"继续"进入下一阶段。</p>
            </div>
          )}
            {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.HEALTH_STATUS && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
                <p>这是您的健康状况总结，点击"继续"进入下一阶段。</p>
              </div>
            )}
            {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS && (
              <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
                <p>这是您的中医体质相关症状总结，点击"开始评估"完成信息采集并提交表单。</p>
            </div>
          )}
        </form>
      </Form>
    </div>
  )
} 
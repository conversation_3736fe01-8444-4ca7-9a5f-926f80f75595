-- 购物清单表
DROP TABLE IF EXISTS "public"."nf_shopping_lists";
CREATE TABLE "public"."nf_shopping_lists" (
  "id" int4 NOT NULL DEFAULT nextval('nf_shopping_lists_id_seq'::regclass),
  "user_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "diet_plan_id" int4,
  "herbal_plan_id" int4,
  "items" jsonb NOT NULL,
  "total_items" int4 NOT NULL DEFAULT 0,
  "purchased_items" int4 NOT NULL DEFAULT 0,
  "estimated_cost" decimal(10,2),
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS nf_shopping_lists_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 添加注释
COMMENT ON COLUMN "public"."nf_shopping_lists"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."nf_shopping_lists"."diet_plan_id" IS '关联的饮食计划ID';
COMMENT ON COLUMN "public"."nf_shopping_lists"."herbal_plan_id" IS '关联的中药方案ID';
COMMENT ON COLUMN "public"."nf_shopping_lists"."items" IS '购物清单项目详细数据（JSON格式）';
COMMENT ON COLUMN "public"."nf_shopping_lists"."total_items" IS '总项目数';
COMMENT ON COLUMN "public"."nf_shopping_lists"."purchased_items" IS '已购买项目数';
COMMENT ON COLUMN "public"."nf_shopping_lists"."estimated_cost" IS '预估总价';
COMMENT ON TABLE "public"."nf_shopping_lists" IS '用户购物清单表';

-- 创建索引
CREATE INDEX "idx_shopping_lists_user_id" ON "public"."nf_shopping_lists" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_shopping_lists_diet_plan_id" ON "public"."nf_shopping_lists" USING btree (
  "diet_plan_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

CREATE INDEX "idx_shopping_lists_herbal_plan_id" ON "public"."nf_shopping_lists" USING btree (
  "herbal_plan_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

CREATE INDEX "idx_shopping_lists_created_at" ON "public"."nf_shopping_lists" USING btree (
  "created_at" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- 主键
ALTER TABLE "public"."nf_shopping_lists" ADD CONSTRAINT "nf_shopping_lists_pkey" PRIMARY KEY ("id");

-- 唯一约束：每个用户只能有一个购物清单
ALTER TABLE "public"."nf_shopping_lists" ADD CONSTRAINT "unique_user_shopping_list" UNIQUE ("user_id");

-- 外键约束（可选，如果需要严格的关联关系）
-- ALTER TABLE "public"."nf_shopping_lists" ADD CONSTRAINT "fk_shopping_lists_diet_plan" 
--   FOREIGN KEY ("diet_plan_id") REFERENCES "public"."nf_diet_plans" ("id") ON DELETE SET NULL;

-- ALTER TABLE "public"."nf_shopping_lists" ADD CONSTRAINT "fk_shopping_lists_herbal_plan" 
--   FOREIGN KEY ("herbal_plan_id") REFERENCES "public"."nf_herbal_plans" ("id") ON DELETE SET NULL;

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription,  CardHeader, CardTitle } from "@/components/ui/card"
import {
  Heart,
  Stethoscope,
  Shield,
  Sparkles,
  Brain,
  Activity,
  Info,
  User,
  TrendingUp,
  Target,
  Clock,
  Award,
  CheckCircle,
  Users,
  Zap,
  BookOpen
} from "lucide-react"
import { Accordion, AccordionContent, AccordionTrigger, AccordionItem } from "@/components/ui/accordion"
import { Badge } from '@/components/ui/badge'
import { type Locale, getPathname, generateAlternates } from "@/i18n-config";
import { getDictionary, i18nNamespaces } from '@/i18n'
import { Home as HomeType } from "@/types/locales";
import { Auth } from "@/types/locales/auth";
import { host } from '@/config/config'
// 完全移除客户端特有的hooks导入
// import { useRef } from 'react';
// import { useSession } from 'next-auth/react';
import HealthAssessmentSection from '@/components/home/<USER>'
import ProfileCompletionDialog from '@/components/collection/profile-completion-dialog'
import ProfileCompletionDialogWrapper from '@/components/collection/profile-dialog-wrapper'
import { cookies } from 'next/headers'
import { Suspense } from 'react'

export const dynamic = 'force-dynamic'

export async function generateMetadata({ params }: { params: { lang: Locale } }) {
  const { lang } = await params
  const alternates = generateAlternates(lang, '/');
  const i18nHome = await getDictionary<HomeType>(lang, i18nNamespaces.home);
  return {
    title: i18nHome.meta.title,
    description: i18nHome.meta.description,
    keywords: i18nHome.meta.keywords,
    twitter: {
      card: "summary_large_image", 
      title: i18nHome.meta.title,
      description: i18nHome.meta.description
    },
    openGraph: {
      type: "website",
      url: `${host}${getPathname(lang, '')}`,
      title: i18nHome.meta.title,
      description: i18nHome.meta.description,
      siteName: i18nHome.hero.title
    },
    alternates: {
      canonical: `${host}${getPathname(lang, '')}`,
      languages: alternates
    }
  }
}

export default async function Home({
  params,
}: {
  params: { lang: string }
}) {
  const { lang } = await params
  const i18nHome = await getDictionary<HomeType>(lang, i18nNamespaces.home);
  // 加载auth相关的i18n
  const i18nAuth = await getDictionary<Auth>(lang, i18nNamespaces.auth);
  
  // 检查用户登录状态和信息完善状态
  // 实际应用中，这部分逻辑可能需要根据您的认证系统进行调整
  let isLoggedIn = false
  try {
    const cookieStore = await cookies()
    
    // 尝试多种可能的认证cookie名称
    isLoggedIn = cookieStore.has('auth_token') || 
                cookieStore.has('next-auth.session-token') || 
                cookieStore.has('__Secure-next-auth.session-token') ||
                cookieStore.has('session-id')
    
    console.log('服务器端检测登录状态:', isLoggedIn)
  } catch (error) {
    console.error('检查登录状态出错:', error)
  }
  
  // 注意：这是一个服务器组件，我们在这里准备客户端组件所需的数据，
  // 实际的交互逻辑将在客户端组件中处理

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50/30 to-emerald-50/20">
      {/* 医疗主题背景装饰 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {/* 心电图波形背景 */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.02]">
          <svg className="w-full h-full" viewBox="0 0 1200 600" preserveAspectRatio="none">
            <path d="M0,300 L100,300 L120,200 L140,400 L160,300 L200,300 L220,250 L240,350 L260,300 L400,300 L420,150 L440,450 L460,300 L600,300 L620,280 L640,320 L660,300 L800,300 L820,200 L840,400 L860,300 L1000,300 L1020,250 L1040,350 L1060,300 L1200,300" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  fill="none"
                  className="text-blue-600"/>
          </svg>
        </div>
        
        {/* 医疗图标装饰 */}
        <div className="absolute top-20 left-10 w-16 h-16 bg-red-100 rounded-full flex items-center justify-center opacity-20 pulse-slow">
          <Heart className="w-8 h-8 text-red-500" />
        </div>
        <div className="absolute top-32 right-16 w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center opacity-20 float-animation">
          <Stethoscope className="w-7 h-7 text-blue-500" />
        </div>
        <div className="absolute bottom-32 right-20 w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center opacity-20 pulse-slow">
          <Shield className="w-10 h-10 text-emerald-500" />
        </div>
        <div className="absolute bottom-64 left-12 w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center opacity-20 float-delayed">
          <Activity className="w-6 h-6 text-purple-500" />
        </div>
        <div className="absolute top-1/2 left-1/3 w-18 h-18 bg-orange-100 rounded-full flex items-center justify-center opacity-15 pulse-slow">
          <TrendingUp className="w-9 h-9 text-orange-500" />
        </div>
      </div>

      {/* Hero区域 - 重新设计 */}
      <section className="flex items-center justify-center relative px-4 py-16 pt-24">
        <div className="max-w-7xl mx-auto text-center space-y-8 w-full">
          {/* AI智能标签 */}
          <div className="inline-flex items-center bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-emerald-600/10 backdrop-blur-xl border border-blue-200/30 px-6 py-3 rounded-full text-sm font-semibold shadow-lg">
            <Sparkles className="w-5 h-5 mr-3 text-blue-600 animate-pulse" />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{i18nHome.hero.aiLabel||"AI智能健康评估"}</span>
          </div>
          
          {/* 主标题 */}
          <div className="space-y-6">
            <h1 className="text-5xl md:text-7xl font-bold leading-tight">
              <span className="block bg-gradient-to-r from-blue-900 via-blue-700 to-emerald-700 bg-clip-text text-transparent break-keep overflow-hidden">
                {i18nHome.hero.title||"高血压健康评估"}
              </span>
              <span className="block text-2xl md:text-4xl mt-4 text-slate-600 font-medium">
                {i18nHome.hero.subtitle||"专业 · 智能 · 个性化血压管理方案"}
              </span>
            </h1>
            
            <p className="text-lg md:text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              {i18nHome.hero.description.split('\n').map((line, index) => (
                <span key={index}>
                  {line}
                  {index === 0 && <br className="hidden md:block" />}
                </span>
              ))}
            </p>
          </div>

          {/* CTA按钮组 */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 pt-4">
            <a href="#health-assessment">
              <button className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-blue-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <Brain className="w-5 h-5 mr-2" />
                  {i18nHome.hero.ctaPrimary||"开始健康评估"}
                </div>
              </button>
            </a>
            <button className="px-8 py-4 bg-white/80 backdrop-blur-sm border border-slate-200 text-slate-700 font-semibold rounded-2xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <Info className="w-5 h-5 mr-2 inline" />
              {i18nHome.hero.ctaSecondary||"了解评估详情"}
            </button>
          </div>

          {/* 核心功能预览卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 px-4">
            {[
              {
                icon: Heart,
                color: "from-red-500 to-pink-500",
                bgColor: "from-red-50 to-pink-50"
              },
              {
                icon: Shield,
                color: "from-emerald-500 to-green-500", 
                bgColor: "from-emerald-50 to-green-50"
              },
              {
                icon: TrendingUp,
                color: "from-blue-500 to-cyan-500",
                bgColor: "from-blue-50 to-cyan-50"
              }
            ].map((feature, index) => (
              <div key={index} className="group relative">
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgColor} rounded-3xl opacity-50 group-hover:opacity-70 transition-opacity duration-300`}></div>
                <div className="relative bg-white/60 backdrop-blur-xl border border-white/50 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                  <div className={`w-16 h-16 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-800 mb-3">{i18nHome.hero.features[index].title}</h3>
                  <p className="text-slate-600 leading-relaxed">{i18nHome.hero.features[index].description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* 中医五行循环图 */}
          <div className="mt-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-5xl font-bold bg-gradient-to-r from-amber-600 via-emerald-600 to-blue-600 bg-clip-text text-transparent mb-4">
                中医五行健康调理系统
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                传统中医五行理论与现代健康科学完美融合，为您打造专属的五行平衡调理方案
              </p>
            </div>

            <div className="relative max-w-7xl mx-auto">
              {/* 五行能量场背景 */}
              <div className="absolute inset-0 bg-gradient-to-t from-amber-100/20 via-emerald-100/20 to-blue-100/20 rounded-3xl blur-3xl"></div>
              
              <div className="relative bg-white/30 backdrop-blur-3xl border border-white/40 rounded-3xl p-8 shadow-2xl">
                <div className="flex items-center justify-center min-h-[800px]">
                  
                  {/* 五行循环圆圈 */}
                  <div className="relative w-[600px] h-[600px]">
                    
                                         {/* 中心太极 */}
                     <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full bg-gradient-to-br from-gray-800 via-gray-600 to-gray-400 flex items-center justify-center shadow-2xl border-4 border-white/60 z-20">
                       <div className="text-white text-4xl animate-spin" style={{ animationDuration: '10s' }}>☯</div>
                    </div>

                     {/* 五行元素 - 循环旋转 */}
                     <div className="absolute inset-0 animate-spin" style={{ animationDuration: '20s' }}>
                       {[
                         { name: "金", char: "金", color: "from-yellow-400 via-amber-500 to-yellow-600", shadow: "shadow-yellow-500/50", angle: 0 },
                         { name: "水", char: "水", color: "from-blue-400 via-cyan-500 to-blue-600", shadow: "shadow-blue-500/50", angle: 72 },
                         { name: "木", char: "木", color: "from-green-400 via-emerald-500 to-green-600", shadow: "shadow-green-500/50", angle: 144 },
                         { name: "火", char: "火", color: "from-red-400 via-rose-500 to-red-600", shadow: "shadow-red-500/50", angle: 216 },
                         { name: "土", char: "土", color: "from-orange-400 via-amber-500 to-orange-600", shadow: "shadow-orange-500/50", angle: 288 }
                       ].map((element, index) => {
                         // 计算每个元素的精确位置（半径220px，圆心在300,300）
                         const radius = 220;
                         const centerX = 300;
                         const centerY = 300;
                         const angleRad = (element.angle * Math.PI) / 180;
                         const x = centerX + radius * Math.cos(angleRad - Math.PI/2); // -π/2让0度在顶部
                         const y = centerY + radius * Math.sin(angleRad - Math.PI/2);
                         
                         return (
                           <div
                             key={element.name}
                             className={`absolute w-28 h-28 rounded-full bg-gradient-to-br ${element.color} flex items-center justify-center text-white font-black text-2xl shadow-2xl ${element.shadow} border-4 border-white/50 cursor-pointer transition-all duration-500 hover:scale-125 hover:shadow-3xl z-10`}
                              style={{ 
                               left: `${x - 56}px`, // -56px因为元素宽度是112px(28*4)
                               top: `${y - 56}px`,   // -56px因为元素高度是112px(28*4)
                               animation: `counter-rotate 20s linear infinite`,
                               boxShadow: `0 0 40px ${element.shadow.includes('yellow') ? '#fbbf24' : element.shadow.includes('blue') ? '#3b82f6' : element.shadow.includes('green') ? '#10b981' : element.shadow.includes('red') ? '#ef4444' : '#f97316'}40, inset 0 0 20px rgba(255,255,255,0.3)`
                             }}
                           >
                             <div className="relative">
                               <div className="text-2xl font-black drop-shadow-lg">{element.char}</div>
                               <div className="absolute inset-0 text-2xl font-black text-white/30 blur-sm">{element.char}</div>
                    </div>

                             {/* 能量光环 */}
                             <div className="absolute inset-0 rounded-full border-2 border-white/40 animate-pulse"></div>
                             <div className="absolute inset-0 rounded-full border border-white/20 animate-ping" style={{ animationDuration: '3s' }}></div>
                        </div>
                         );
                       })}
                  </div>

                    {/* 相生相克连接线 */}
                    <svg className="absolute inset-0 w-full h-full pointer-events-none z-5" viewBox="0 0 600 600">
                      {/* 相生圆环 */}
                      <circle 
                        cx="300" 
                        cy="300" 
                        r="220" 
                        fill="none" 
                        stroke="url(#gradientGreen)" 
                        strokeWidth="4" 
                        strokeDasharray="20,8"
                        className="animate-pulse"
                        style={{ animationDuration: '3s' }}
                      />
                      
                      {/* 相克五角星 */}
                      <path 
                        d={(() => {
                          // 计算五角星的精确坐标
                          const radius = 220;
                          const centerX = 300;
                          const centerY = 300;
                          const points: { x: number; y: number }[] = [];
                          
                          // 五个顶点的角度 (0°, 72°, 144°, 216°, 288°)
                          for (let i = 0; i < 5; i++) {
                            const angle = (i * 72 - 90) * Math.PI / 180; // -90度让第一个点在顶部
                            const x = centerX + radius * Math.cos(angle);
                            const y = centerY + radius * Math.sin(angle);
                            points.push({ x, y });
                          }
                          
                          // 按相克顺序连接：金->木->土->水->火->金
                          // 对应索引：0->2->4->1->3->0
                          const order = [0, 2, 4, 1, 3, 0]; // 相克顺序
                          const pathData = order.map((index, i) => 
                            `${i === 0 ? 'M' : 'L'}${points[index].x.toFixed(1)},${points[index].y.toFixed(1)}`
                          ).join(' ') + ' Z';
                          
                          return pathData;
                        })()}
                        fill="none" 
                        stroke="url(#gradientOrange)" 
                        strokeWidth="3" 
                        strokeDasharray="15,8"
                        className="animate-pulse"
                        style={{ animationDuration: '4s' }}
                      />
                      
                      {/* SVG渐变定义 */}
                      <defs>
                        <linearGradient id="gradientGreen" x1="0%" y1="0%" x2="100%" y2="0%">
                          <stop offset="0%" style={{ stopColor: '#10b981', stopOpacity: 0.8 }} />
                          <stop offset="50%" style={{ stopColor: '#34d399', stopOpacity: 0.6 }} />
                          <stop offset="100%" style={{ stopColor: '#6ee7b7', stopOpacity: 0.4 }} />
                        </linearGradient>
                        <linearGradient id="gradientOrange" x1="0%" y1="0%" x2="100%" y2="0%">
                          <stop offset="0%" style={{ stopColor: '#f97316', stopOpacity: 0.8 }} />
                          <stop offset="50%" style={{ stopColor: '#fb923c', stopOpacity: 0.6 }} />
                          <stop offset="100%" style={{ stopColor: '#fdba74', stopOpacity: 0.4 }} />
                        </linearGradient>
                      </defs>
                    </svg>

                                         {/* 能量粒子轨道 */}
                     <div className="absolute inset-0 pointer-events-none">
                       {[...Array(12)].map((_, i) => (
                         <div
                           key={i}
                           className="absolute w-4 h-4 bg-gradient-to-r from-white to-amber-300 rounded-full opacity-70"
                           style={{
                             left: '50%',
                             top: '50%',
                             animation: `orbit-particles 10s linear infinite`,
                             animationDelay: `${i * 0.8}s`,
                             transformOrigin: '0 0'
                           }}
                         ></div>
                       ))}
                          </div>
                        </div>
                      </div>

                {/* 五行说明 */}
                <div className="mt-16 text-center">
                  <h3 className="text-2xl font-bold text-slate-800 mb-8">五行相生相克 · 循环不息</h3>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                    {[
                      { name: "金", desc: "收敛肃降", organ: "肺大肠", color: "text-yellow-600", bg: "bg-yellow-50" },
                      { name: "水", desc: "滋润下行", organ: "肾膀胱", color: "text-blue-600", bg: "bg-blue-50" },
                      { name: "木", desc: "生发升散", organ: "肝胆", color: "text-green-600", bg: "bg-green-50" },
                      { name: "火", desc: "温热上炎", organ: "心小肠", color: "text-red-600", bg: "bg-red-50" },
                      { name: "土", desc: "运化承载", organ: "脾胃", color: "text-orange-600", bg: "bg-orange-50" }
                    ].map((item, index) => (
                      <div key={item.name} className={`${item.bg} rounded-2xl p-6 border-2 border-white/50 shadow-lg hover:scale-105 transition-all duration-300`}>
                        <div className={`text-3xl font-black ${item.color} mb-3`}>{item.name}</div>
                        <div className="text-sm font-semibold text-slate-700 mb-2">{item.desc}</div>
                        <div className="text-xs text-slate-500">{item.organ}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 五行特色统计 */}
                <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                  {[
                    { number: "5千年", label: "中医传承智慧", color: "from-amber-50 to-amber-100", text: "text-amber-600" },
                    { number: "360°", label: "全方位调理", color: "from-emerald-50 to-emerald-100", text: "text-emerald-600" },
                    { number: "循环", label: "生生不息", color: "from-blue-50 to-blue-100", text: "text-blue-600" },
                    { number: "平衡", label: "阴阳调和", color: "from-purple-50 to-purple-100", text: "text-purple-600" }
                  ].map((stat, index) => (
                    <div key={index} className={`text-center p-6 bg-gradient-to-br ${stat.color} rounded-2xl shadow-lg hover:scale-105 transition-all duration-300`}>
                      <div className={`text-3xl font-bold ${stat.text} mb-2`}>
                        {stat.number}
                      </div>
                      <div className="text-sm text-slate-600 leading-tight">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 数据统计展示 */}
          <div className="mt-16 bg-white/40 backdrop-blur-xl rounded-3xl border border-white/50 p-6 md:p-8 shadow-xl">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
              {[
                { icon: Users },
                { icon: Target },
                { icon: Clock },
                { icon: Award }
              ].map((stat, index) => (
                <div key={index} className="text-center space-y-2">
                  <stat.icon className="w-6 h-6 md:w-8 md:h-8 mx-auto text-blue-600 mb-2" />
                  <div className="text-xl md:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-700 to-emerald-700 bg-clip-text text-transparent">
                    {i18nHome.statistics.stats[index].number}
                  </div>
                  <div className="text-xs md:text-sm text-slate-600 font-medium leading-tight">{i18nHome.statistics.stats[index].label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 主要功能区域 */}
      <section id="health-assessment" className="py-16 px-4 bg-gradient-to-b from-transparent to-white/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 space-y-6">
            <h2 className="text-4xl md:text-6xl font-bold">
              <span className="bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                {i18nHome.assessment.title}
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              {i18nHome.assessment.subtitle}
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-emerald-600 mx-auto rounded-full"></div>
          </div>
        
          {/* 健康评估部分 - 根据用户是否已有报告显示不同内容 */}
          <div id="health-assessment-section">
            <Suspense fallback={<div>加载中...</div>}>
              <HealthAssessmentSection lang={lang} />
            </Suspense>
          </div>
        </div>
      </section>

      {/* 评估流程 */}
      <section className="py-16 px-4 bg-slate-50/50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">
              {i18nHome.process.title}
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              {i18nHome.process.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { icon: User },
              { icon: Heart },
              { icon: Stethoscope },
              { icon: Brain }
            ].map((step, index) => (
              <div key={index} className="relative">
                {/* 连接线 */}
                {index < 3 && (
                  <div className="hidden md:block absolute top-12 left-full w-full h-0.5 bg-gradient-to-r from-blue-300 to-emerald-300 z-0"></div>
                )}
                
                <div className="relative bg-white/80 backdrop-blur-xl border border-white/50 rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 z-10">
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-r from-blue-600 to-emerald-600 text-white text-sm font-bold rounded-full flex items-center justify-center">
                    {i18nHome.process.steps[index].step}
                  </div>
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-emerald-100 rounded-xl flex items-center justify-center mx-auto mb-4 mt-2">
                    <step.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-bold text-slate-800 mb-3">{i18nHome.process.steps[index].title}</h3>
                  <p className="text-sm text-slate-600 leading-relaxed">{i18nHome.process.steps[index].desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 核心优势 */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">
              {i18nHome.advantages.title}
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              {i18nHome.advantages.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {i18nHome.advantages.features.map((feature, index) => (
              <div key={index} className="bg-white/80 backdrop-blur-xl border border-white/50 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300">
                <div className="flex items-center space-x-6 mb-6">
                  <div className={`w-16 h-16 bg-gradient-to-br ${index === 0 ? "from-blue-500 to-cyan-500" : "from-emerald-500 to-green-500"} rounded-2xl flex items-center justify-center`}>
                    {index === 0 ? <Brain className="w-8 h-8 text-white" /> : <Shield className="w-8 h-8 text-white" />}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-800">{feature.title}</h3>
                    <p className="text-slate-600">{feature.subtitle}</p>
                  </div>
                </div>
                <p className="text-slate-600 leading-relaxed mb-6">
                  {feature.description}
                </p>
                <div className="flex flex-wrap gap-3">
                  {feature.tags.map((tag, tagIndex) => (
                    <span key={tagIndex} className={`px-4 py-2 ${index === 0 ? (tagIndex === 0 ? "bg-blue-100 text-blue-800" : tagIndex === 1 ? "bg-emerald-100 text-emerald-800" : "bg-purple-100 text-purple-800") : (tagIndex === 0 ? "bg-emerald-100 text-emerald-800" : tagIndex === 1 ? "bg-blue-100 text-blue-800" : "bg-orange-100 text-orange-800")} text-sm font-medium rounded-full`}>
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* 特色功能亮点 */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { icon: CheckCircle },
              { icon: Zap },
              { icon: Users }
            ].map((feature, index) => (
              <div key={index} className="text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center mx-auto">
                  <feature.icon className="w-8 h-8 text-slate-600" />
                </div>
                <h3 className="text-xl font-bold text-slate-800">{i18nHome.advantages.highlights[index].title}</h3>
                <p className="text-slate-600">{i18nHome.advantages.highlights[index].description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ部分 */}
      <section className="py-16 px-4 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">{i18nHome.faq.title}</h2>
            <p className="text-xl text-slate-600">{i18nHome.faq.subtitle}</p>
          </div>

          <Accordion type="single" collapsible className="space-y-6">
            {i18nHome.faq.items.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index + 1}`} className="bg-white/80 backdrop-blur-xl border border-white/50 rounded-2xl px-6 shadow-lg">
                <AccordionTrigger className="text-left font-bold text-slate-800 hover:no-underline text-lg py-6">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-slate-600 leading-relaxed pb-6">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>

      {/* 客户端组件包装器，用于处理登录后的弹窗逻辑 */}
      <ProfileCompletionDialogWrapper isLoggedIn={isLoggedIn} lang={lang} />
    </div>
  )
}


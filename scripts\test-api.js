// 测试通义千问API配置的脚本
// 运行命令: node scripts/test-api.js

require('dotenv').config({ path: '.env.local' });

async function testDashScopeAPI() {
  const apiKey = process.env.DASHSCOPE_API_KEY;
  const model = process.env.VISION_MODEL;

  console.log('🔍 检查环境变量配置...');
  console.log(`DASHSCOPE_API_KEY: ${apiKey ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`VISION_MODEL: ${model || 'qwen-plus-latest'}`);

  if (!apiKey) {
    console.error('❌ 错误: 请在 .env.local 文件中配置 DASHSCOPE_API_KEY');
    return;
  }

  console.log('\n🚀 测试API连接...');

  try {
    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model || 'qwen-plus-latest',
        input: {
          messages: [
            {
              role: 'user',
              content: '请简单介绍一下中医的基本理论。'
            }
          ]
        },
        parameters: {
          max_tokens: 100,
          temperature: 0.7,
          top_p: 0.8,
        }
      }),
    });

    console.log(`📡 API响应状态: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API调用失败:', errorText);
      return;
    }

    const data = await response.json();
    
    if (data.output?.text) {
      console.log('✅ API测试成功!');
      console.log('📝 测试响应:', data.output.text.substring(0, 100) + '...');
      console.log('\n🎉 您的配置正确，健康评估功能可以正常使用!');
    } else {
      console.error('❌ API返回数据格式异常:', JSON.stringify(data, null, 2));
    }

  } catch (error) {
    console.error('❌ 网络错误:', error.message);
    console.log('\n💡 可能的解决方案:');
    console.log('1. 检查网络连接');
    console.log('2. 验证API密钥是否正确');
    console.log('3. 确认阿里云账户余额充足');
  }
}

// 运行测试
testDashScopeAPI(); 
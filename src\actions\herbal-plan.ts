'use server'

import { getLatestHerbalPlan, hasHerbalPlan, deleteHerbalPlan } from '@/lib/db/herbal-plan'
import { HerbalWeightLossPlan, HerbalPlanResponse } from '@/types/herbal-plan'

/**
 * 获取用户的中药方案
 */
export async function getUserHerbalPlan(userId: string): Promise<HerbalWeightLossPlan | null> {
  try {
    return await getLatestHerbalPlan(userId)
  } catch (error) {
    console.error('获取用户中药方案失败:', error)
    return null
  }
}

/**
 * 检查用户是否有中药方案
 */
export async function checkUserHasHerbalPlan(userId: string): Promise<boolean> {
  try {
    return await hasHerbal<PERSON>lan(userId)
  } catch (error) {
    console.error('检查用户中药方案失败:', error)
    return false
  }
}

/**
 * 生成中药方案
 */
export async function generateHerbalPlan(userId: string): Promise<HerbalPlanResponse> {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/generate-herbal-plan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId
      })
    })

    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: result.error || '生成中药方案失败'
      }
    }

    return {
      success: true,
      herbalPlan: result.herbalPlan
    }
  } catch (error) {
    console.error('生成中药方案失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '生成中药方案失败'
    }
  }
}

/**
 * 删除用户的中药方案
 */
export async function removeUserHerbalPlan(userId: string): Promise<boolean> {
  try {
    return await deleteHerbalPlan(userId)
  } catch (error) {
    console.error('删除用户中药方案失败:', error)
    return false
  }
}

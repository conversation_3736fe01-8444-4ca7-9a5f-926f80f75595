{"meta": {"title": "고혈압 건강 평가 - 고혈압 식단 메뉴 | 고혈압 운동 가이드 & 혈압 식단 계획", "description": "상세한 건강 설문지를 통해 AI 기술을 사용한 고혈압 위험 평가로 개인 맞춤형 혈압 조절 계획, 식단 권장사항, 운동 지도, 생활습관 조정 제안을 받아보세요.", "alt": "고혈압 건강 평가 - 지능형 혈압 위험 분석 시스템", "keywords": "DASH 다이어트,고혈압 음식,고혈압 건강 식단"}, "hero": {"aiLabel": "AI 지능형 건강 평가", "title": "고혈압 건강 평가", "subtitle": "전문적 · 지능적 · 개인맞춤형 혈압 관리 솔루션", "description": "국제 의학 기준을 바탕으로 AI 지능형 분석 기술과 결합하여 과학적이고 정확한 고혈압 위험 평가를 제공합니다\n맞춤형 혈압 관리 방안으로 심혈관 건강을 종합적으로 보호합니다", "ctaPrimary": "건강 평가 시작", "ctaSecondary": "평가 세부사항 알아보기", "features": [{"title": "전문적 위험 평가", "description": "임상 기준에 기반한 고혈압 위험 등급 평가, 과학적이고 정확함"}, {"title": "개인맞춤형 솔루션", "description": "맞춤형 비약물 치료 및 관리 방안, 안전하고 효과적"}, {"title": "지속적 모니터링", "description": "장기간 혈압 변화 추적, 관리 전략 동적 조정"}]}, "pyramid": {"title": "혈압 건강 관리 피라미드", "subtitle": "과학적인 혈압 관리 계층 구조로 종합적인 심혈관 건강 보호 체계 구축을 돕습니다", "managementLevels": "관리 단계", "fromAcuteToPrevention": "응급에서 예방까지", "healthScore": "85점", "healthScoreLabel": "건강 관리 점수", "levels": [{"level": 1, "label": "급성기", "percentage": "80%"}, {"level": 2, "label": "약물", "percentage": "60%"}, {"level": 3, "label": "생활습관", "percentage": "40%"}, {"level": 4, "label": "모니터링", "percentage": "20%"}, {"level": 5, "label": "예방", "percentage": "5%"}], "pyramidLevels": [{"title": "급성기 관리", "subtitle": "응급 혈압 조절 및 위험 평가", "level": "Level 1", "category": "위기 개입", "systolic": "≥180", "diastolic": "≥110", "intervention": "즉시", "systolicUnit": "수축기 mmHg", "diastolicUnit": "이완기 mmHg", "interventionUnit": "의료 개입"}, {"title": "약물 치료", "subtitle": "표준화된 강압 약물 방안", "level": "Level 2", "category": "의학적 개입", "systolic": "160-179", "diastolic": "100-109", "intervention": "4-6주", "systolicUnit": "수축기 mmHg", "diastolicUnit": "이완기 mmHg", "interventionUnit": "효과 주기"}, {"title": "생활습관 개입", "subtitle": "식단, 운동 및 행동 조정", "level": "Level 3", "category": "행동 개선", "systolic": "140-159", "diastolic": "90-99", "intervention": "3-6개월", "systolicUnit": "수축기 mmHg", "diastolicUnit": "이완기 mmHg", "interventionUnit": "개선 주기"}, {"title": "건강 모니터링", "subtitle": "정기 검진 및 데이터 추적", "level": "Level 4", "category": "모니터링 예방", "systolic": "130-139", "diastolic": "85-89", "intervention": "매월", "systolicUnit": "수축기 mmHg", "diastolicUnit": "이완기 mmHg", "interventionUnit": "모니터링 빈도"}, {"title": "예방 건강관리", "subtitle": "건강 교육 및 위험 예방", "level": "Level 5", "category": "기초 예방", "systolic": "<130", "diastolic": "<85", "intervention": "연간", "systolicUnit": "수축기 mmHg", "diastolicUnit": "이완기 mmHg", "interventionUnit": "검진 빈도"}], "description": "저희 AI 평가 시스템은 귀하의 혈압 수준과 건강 상태에 따라 다층적인 과학적 관리 방안을 수립합니다", "stats": [{"number": "5단계", "label": "관리 단계"}, {"number": "360°", "label": "전방위 커버"}, {"number": "개인맞춤형", "label": "맞춤형 솔루션"}, {"number": "AI 지능형", "label": "과학적 분석"}]}, "statistics": {"stats": [{"number": "5만+", "label": "사용자 신뢰"}, {"number": "98%", "label": "평가 정확도"}, {"number": "30일", "label": "개인맞춤형 솔루션"}, {"number": "전문적", "label": "의학 기준"}]}, "assessment": {"title": "건강 평가 시작하기", "subtitle": "몇 분만 투자하시면 전문적인 고혈압 위험 평가 보고서와 개인맞춤형 건강 관리 방안을 받으실 수 있습니다"}, "process": {"title": "지능형 평가 프로세스", "subtitle": "간단한 4단계로 전문 의학급 고혈압 건강 평가를 받으세요", "steps": [{"title": "기본 정보", "desc": "나이, 성별, 키, 몸무게 등 기초 건강 정보를 입력하세요", "step": "01"}, {"title": "혈압 데이터", "desc": "현재 혈압 수치와 최근 혈압 변화 상황을 기록하세요", "step": "02"}, {"title": "건강 상태", "desc": "병력, 가족력 및 복용 약물 정보를 자세히 파악하세요", "step": "03"}, {"title": "AI 분석", "desc": "전문적인 위험 평가와 개인맞춤형 건강 조언을 생성하세요", "step": "04"}]}, "advantages": {"title": "저희를 선택하는 이유", "subtitle": "최신 의학 연구와 임상 가이드라인을 바탕으로 과학적이고 신뢰할 수 있는 건강 평가 서비스를 제공합니다", "features": [{"title": "AI 지능형 분석", "subtitle": "근거 기반 의학의 평가 모델", "description": "국제 표준의 고혈압 위험 평가 모델을 사용하여 기계 학습 알고리즘과 결합하고, 나이, 성별, 혈압 수치, 병력, 가족력 등 다차원 요소를 종합 분석하여 정확한 위험 등급 평가를 제공합니다.", "tags": ["근거 기반 의학", "다요소 분석", "국제 기준"]}, {"title": "개인맞춤형 솔루션", "subtitle": "맞춤형 건강 관리 전략", "description": "개인의 건강 상태, 생활 습관 및 위험 등급에 따라 개인맞춤형 혈압 관리 방안을 수립하며, 식단 조정, 운동 지도, 생활습관 개선 등 비약물 개입 조치를 포함합니다.", "tags": ["식단 지도", "운동 권장", "생활습관"]}], "highlights": [{"title": "과학적 정확성", "description": "국제 의학 기준과 임상 연구 데이터를 기반으로"}, {"title": "빠르고 편리", "description": "3-5분 평가 완료, 즉시 보고서 생성"}, {"title": "전문적 신뢰성", "description": "의학 전문가 팀 검토, 평가 품질 보장"}]}, "faq": {"title": "자주 묻는 질문", "subtitle": "고혈압 건강 평가에 대한 자주 묻는 질문과 답변", "items": [{"question": "이 평가는 얼마나 정확한가요?", "answer": "저희 평가는 국제 표준 고혈압 위험 평가 가이드라인을 기반으로 하며, AI 지능형 분석 기술과 결합되어 높은 참고 가치를 가집니다. 평가 정확도는 98% 이상이지만, 이것이 전문 의사의 진단을 대체할 수는 없습니다. 평가 결과를 의사와의 상담에서 중요한 참고 자료로 활용하시기를 권장합니다."}, {"question": "개인 정보가 안전한가요?", "answer": "저희는 귀하의 개인정보 보안을 엄격히 보호합니다. 모든 건강 데이터는 종단간 암호화 처리되며 국제 의료 데이터 보호 기준에 부합합니다. 개인 평가 보고서 생성에만 사용되며, 제3자와 절대 공유하거나 상업적 목적으로 사용하지 않습니다."}, {"question": "평가 보고서에는 어떤 내용이 포함되나요?", "answer": "상세한 평가 보고서에는 다음이 포함됩니다: (1) 고혈압 위험 등급 평가 및 해석 (2) 개인 건강 상태 종합 분석 (3) 개인맞춤형 혈압 관리 방안 (4) 과학적 식단 및 운동 지도 (5) 생활습관 개선 제안 (6) 정기 모니터링 및 추적 관리 계획."}, {"question": "얼마나 자주 재평가를 받을 수 있나요?", "answer": "혈압 관리 상황에 따라 정기적인 재평가를 권장합니다. 적극적으로 생활습관을 조정하고 있다면 진행 상황을 모니터링하기 위해 월 1회 평가를 권장하며, 혈압이 안정적으로 관리되고 있다면 3-6개월마다 한 번씩 평가하여 관리 방안의 효과를 확인할 수 있습니다."}]}, "form": {"mainTitle": "고혈압을 위한 맞춤형 식단 계획", "subtitle": "귀하의 건강 데이터 지원이 필요합니다! 간단한 설문지를 작성해 주시면(약 2분), 저나트륨·고칼륨 레시피를 정확히 추천하여 과학적인 혈압 관리를 도와드립니다.", "step": "단계", "ageLabel": "나이", "years": "세", "prevButton": "이전", "nextButton": "다음", "submitButton": "맞춤 설정 시작", "submittingButton": "평가 중...", "loadExistingTitle": "이전 평가 발견", "loadExistingDesc": "최근 건강 평가 기록이 발견되었습니다. 불러오시겠습니까?", "loadExistingAction": "기록 불러오기", "loadExistingCancel": "새로 작성", "submitSuccessTitle": "평가 성공", "submitSuccessDesc": "건강 보고서가 생성되었습니다.", "submitErrorAuthTitle": "먼저 로그인해주세요", "submitErrorAuthDesc": "건강 평가를 진행하기 전에 로그인해주세요.", "submitErrorNetworkTitle": "평가 실패", "submitErrorNetworkDesc": "서버에 문제가 있습니다. 나중에 다시 시도해주세요.", "questions": {"basicInfoTitle": "기본 정보", "birthDate": {"title": "생년월일을 알려주세요", "monthNames": ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"], "daySuffix": "일"}, "gender": {"title": "성별을 알려주세요", "male": "남성", "female": "여성", "non_binary": "논바이너리"}, "height": {"title": "키를 알려주세요", "unit": "cm"}, "weight": {"title": "체중을 알려주세요", "unit": "kg", "label": "체중", "range": "범위: {min} - {max} kg"}, "bloodPressure": {"title": "현재 혈압을 알려주세요", "description": "슬라이드하여 수축기 혈압과 이완기 혈압을 조정하세요", "systolicLabel": "수축기 혈압", "diastolicLabel": "이완기 혈압", "unknownBloodPressure": "혈압을 모르시나요?", "normalBloodPressure": "정상 혈압", "highBloodPressure": "고혈압", "lowBloodPressure": "저혈압"}, "medicalHistory": {"title": "건강상 문제가 있나요?", "familyHistory": "고혈압 가족력", "diabetes": "당뇨병", "heartDisease": "심장병", "kidneyDisease": "신장병", "stroke": "뇌졸중", "cholesterol": "고콜레스테롤"}, "exercise": {"title": "평균적으로 일주일에 몇 시간 운동하시나요?", "none": "운동 안 함", "light": "주 1-2시간", "moderate": "주 3-4시간", "intense": "주 5시간 이상"}, "salt": {"title": "일상적인 염분 섭취는 어떠신가요?", "low": "짜지 않음", "normal": "보통", "high": "매우 짬"}, "stress": {"title": "최근 정서적 스트레스 수준은 어떠신가요?", "low": "낮음", "moderate": "보통", "high": "높음"}, "sleep": {"title": "하루 수면 시간은 얼마나 되나요?", "unit": "시간", "short": "수면 부족 😴", "good": "충분한 수면 😊", "long": "넉넉한 수면 😌", "range_4": "4시간", "range_8": "8시간", "range_12": "12시간"}, "additionalInfo": {"title": "추가로 알려주실 건강 정보가 있나요?", "description": "예: 다른 질병, 알레르기, 장기 복용 약 등", "placeholder": "여기에 입력..."}, "badHabits": {"title": "우리 모두에게는 나쁜 습관이 있습니다. 당신의 것은 무엇인가요?", "smoking": "자주 흡연", "drinking": "자주 음주", "stayingUpLate": "자주 밤늦게 깨어있기", "overeating": "과식"}}}, "result": {"title": "고혈압 위험 평가 보고서", "description": "귀하의 건강 정보를 기반으로 한 맞춤형 평가", "reassess": "재평가", "riskAssessment": "I. 위험 수준 평가", "mainRiskFactors": "주요 위험 요인", "bmi": "BMI 지수", "currentBloodPressure": "현재 혈압", "systolic": "수축기", "diastolic": "이완기", "healthAnalysis": "II. 건강 상태 분석", "currentSymptoms": "(1) 현재 증상 추정", "managementAdvice": "관리 조언", "noSymptoms": "현재 뚜렷한 증상이 발견되지 않음", "organDamageRisk": "장기 손상 위험", "lowRisk": "낮은 위험", "moderateRisk": "중간 위험", "highRisk": "높은 위험", "possibleOrgans": "손상 가능 장기", "possibleComplications": "잠재적 합병증", "nonDrugPlan": "3. 비약물 관리 방안", "dietAdjustment": "(1) 식단 조정", "dietPlanLoading": "현재 지역에 맞춤화 중...", "customDietPlan": "맞춤형 식단 계획", "dietAdvice": "식단 조언", "dietRestriction": "식단 제한", "dietPlan": "구체적 식단 계획", "exerciseIntervention": "(2) 운동 개입", "exercisePlanLoading": "생성 중...", "customExercisePlan": "맞춤형 운동 계획", "recommendedExercise": "권장 운동 유형", "frequency": "빈도", "duration": "지속 시간", "precautions": "주의사항", "lifestyleAdjustment": "(3) 생활습관 조정", "sleepManagement": "수면 관리", "stressManagement": "스트레스 관리", "habitAdjustment": "습관 조정", "followUpAdvice": "추적 관리 조언", "monitoringIndicators": "모니터링 지표", "checkupAdvice": "검진 조언", "emergencyIndicators": "응급 의료 지표", "reassessConfirm": "⚠️ 재평가를 하시면 맞춤화된 모든 계획(식단 계획 및 운동 계획 포함)이 삭제됩니다. 계속하시겠습니까?", "loading": "건강 평가를 로딩 중입니다..."}, "dietPlan": {"title": "고혈압 환자 맞춤형 식단 계획", "subtitle": "위험 평가에 기반한 개인맞춤형 식단 방안", "planTitle": "고혈압 환자 맞춤형 식단 계획", "planDescription": "위험 평가에 기반한 개인맞춤형 식단 방안", "dailyMealPlan": "식단 계획", "calendarTitle": "식단 계획 달력", "calendarDescription": "여기서 완전한 30일 식단 계획을 확인하고 원하는 날짜를 선택할 수 있습니다.", "noDietPlanData": "제{day}일의 식단 계획 데이터가 없습니다.", "planStartsFromDay": "계획은 {day}일부터 시작, 총 {total}일", "noMonthPlan": "이번 달 계획 없음", "generatingProgress": "⏳ {current}/30일 생성됨", "weekDays": ["일", "월", "화", "수", "목", "금", "토"], "today": "오늘", "close": "닫기", "nutritionTips": {"saltReduction": "염분 섭취 감소로 혈압을 낮출 수 있습니다", "balancedNutrition": "균형 잡힌 영양이 혈압 조절에 도움됩니다", "regularMeals": "규칙적인 식사가 혈압 안정에 도움됩니다"}, "foodCategories": {"staples": "주식", "protein": "단백질", "vegetables": "채소", "fruits": "과일", "dairy": "유제품", "nuts": "견과류"}, "dailyOverview": {"title": "하루 영양 구성 개요", "nutritionMatch": "영양 구성", "totalCalories": "총 칼로리"}, "mealTimes": {"breakfast": "아침식사", "lunch": "점심식사", "dinner": "저녁식사", "snack": "간식"}, "foodItems": {"oatmeal": "오트밀", "boiledEgg": "삶은 달걀", "banana": "바나나", "apple": "사과", "rice": "밥", "chicken": "닭고기", "fish": "생선", "vegetables": "녹색 채소", "tofu": "두부", "milk": "우유", "yogurt": "요거트"}, "nutritionInfo": {"calories": "칼로리", "kcal": "kcal", "protein": "단백질", "carbs": "탄수화물", "fat": "지방", "fiber": "섬유질", "sodium": "나트륨", "potassium": "칼륨"}, "actions": {"deleteAndRegenerate": "삭제 후 재생성", "backToResults": "평가 결과로 돌아가기", "downloadPlan": "계획 다운로드", "sharePlan": "계획 공유", "viewMore": "더 많은 식단 계획 보기", "refresh": "새로고침"}, "timeFormats": {"morning": "7:30-8:00", "noon": "12:00-13:00", "evening": "18:30-19:30"}, "portions": {"times1": "×1", "times2": "×2", "times3": "×3", "serving": "인분", "cup": "컵", "piece": "개", "bowl": "그릇"}, "dayNumber": "제{day}일", "monthDay": "{month}월 {day}일", "combinedWith": ", {country}와 결합", "regionalSpecialty": "지역 특색", "clickTip": "📖 아무 요리나 클릭하여 자세한 조리법 확인 | 🍚주식 🥩단백질 🥬채소 🍎과일 🥜기타", "deleteConfirm": "현재 식단 계획을 삭제하시겠습니까? 삭제 후 새로운 식단 계획을 생성할 수 있습니다.", "deleteSuccess": "식단 계획이 삭제되었습니다. 새로운 계획을 생성할 수 있습니다", "deleteFailed": "삭제 실패:", "unknownError": "알 수 없는 오류", "networkError": "삭제 실패: 네트워크 오류"}, "exercisePlan": {"refresh": "새로고침", "calendarTitle": "운동 달력", "calendarDescription": "여기서 완전한 30일 운동 계획을 확인하고 원하는 날짜를 선택할 수 있습니다.", "planStartsFromDay": "운동 계획은 {day}일부터 시작, 총 {total}일", "noMonthPlan": "이번 달 운동 계획 없음, 다른 달을 확인할 수 있습니다", "generatingProgress": "⏳ 생성 중, {current}/30일 완료 (새로고침을 클릭하여 최신 진행 상황 확인)", "weekDays": ["일", "월", "화", "수", "목", "금", "토"], "today": "오늘", "legend": {"today": "오늘", "hasExercisePlan": "운동 계획 있음", "currentViewing": "현재 보고 있음"}, "noMonthExercise": "이번 달 운동 계획 없음", "noMonthExerciseDescription": "좌우 화살표를 사용하여 운동 계획이 있는 달로 전환하여 자세한 내용을 확인할 수 있습니다", "exerciseArrangement": "운동 계획", "totalDuration": "총 시간", "intensity": {"low": "저강도", "medium": "중강도", "high": "고강도"}, "heartRateControl": "심박수 조절", "targetHeartRate": "목표 심박수:", "calculationMethod": "계산 방법:", "specificActions": "구체적 동작:", "duration": "시간:", "sets": "세트 수:", "reps": "횟수:", "precautions": "주의사항", "requiredEquipment": "필요한 장비:", "dailyTips": "당일 팁", "exerciseGuidelines": "운동 주의사항", "exerciseContraindications": "운동 금기증", "progressiveAdvice": "점진적 권장사항", "generatedTime": "생성 시간:", "clickTip": "🏃‍♂️ 아무 운동 항목이나 클릭하여 자세한 동작 설명 확인", "monthPlanTitle": "운동 계획", "deleteConfirm": "현재 운동 계획을 삭제하시겠습니까? 삭제 후 새로운 계획을 생성할 수 있습니다.", "deleteSuccess": "운동 계획이 삭제되었습니다. 새로운 계획을 생성할 수 있습니다", "deleteFailed": "삭제 실패:", "unknownError": "알 수 없는 오류", "networkError": "삭제 실패: 네트워크 오류", "deleteAndRegenerate": "🗑️ 삭제 후 재생성", "backToResults": "← 평가 결과로 돌아가기", "close": "닫기", "exerciseActionDialog": {"intensity": {"low": "저강도", "medium": "중강도", "high": "고강도", "unknown": "알 수 없음"}, "actionDescription": "동작 설명", "detailedSteps": "상세 단계", "sets": "세트 수", "reps": "횟수", "duration": "시간", "heartRateControl": "심박수 조절", "targetHeartRate": "목표 심박수:", "calculationMethod": "계산 방법:", "monitoringMethod": "모니터링 방법:", "tips": "팁", "precautions": "주의사항", "requiredEquipment": "필요한 장비"}}, "toast": {"unlockFullDietPlan": "💡 완전한 30일 식단 계획 잠금 해제", "unlockFullDietPlanDesc": "현재 제1일 계획을 보고 계십니다. 구독자가 되어 전체 30일 개인맞춤형 콘텐츠를 잠금 해제하고 건강 목표를 달성하세요!", "unlockFullExercisePlan": "💡 완전한 30일 운동 계획 잠금 해제", "unlockFullExercisePlanDesc": "현재 제1일 계획을 보고 계십니다. 구독자가 되어 전체 30일 과학적 운동 지도를 잠금 해제하고 건강을 더욱 향상시키세요!", "subscribe": "구독하기", "dietPlanUpdated": "✅ 식단 계획이 업데이트되었습니다!", "dietPlanUpdatedDesc": "{count}일 추가됨, 현재 {total}/30일.", "exercisePlanUpdated": "✅ 운동 계획이 업데이트되었습니다!", "exercisePlanUpdatedDesc": "{count}일 추가됨, 현재 {total}/30일.", "stillGenerating": "⏳ 아직 생성 중...", "dietPlanGeneratingDesc": "식단 계획을 백그라운드에서 빠르게 생성 중, 현재 {completed}/30일 완료, 잠시 후 다시 시도해 주세요.", "exercisePlanGeneratingDesc": "운동 계획을 백그라운드에서 빠르게 생성 중, 현재 {completed}/30일 완료, 잠시 후 다시 시도해 주세요.", "planIsLatest": "👍 계획이 최신입니다", "dietPlanLatestDesc": "귀하의 식단 계획이 최신 버전입니다, {total}/30일.", "exercisePlanLatestDesc": "귀하의 운동 계획이 최신 버전입니다, {total}/30일.", "noPlanToRefresh": "ℹ️ 새로고침할 계획이 없습니다", "noDietPlanDesc": "식단 계획을 찾을 수 없습니다. 먼저 생성해 주세요.", "noExercisePlanDesc": "운동 계획을 찾을 수 없습니다. 먼저 생성해 주세요.", "generateDietPlanFailed": "식단 계획 생성 실패", "generateExercisePlanFailed": "운동 계획 생성 실패", "networkError": "네트워크 오류, 다시 시도해 주세요", "refreshFailed": "새로고침 실패, 다시 시도해 주세요", "dietPlanGenerating": "📋 식단 계획 생성 중", "dietPlanGeneratingInProgress": "백그라운드에서 더 완전한 식단 계획을 생성 중, 현재 {completed}/30일 완료, 기다려 주세요! 새로고침 버튼을 클릭하여 최신 진행 상황을 확인할 수 있습니다.", "exercisePlanGenerating": "🏃‍♂️ 운동 계획 생성 중", "exercisePlanGeneratingInProgress": "백그라운드에서 더 완전한 운동 계획을 생성 중, 현재 {completed}/30일 완료, 기다려 주세요! 새로고침 버튼을 클릭하여 최신 진행 상황을 확인할 수 있습니다.", "assessmentFailed": "평가 실패", "networkErrorRetry": "네트워크 오류, 다시 시도해 주세요"}, "riskLevels": {"low": "낮은 위험", "moderate": "중간 위험", "high": "높은 위험", "very_high": "매우 높은 위험", "unknown": "알 수 없음"}, "foodCategories": {"staples": "주식", "protein": "단백질", "vegetables": "채소", "fruits": "과일", "others": "기타", "nutritionMatch": "🍽️ 영양 구성", "viewRecipe": "클릭하여 자세한 조리법 확인"}, "foodRecipe": {"basicInfo": "기본 정보", "category": "분류:", "quantity": "용량:", "calories": "칼로리:", "ingredients": "재료 준비", "steps": "조리 단계", "tips": "핵심 팁", "nutritionValue": "영양 가치", "difficulty": {"easy": "쉬움", "medium": "보통", "hard": "어려움"}, "kcal": "kcal"}}
import { sql } from '@/lib/postgres-client'
import { HerbalWeightLossPlan } from '@/types/herbal-plan'

/**
 * 保存中医药方案到数据库
 */
export async function saveHerbalPlan(data: HerbalWeightLossPlan): Promise<number> {
  // 首先检查用户是否已有中药方案
  const existingPlan = await getLatestHerbalPlan(data.user_id)
  
  if (existingPlan) {
    // 用户已有方案，更新现有记录
    const { rows } = await sql`
      UPDATE nf_herbal_plans 
      SET 
        plan_type = ${data.plan_type},
        weekly_plan = ${JSON.stringify(data.weekly_plan)},
        tcm_diagnosis = ${data.tcm_diagnosis},
        constitution_type = ${data.constitution_type},
        treatment_principle = ${data.treatment_principle},
        expected_effects = ${data.expected_effects},
        precautions = ${data.precautions},
        basic_analysis = ${data.basic_analysis},
        herbal_recommendations = ${data.herbal_recommendations},
        updated_at = NOW()
      WHERE user_id = ${data.user_id}
      RETURNING id
    `
    
    console.log('=== 更新现有中药方案 ===', data.user_id)
    return rows[0].id
  } else {
    // 用户没有方案，插入新记录
    const { rows } = await sql`
      INSERT INTO nf_herbal_plans (
        user_id,
        plan_type,
        weekly_plan,
        tcm_diagnosis,
        constitution_type,
        treatment_principle,
        expected_effects,
        precautions,
        basic_analysis,
        herbal_recommendations,
        created_at,
        updated_at
      ) VALUES (
        ${data.user_id},
        ${data.plan_type},
        ${JSON.stringify(data.weekly_plan)},
        ${data.tcm_diagnosis},
        ${data.constitution_type},
        ${data.treatment_principle},
        ${data.expected_effects},
        ${data.precautions},
        ${data.basic_analysis},
        ${data.herbal_recommendations},
        NOW(),
        NOW()
      ) RETURNING id
    `
    
    console.log('=== 创建新中药方案 ===', data.user_id)
    return rows[0].id
  }
}

/**
 * 获取用户最新的中药方案
 */
export async function getLatestHerbalPlan(userId: string): Promise<HerbalWeightLossPlan | null> {
  const { rows } = await sql`
    SELECT * FROM nf_herbal_plans 
    WHERE user_id = ${userId}
    ORDER BY created_at DESC 
    LIMIT 1
  `
  
  if (rows.length === 0) {
    return null
  }
  
  const row = rows[0]
  
  // 修复数据：确保每个 dayPlan 都有 date 字段
  const fixedWeeklyPlan = fixWeeklyHerbalPlanData(row.weekly_plan)
  
  return {
    id: row.id,
    user_id: row.user_id,
    plan_type: row.plan_type,
    weekly_plan: fixedWeeklyPlan,
    tcm_diagnosis: row.tcm_diagnosis,
    constitution_type: row.constitution_type,
    treatment_principle: row.treatment_principle,
    expected_effects: row.expected_effects,
    precautions: row.precautions,
    basic_analysis: row.basic_analysis,
    herbal_recommendations: row.herbal_recommendations,
    created_at: row.created_at,
    updated_at: row.updated_at
  }
}

/**
 * 检查用户是否已有中药方案
 */
export async function hasHerbalPlan(userId: string): Promise<boolean> {
  const { rows } = await sql`
    SELECT COUNT(*) as count FROM nf_herbal_plans 
    WHERE user_id = ${userId}
  `
  
  return parseInt(rows[0].count) > 0
}

/**
 * 删除用户的中药方案
 */
export async function deleteHerbalPlan(userId: string): Promise<boolean> {
  const { rowCount } = await sql`
    DELETE FROM nf_herbal_plans 
    WHERE user_id = ${userId}
  `
  
  return (rowCount || 0) > 0
}

/**
 * 修复周中药方案数据，确保每个 dayPlan 都有 date 字段
 */
function fixWeeklyHerbalPlanData(weeklyPlan: any): any {
  if (!weeklyPlan || typeof weeklyPlan !== 'object') {
    return weeklyPlan
  }

  const dayMapping: { [key: string]: string } = {
    'day1': '周一',
    'day2': '周二', 
    'day3': '周三',
    'day4': '周四',
    'day5': '周五',
    'day6': '周六',
    'day7': '周日'
  }

  const fixedPlan = { ...weeklyPlan }

  // 遍历每一天的计划
  Object.keys(fixedPlan).forEach(dayKey => {
    const dayPlan = fixedPlan[dayKey]
    if (dayPlan && typeof dayPlan === 'object') {
      // 如果没有 date 字段或者 date 为空，则添加默认值
      if (!dayPlan.date) {
        dayPlan.date = dayMapping[dayKey] || dayKey
      }
    }
  })

  return fixedPlan
}

import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import {
  hasHealthAssessmentReport,
  getLatestHealthAssessmentReport
} from '@/lib/db/health-assessment'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: '缺少用户ID参数' },
        { status: 400 }
      )
    }

    const hasReport = await hasHealthAssessmentReport(userId)
    
    if (!hasReport) {
      return NextResponse.json({
        success: true,
        hasReport: false,
        report: null
      })
    }

    const latestReport = await getLatestHealthAssessmentReport(userId)

    return NextResponse.json({
      success: true,
      hasReport: true,
      report: latestReport
    })

  } catch (error) {
    console.error('检查健康报告时出错:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '检查报告时发生未知错误',
        success: false 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取当前用户信息
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })
    
    // 只允许登录用户查询报告
    if (!token?.userId) {
      return NextResponse.json({
        success: true,
        hasReport: false,
        report: null
      })
    }

    const userId = token.userId as string
    console.log('=== 查询登录用户报告 ===', userId)
    
    const hasReport = await hasHealthAssessmentReport(userId)
    let latestReport = null
    
    if (hasReport) {
      latestReport = await getLatestHealthAssessmentReport(userId)
    }

    return NextResponse.json({
      success: true,
      hasReport: hasReport,
      report: latestReport
    })

  } catch (error) {
    console.error('检查健康报告时出错:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '检查报告时发生未知错误',
        success: false 
      },
      { status: 500 }
    )
  }
} 
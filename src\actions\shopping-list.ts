'use server'

import { 
  getLatestShoppingList, 
  hasShoppingList, 
  deleteShoppingList,
  updateShoppingListItems,
  addShoppingListItem,
  removeShoppingListItem,
  markItemAsPurchased
} from '@/lib/db/shopping-list'
import { 
  ShoppingList, 
  ShoppingListItem, 
  ShoppingListResponse,
  ShoppingListGenerateRequest 
} from '@/types/shopping-list'

/**
 * 获取用户的购物清单
 */
export async function getUserShoppingList(userId: string): Promise<ShoppingList | null> {
  try {
    return await getLatestShoppingList(userId)
  } catch (error) {
    console.error('获取用户购物清单失败:', error)
    return null
  }
}

/**
 * 检查用户是否有购物清单
 */
export async function checkUserHasShoppingList(userId: string): Promise<boolean> {
  try {
    return await hasShoppingList(userId)
  } catch (error) {
    console.error('检查用户购物清单失败:', error)
    return false
  }
}

/**
 * 生成购物清单
 */
export async function generateShoppingList(
  userId: string, 
  includeDietPlan: boolean = true, 
  includeHerbalPlan: boolean = true
): Promise<ShoppingListResponse> {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/generate-shopping-list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        includeDietPlan,
        includeHerbalPlan
      } as ShoppingListGenerateRequest)
    })

    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: result.error || '生成购物清单失败'
      }
    }

    return {
      success: true,
      shoppingList: result.shoppingList
    }
  } catch (error) {
    console.error('生成购物清单失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '生成购物清单失败'
    }
  }
}

/**
 * 更新购物清单项目
 */
export async function updateShoppingList(userId: string, items: ShoppingListItem[]): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/generate-shopping-list`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        items
      })
    })

    const result = await response.json()
    return result.success || false
  } catch (error) {
    console.error('更新购物清单失败:', error)
    return false
  }
}

/**
 * 添加购物清单项目
 */
export async function addItemToShoppingList(userId: string, item: ShoppingListItem): Promise<boolean> {
  try {
    return await addShoppingListItem(userId, item)
  } catch (error) {
    console.error('添加购物清单项目失败:', error)
    return false
  }
}

/**
 * 删除购物清单项目
 */
export async function removeItemFromShoppingList(userId: string, itemName: string): Promise<boolean> {
  try {
    return await removeShoppingListItem(userId, itemName)
  } catch (error) {
    console.error('删除购物清单项目失败:', error)
    return false
  }
}

/**
 * 标记项目为已购买
 */
export async function markShoppingItemAsPurchased(userId: string, itemName: string): Promise<boolean> {
  try {
    return await markItemAsPurchased(userId, itemName)
  } catch (error) {
    console.error('标记购物项目失败:', error)
    return false
  }
}

/**
 * 删除用户的购物清单
 */
export async function removeUserShoppingList(userId: string): Promise<boolean> {
  try {
    return await deleteShoppingList(userId)
  } catch (error) {
    console.error('删除用户购物清单失败:', error)
    return false
  }
}

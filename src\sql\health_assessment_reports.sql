-- 健康评估报告表
DROP TABLE IF EXISTS "public"."nf_health_assessment_reports";
CREATE TABLE "public"."nf_health_assessment_reports" (
  "id" int4 NOT NULL DEFAULT nextval('nf_health_assessments_id_seq'::regclass),
  "user_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "user_session_id" varchar(255) COLLATE "pg_catalog"."default",
  "audience_group" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "form_data" jsonb NOT NULL,
  "user_data_summary" text COLLATE "pg_catalog"."default" NOT NULL,
  "report_data" jsonb NOT NULL,
  "basic_analysis" text COLLATE "pg_catalog"."default",
  "tcm_constitution" text COLLATE "pg_catalog"."default",
  "health_risks" text COLLATE "pg_catalog"."default",
  "diet_recommendations" text COLLATE "pg_catalog"."default",
  "herbal_recommendations" text COLLATE "pg_catalog"."default",
  "lifestyle_adjustments" text COLLATE "pg_catalog"."default",
  "short_term_goals" text COLLATE "pg_catalog"."default",
  "long_term_plan" text COLLATE "pg_catalog"."default",
  "ip_address" varchar(50) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."user_id" IS '用户ID（可能是Clerk ID或session ID）';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."user_session_id" IS '用户会话ID（用于未登录用户）';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."audience_group" IS '用户选择的群体类型：weight-loss, fitness, white-collar';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."form_data" IS '用户填写的完整表单数据';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."user_data_summary" IS '发送给AI的用户数据摘要';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."report_data" IS '完整的AI生成报告数据（JSON格式）';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."basic_analysis" IS '基本情况分析';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."tcm_constitution" IS '中医体质诊断';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."health_risks" IS '健康风险预警';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."diet_recommendations" IS '饮食调理建议';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."herbal_recommendations" IS '中药调理方案';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."lifestyle_adjustments" IS '生活方式调整';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."short_term_goals" IS '短期目标';
COMMENT ON COLUMN "public"."nf_health_assessment_reports"."long_term_plan" IS '长期养生计划';
COMMENT ON TABLE "public"."nf_health_assessment_reports" IS '健康评估报告表';

-- 创建索引
CREATE INDEX "idx_health_reports_user_id" ON "public"."nf_health_assessment_reports" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_health_reports_session_id" ON "public"."nf_health_assessment_reports" USING btree (
  "user_session_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_health_reports_created_at" ON "public"."nf_health_assessment_reports" USING btree (
  "created_at" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- 主键
ALTER TABLE "public"."nf_health_assessment_reports" ADD CONSTRAINT "nf_health_assessment_reports_pkey" PRIMARY KEY ("id"); 
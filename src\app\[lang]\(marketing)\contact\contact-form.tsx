'use client'

import { useState } from "react"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useToast } from "@/hooks/use-toast"
import { ContactFormLocale, FormFields } from "@/types/locales/contact"
import { createLocalizedContactSchema } from "@/lib/validations/contact"
import { addContactInfo } from "@/actions/contact"

export function ContactForm({ formLabels }: { formLabels: ContactFormLocale }) {
  const [isPending, setIsPending] = useState(false)
  const { toast } = useToast()

  // 使用本地化的验证模式
  const localizedSchema = createLocalizedContactSchema(formLabels.fields)

  const form = useForm<z.infer<typeof localizedSchema>>({
    resolver: zod<PERSON>esolver(localizedSchema),
    defaultValues: {
      name: "",
      email: "",
      message: "",
    },
  })

  async function onSubmit(values: z.infer<typeof localizedSchema>) {
    try {
      setIsPending(true)
      const result = await addContactInfo(values)

      if (result?.success) {
        console.log(result)
        toast({
          title: "Success",
          description: 'Your message has been submitted,We value your feedback as it empowers us to enhance our tools.',
          variant: "default",
          duration: 3000
        })
        form.reset()
      } else {
        toast({
          title: "Error",
          description: 'Failed to submit message. Please try again.',
          variant: "destructive",
          duration: 3000
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit form. Please try again later.",
        variant: "destructive",
        duration: 3000
      })
    } finally {
      setIsPending(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{formLabels.fields.name.label}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={formLabels.fields.name.placeholder}
                    {...field}
                    className="border-primary-gold/20 focus:border-primary-gold"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{formLabels.fields.email.label}</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={formLabels.fields.email.placeholder}
                    {...field}
                    className="border-primary-gold/20 focus:border-primary-gold"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{formLabels.fields.message.label}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={formLabels.fields.message.placeholder}
                  className="min-h-[160px] border-primary-gold/20 focus:border-primary-gold"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          disabled={isPending}
          className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-6 
            dark:bg-primary/80 dark:hover:bg-primary disabled:opacity-50"
        >
          {isPending ? formLabels.submitting : formLabels.submitButton}
        </Button>
      </form>
    </Form>
  )
}
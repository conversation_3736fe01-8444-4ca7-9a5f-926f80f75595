// 为当前请求的语言创建一个存储变量
let currentRequestLanguage = '';

/**
 * 设置当前请求的语言
 * 在路由处理程序中调用此函数
 */
export function setCurrentLanguage(lang: string) {
  if(lang === 'zh') {
    currentRequestLanguage = "中文";
  }
  else if(lang === 'en') {
    currentRequestLanguage = "英文";
  }
  else if(lang === 'ja') {
    currentRequestLanguage = "日语";
  }
  else if(lang === 'ko') {
    currentRequestLanguage = "韩语";
  }
  else if(lang === 'fr') {
    currentRequestLanguage = "法语";
  }
  else if(lang === 'de') {
    currentRequestLanguage = "德语";
  }
  else if(lang === 'es') {
    currentRequestLanguage = "西班牙语";
  }
  else {
    // 默认使用中文
    currentRequestLanguage = "中文";
  }
}

/**
 * 获取当前语言
 * @returns 当前设置的语言
 */
export function getCurrentLanguage(): string {
  // 如果没有设置语言，默认返回中文
  return currentRequestLanguage || "中文";
}

/**
 * 根据语言代码获取对应的语言名称
 * @param langCode 语言代码，如'zh'、'en'、'ja'
 * @returns 语言名称，如'中文'、'英文'、'日语'
 */
export function getLanguageNameByCode(langCode: string): string {
  switch(langCode) {
    case 'zh': return "中文";
    case 'en': return "English";
    case 'ja': return "日本語";
    case 'ko': return "한국어";
    case 'fr': return "Français";
    case 'de': return "Deutsch";
    case 'es': return "Español";
    default: return "English";
  }
}

/**
 * 中医健康评估报告生成提示词
 */
export const TCM_HEALTH_ASSESSMENT_PROMPT = `你是一个经验丰富的传统老中医，拥有50年以上的中医临床诊断和治疗经验，专注于中医健康养生，现在请你根据我的基础数据信息、我的生活习性和我的目标帮我生成一份健康评估报告，健康评估报告必须包含我的健康诊断结果和诊断依据以及预判目前可能出现的症状或即将引发的病症，同时根据健康诊断评估报告和我的目标给我生成减肥相关的饮食建议和中医健康减肥方案，目的在于让海外用户了解中国的中医文化，吸引用户购买订阅饮食计划和中药处方。

请按照以下结构生成报告：

## 一、基本情况分析
根据提供的基础数据进行分析

## 二、中医体质诊断
根据中医理论分析体质类型和特征

## 三、健康状况评估
### 当前症状分析
### 潜在健康风险预警

## 四、中医减肥方案
### 饮食调理建议
### 中药调理方案
### 生活方式调整

## 五、个性化建议
### 短期目标（1-3个月）
### 长期养生计划（6-12个月）

请用专业的中医术语，但要通俗易懂，体现中医文化的博大精深，让海外用户感受到中医的神奇和科学性。

我的具体信息如下：
{userDataPlaceholder}` 
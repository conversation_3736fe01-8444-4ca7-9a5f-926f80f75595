import React, { useCallback, useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { useSession } from 'next-auth/react';
import { findUserCreditsByUserId } from "@/actions/user";

import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout
} from '@stripe/react-stripe-js';


interface CheckoutProps{
  priceId:string;
  mode:string;
  className?: string;
}
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function CheckoutForm({priceId,mode,className}:CheckoutProps) {

  const { data: session } = useSession();
  const [isReady, setIsReady] = useState(false);
  const [isUpdatingCredits, setIsUpdatingCredits] = useState(false);
  const [currentCredits, setCurrentCredits] = useState<number>(0);

  // 等待用户认证状态完全加载
  useEffect(() => {
    if (session?.user?.id && session?.user?.email) {
      console.log('用户认证状态已就绪:', { userId: session.user.id, userEmail: session.user.email });
      setIsReady(true);
      // 初始加载积分
      loadCredits();
    } else {
      console.log('等待用户认证状态加载...', session);
      setIsReady(false);
    }
  }, [session]);

  const loadCredits = async () => {
    if (session?.user?.id) {
      const credits = await findUserCreditsByUserId(session.user.id, true);
      setCurrentCredits(credits);
    }
  };

  async function handlePaymentSuccess(){
    console.log('支付成功，开始更新积分');
    setIsUpdatingCredits(true);
    
    try {
      // 延迟重试机制，确保 webhook 有时间处理
      const maxRetries = 3;
      const initialDelay = 3000; // 3秒初始延迟
      
      for (let i = 0; i < maxRetries; i++) {
        try {
          console.log(`第 ${i + 1} 次尝试更新积分...`);
          
          // 延迟等待（逐渐增加延迟时间）
          const delay = initialDelay + (i * 2000); // 3s, 5s, 7s
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // 获取最新积分
          const credits = await findUserCreditsByUserId(session?.user?.id, true);
          console.log(`第 ${i + 1} 次获取积分结果:`, credits);
          
          // 如果积分大于原来的值，说明 webhook 已经处理完成
          if (credits > currentCredits) {
            console.log('检测到积分已更新，停止重试');
            await updateCredits();
            return;
          }
        } catch (error) {
          console.error(`第 ${i + 1} 次尝试失败:`, error);
        }
        
        // 最后一次尝试
        if (i === maxRetries - 1) {
          console.log('达到最大重试次数，执行最终更新');
          await updateCredits();
        }
      }
    } finally {
      setIsUpdatingCredits(false);
    }
  }

  const fetchClientSecret = useCallback(() => {
    console.log('=== Stripe fetchClientSecret 调试信息 ===');
    console.log('session对象:', session);
    console.log('用户ID:', session?.user?.id);
    console.log('用户邮箱:', session?.user?.email);
    console.log('isReady:', isReady);
    
    if (!session?.user?.id) {
      console.error('用户ID不存在，请先登录');
      return Promise.reject(new Error('用户ID不存在，请先登录'));
    }
    
    if (!session?.user?.email) {
      console.error('用户邮箱不存在');
      return Promise.reject(new Error('用户邮箱不存在'));
    }
    
    console.log('开始创建 Stripe 结账会话...');
    // Create a Checkout Session
    return fetch("/api/stripe", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        priceId: priceId, 
        userId: session.user.id,
        type: mode === "payment" ? "1" : "2",
        customerEmail: session.user.email
      }),
    })
      .then((res) => {
        console.log('Stripe API 响应状态:', res.status);
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json();
      })
      .then((data) => {
        console.log('Stripe API 响应数据:', data);
        if (data.error) {
          throw new Error(data.error);
        }
        return data.clientSecret;
      })
      .catch((error) => {
        console.error('创建 Stripe 会话失败:', error);
        throw error;
      });
  }, [session?.user?.id, session?.user?.email, priceId, mode, isReady]);

  const options = {
    fetchClientSecret,
    onComplete: handlePaymentSuccess
  };

  async function updateCredits() {
    const credits = await findUserCreditsByUserId(session?.user?.id, true); // 强制刷新
    console.log('支付成功后获取到的积分:', credits);
    setCurrentCredits(credits);
    
    // 触发全局状态刷新
    window.dispatchEvent(new CustomEvent('creditsUpdated', { detail: { credits } }));
  }

  // 如果用户认证状态未就绪，显示加载状态
  if (!isReady) {
    return (
      <div className="w-full h-[600px] md:h-full md:min-w-[992px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">正在加载支付信息...</p>
          <p className="text-sm text-muted-foreground mt-2">
            用户ID: {session?.user?.id || '加载中...'}
          </p>
          <p className="text-sm text-muted-foreground">
            邮箱: {session?.user?.email || '加载中...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
    <EmbeddedCheckoutProvider
      stripe={stripePromise}
      options={options}
    >
      <EmbeddedCheckout className={`w-full h-[600px] md:h-full md:min-w-[992px] overflow-auto`}/>
    </EmbeddedCheckoutProvider>
      
      {/* 积分更新状态提示 */}
      {isUpdatingCredits && (
        <div className="fixed top-4 right-4 bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
          <span className="text-sm">正在更新积分...</span>
        </div>
      )}
    </>
  )
}
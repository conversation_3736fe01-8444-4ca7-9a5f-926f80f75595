'use server'
import { getCurrentUser } from '@/lib/auth';
import { sql } from '@/lib/postgres-client';
import { UserOrder, UserSubscription } from '@/types/order';
import { cancelSubscriptionById } from "@/actions/stripe-billing";

export async function findOrderByUserId(): Promise<UserOrder[]> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return [];
        }

        const { rows } = await sql`
            SELECT 
                order_number,
                credit_amount,
                order_price,
                order_date,
                credit_type,
                created_at
            FROM nf_credits where credit_type in ('1','2','3') and user_id = ${user.userId}`;

        return rows.map(row => ({
            orderId: row.order_number,
            credits: row.credit_amount,
            price: row.order_price,
            date: row.order_date || row.created_at,
            type: row.credit_type === '1' ? 'subscription' :
                row.credit_type === '2' ? 'one-time' : '',
            status: row.credit_type === '1' || row.credit_type === '2' ? 'completed' :
                row.credit_type === '3' ? 'refunded' : '',
        }));

    } catch (error) {
        console.error('Failed to get user orders:', error);
        return [];
    }
}

export async function findSubscriptionByUserId(): Promise<UserSubscription[]> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return [];
        }

        const { rows } = await sql`
        SELECT 
            subscription_id,
            credit_amount,
            order_price,
            order_date
        FROM nf_subscription where order_type = '1' and user_id = ${user.userId}`;

        return rows.map(row => {
            // 计算下次续订时间
            const orderDate = new Date(row.order_date);
            const today = new Date();
            const daysSinceOrder = Math.floor((today.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
            // 根据价格判断是月度还是年度订阅
            const isAnnual = Number(row.order_price) === 79.99;
            const periodDays = isAnnual ? 365 : 30;
            const periodsElapsed = Math.floor(daysSinceOrder / periodDays);
            const nextRenewalDate = new Date(orderDate);
            nextRenewalDate.setDate(orderDate.getDate() + ((periodsElapsed + 1) * periodDays));

            return {
                orderId: row.subscription_id,
                credits: row.credit_amount,
                price: row.order_price,
                date: row.order_date,
                renewalDate: nextRenewalDate.toISOString(),
            };
        });

    } catch (error) {
        console.error('Failed to get user subscriptions:', error);
        return [];
    }
}

export async function updateSubscriptionByOrderId(subscriptionId: string) {
    const result = await cancelSubscriptionById(subscriptionId);
    return result;
}

export async function getSubscriptionByUserId(userId: string) {
    try {
        const { rows } = await sql`
            SELECT 
                subscription_id,
                order_type
            FROM nf_subscription 
            WHERE user_id = ${userId} 
            ORDER BY order_date DESC 
            LIMIT 1`;

        if (rows.length > 0) {
            return rows[0];
        }
        return null;
    } catch (error) {
        console.error('Failed to get user subscription by userId:', error);
        return null;
    }
}

// 兼容性函数（将会被弃用）
export async function getSubscriptionByClerkId(clerkId: string) {
    console.warn('getSubscriptionByClerkId is deprecated, use getSubscriptionByUserId instead');
    return getSubscriptionByUserId(clerkId);
}

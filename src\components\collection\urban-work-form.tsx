'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react'
import Link from 'next/link'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group'
import { cn } from '@/lib/utils'
import { DatePicker } from '@/components/date-picker'
import { WeightGauge } from '@/components/weight-gauge'
import { useRouter } from 'next/navigation'

// 表单验证 Schema
const weightLossFormSchema = z.object({
  // 身高
  height: z.string().min(1, { message: '请输入您的身高' }),
  heightUnit: z.enum(['cm', 'inch'], {
    required_error: '请选择身高单位',
  }),
  
  // 体重
  weight: z.string().min(1, { message: '请输入您的体重' }),
  weightUnit: z.enum(['kg', 'lb'], {
    required_error: '请选择体重单位',
  }),
  
  // 体脂率
  bodyFatPercentage: z.string().optional(),
  knowsBodyFat: z.enum(['yes', 'no'], {
    required_error: '请选择是否知道体脂率',
  }),
  
  // 出生日期（替代年龄）
  birthDate: z.date({
    required_error: '请选择您的出生日期',
  }),
  
  // 性别
  gender: z.enum(['male', 'female', 'other'], {
    required_error: '请选择您的性别',
  }),
  
  // 工作性质
  workNature: z.enum(['sedentary', 'light', 'moderate', 'active'], {
    required_error: '请选择您的工作性质',
  }),
  
  // 每日步行步数
  dailySteps: z.enum(['less2000', '2000to5000', '5000to10000', 'more10000'], {
    required_error: '请选择您的每日步行步数范围',
  }),
  
  // 健康目标类型
  healthGoalType: z.enum(['maintain', 'mild_weight_loss', 'vitality'], {
    required_error: '请选择您的健康目标',
  }),
  
  // 体重减少数值(仅当选择轻度减脂时需要)
  weightLossAmount: z.string().optional(),
  
  // 目标达成时间(仅当选择轻度减脂时需要)
  goalAchieveTime: z.string().optional(),
  
  // 是否因工作压力导致体重波动
  isWorkPressureAffectWeight: z.enum(['yes', 'no', 'not_sure'], {
    required_error: '请选择是否因工作压力导致体重波动',
  }),
  
  // 健康目标 - 期望减重数值
  weightLossGoal: z.string().min(1, { message: '请输入您期望减少的体重' }),
  weightLossGoalUnit: z.enum(['kg', 'lb', 'bodyFat'], {
    required_error: '请选择减重单位',
  }),
  
  // 健康目标 - 期望达成时间
  goalTimeframe: z.string().min(1, { message: '请选择您期望达成目标的时间' }),
  
  // 节食经历
  hasDietHistory: z.enum(['yes', 'no'], {
    required_error: '请选择是否有过盲目节食经历',
  }),
  
  // 节食后反应（多选）
  dietReactions: z.array(z.string()).optional(),
  
  // 饮食偏好
  favoriteFoods: z.array(z.string()).optional(),
  dislikedFoods: z.array(z.string()).optional(),
  religiousDietaryRestrictions: z.enum(['none', 'halal', 'kosher', 'hindu', 'buddhist', 'other']).optional(),
  personalDietaryRestrictions: z.array(z.string()).optional(),
  dietaryRestrictions: z.array(z.string()).optional(),
  
  // 常点的外卖类型
  takeoutTypes: z.array(z.string()).optional(),
  
  // 对方便快捷烹饪食材的偏好
  quickCookingPreference: z.array(z.string()).optional(),
  
  // 可接受的每餐烹饪时间
  acceptableCookingTime: z.enum(['within15', 'within30', 'within60', 'over60'], {
    required_error: '请选择您可接受的烹饪时间',
  }),
  
  lowCalorieAcceptance: z.enum(['high', 'medium', 'low'], {
    required_error: '请选择您对低热量、低脂肪食材的接受度',
  }),
  unhealthyFoodPreference: z.array(z.string()).optional(),
  eatingOutFrequency: z.enum(['0-1', '2-3', '4-5', '6+'], {
    required_error: '请选择您每周在外就餐的次数',
  }),
  medicinalDietPlan: z.enum(['daily', 'threedays', 'weekly', 'monthly', 'none'], {
    required_error: '请选择您是否愿意加入药膳饮食计划',
  }),
  
  // 饮食习惯
  // 早餐是否经常不吃
  skipBreakfast: z.enum(['often', 'sometimes', 'rarely'], {
    required_error: '请选择您是否经常不吃早餐',
  }),
  
  // 午餐是否在公司食堂或外卖解决
  lunchSolution: z.enum(['canteen', 'takeout', 'homemade', 'mixed'], {
    required_error: '请选择您的午餐解决方式',
  }),
  
  // 晚餐用餐时间是否固定
  dinnerTimeRegular: z.enum(['regular', 'irregular'], {
    required_error: '请选择您的晚餐用餐时间是否固定',
  }),
  
  // 是否有下午茶习惯
  afternoonTeaHabit: z.enum(['yes', 'no', 'sometimes'], {
    required_error: '请选择您是否有下午茶习惯',
  }),
  
  regularMeals: z.enum(['regular', 'irregular'], {
    required_error: '请选择您的三餐是否规律',
  }),
  snackingHabit: z.enum(['yes', 'no'], {
    required_error: '请选择您是否有吃零食的习惯',
  }),
  snackTypes: z.array(z.string()).optional(),
  eatingSpeed: z.enum(['fast', 'medium', 'slow'], {
    required_error: '请选择您的用餐速度',
  }),
  foodAllergies: z.array(z.string()).optional(),
  chronicDiseases: z.array(z.string()).optional(),
  medications: z.array(z.string()).optional(),
  sleepQuality: z.enum(['good', 'fair', 'poor', 'irregular']).optional(),
  spineDiscomfort: z.enum(['yes', 'no', 'sometimes']).optional(),
  fatigue: z.enum(['yes', 'no', 'sometimes']).optional(),
  digestiveIssues: z.enum(['yes', 'no', 'sometimes']).optional(),
  workStressLevel: z.enum(['low', 'medium', 'high']).optional(),
  sweating: z.enum(['yes', 'no', 'onlyExercise']).optional(),
  backPain: z.enum(['yes', 'no', 'sometimes']).optional(),
  appetite: z.enum(['yes', 'no', 'normal']).optional(),
  menstrualRegularity: z.enum(['yes', 'no', 'sometimes']).optional(),
})

// 定义表单数据类型
type WeightLossFormValues = z.infer<typeof weightLossFormSchema>

// 组件属性定义
interface UrbanWorkFormProps {
  lang: string;
  onBack?: () => void; // 返回上一级组件的回调函数
}

// 定义选项类型
interface FormOption {
  value: string;
  label: string;
  icon?: string;
}

// 定义表单字段类型
interface FormField {
  id: string;
  label: string;
  type: string;
  placeholder?: string;
  options?: FormOption[];
  description?: string;
}

// 定义表单部分类型
interface FormSection {
  title: string;
  fields: FormField[];
}

// 定义步骤配置类型
interface FormStep {
  id: string;
  title: string;
  type?: string;
  options?: FormOption[];
  units?: { value: string; label: string }[];
  placeholder?: string;
  description?: string;
  sections?: FormSection[];
  fields?: FormField[];
}

// 步骤配置
const formSteps: FormStep[] = [
  {
    id: 'gender',
    title: '您的性别是？',
    options: [
      { value: 'male', label: '男性', icon: '👨' },
      { value: 'female', label: '女性', icon: '👩' },
      { value: 'other', label: '其他/不便透露', icon: '🧑' },
    ]
  },
  {
    id: 'birthDate',
    title: '您的出生日期是？',
    type: 'birthDate',
    description: '我们将根据您的出生日期计算年龄'
  },
  {
    id: 'height',
    title: '您的身高是？',
    type: 'input',
    placeholder: '请输入身高',
    units: [
      { value: 'cm', label: '厘米 (cm)' },
      { value: 'inch', label: '英寸 (inch)' },
    ]
  },
  {
    id: 'weight',
    title: '您的体重是？',
    type: 'input',
    placeholder: '请输入体重',
    units: [
      { value: 'kg', label: '千克 (kg)' },
      { value: 'lb', label: '磅 (lb)' },
    ]
  },
  {
    id: 'knowsBodyFat',
    title: '您是否知道自己的体脂率？',
    options: [
      { value: 'yes', label: '是的，我知道', icon: '✅' },
      { value: 'no', label: '不，我不清楚', icon: '❓' },
    ]
  },
  {
    id: 'workNature',
    title: '您的工作性质是？',
    options: [
      { value: 'sedentary', label: '久坐办公室', icon: '💻' },
      { value: 'light', label: '轻度活动（站立或走动较多）', icon: '🚶' },
      { value: 'moderate', label: '中度活动（定期体力劳动）', icon: '🔧' },
      { value: 'active', label: '重度活动（频繁体力劳动）', icon: '🏋️' },
    ],
    description: '了解您的工作性质有助于我们评估您的基础代谢率和制定合适的运动计划'
  },
  {
    id: 'dailySteps',
    title: '您的每日步行步数大约是？',
    options: [
      { value: 'less2000', label: '少于2000步', icon: '🐢' },
      { value: '2000to5000', label: '2000-5000步', icon: '🚶' },
      { value: '5000to10000', label: '5000-10000步', icon: '🚶‍♂️' },
      { value: 'more10000', label: '10000步以上', icon: '🏃' },
    ],
    description: '步行是最基础的身体活动，了解您的日常活动量有助于定制饮食计划'
  },
  {
    id: 'healthGoalType',
    title: '您的健康目标是什么？',
    options: [
      { value: 'maintain', label: '维持健康体重', icon: '⚖️' },
      { value: 'mild_weight_loss', label: '轻度减脂', icon: '📉' },
      { value: 'vitality', label: '提升身体活力', icon: '✨' },
    ],
    description: '不同的健康目标需要不同的饮食和活动计划'
  },
  {
    id: 'weightLossAmount',
    title: '您希望减少多少体重？',
    type: 'input',
    placeholder: '请输入目标数值',
    units: [
      { value: 'kg', label: '千克 (kg)' },
    ],
    description: '对于办公室工作者，建议设定合理的减重目标，如3-5千克'
  },
  {
    id: 'goalAchieveTime',
    title: '您期望在多长时间内达成目标？',
    type: 'timeframe',
    placeholder: '请选择时间范围（1-6个月）',
    description: '健康减重的速度建议为每月1-2千克，过快的减重可能不利于健康'
  },
  {
    id: 'isWorkPressureAffectWeight',
    title: '您是否因工作压力导致体重波动？',
    options: [
      { value: 'yes', label: '是的，明显相关', icon: '😓' },
      { value: 'no', label: '没有明显关系', icon: '😌' },
      { value: 'not_sure', label: '不确定', icon: '🤔' },
    ],
    description: '工作压力与饮食习惯、体重变化往往密切相关'
  },
  // 饮食偏好 - 喜欢的食材
  {
    id: 'favoriteFoods',
    title: '您喜欢哪些食材？',
    type: 'multiSelect',
    options: [
      { value: 'meat', label: '肉类（牛肉、猪肉等）', icon: '🥩' },
      { value: 'poultry', label: '禽类（鸡肉、鸭肉等）', icon: '🍗' },
      { value: 'fish', label: '鱼类', icon: '🐟' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'dairy', label: '奶制品', icon: '🧀' },
      { value: 'vegetables', label: '蔬菜', icon: '🥦' },
      { value: 'fruits', label: '水果', icon: '🍎' },
      { value: 'grains', label: '谷物（米饭、面食等）', icon: '🍚' },
      { value: 'beans', label: '豆类', icon: '🫘' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
    ],
    description: '了解您喜欢的食材有助于我们为您定制更符合口味的饮食计划'
  },
  // 饮食偏好 - 不喜欢的食材
  {
    id: 'dislikedFoods',
    title: '您不喜欢哪些食材？',
    type: 'multiSelect',
    options: [
      { value: 'meat', label: '肉类（牛肉、猪肉等）', icon: '🥩' },
      { value: 'poultry', label: '禽类（鸡肉、鸭肉等）', icon: '🍗' },
      { value: 'fish', label: '鱼类', icon: '🐟' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'dairy', label: '奶制品', icon: '🧀' },
      { value: 'vegetables', label: '蔬菜', icon: '🥦' },
      { value: 'fruits', label: '水果', icon: '🍎' },
      { value: 'grains', label: '谷物（米饭、面食等）', icon: '🍚' },
      { value: 'beans', label: '豆类', icon: '🫘' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
      { value: 'none', label: '没有特别不喜欢的食材', icon: '✅' },
    ],
    description: '了解您不喜欢的食材有助于我们避免在饮食计划中包含这些食材'
  },
  // 饮食偏好 - 宗教饮食限制
  {
    id: 'religiousDietaryRestrictions',
    title: '您是否有宗教相关的饮食限制？',
    options: [
      { value: 'none', label: '无宗教饮食限制', icon: '✅' },
      { value: 'halal', label: '清真饮食 (伊斯兰教)', icon: '🌙' },
      { value: 'kosher', label: '洁食 (犹太教)', icon: '✡️' },
      { value: 'hindu', label: '印度教饮食限制', icon: '🕉️' },
      { value: 'buddhist', label: '佛教饮食限制', icon: '☸️' },
      { value: 'other', label: '其他宗教饮食限制', icon: '🙏' },
    ],
    description: '了解您的宗教饮食限制有助于我们为您提供符合要求的饮食计划'
  },
  // 饮食偏好 - 个人忌口
  {
    id: 'personalDietaryRestrictions',
    title: '您有哪些个人忌口？',
    type: 'multiSelect',
    options: [
      { value: 'vegetarian', label: '素食', icon: '🥗' },
      { value: 'vegan', label: '纯素', icon: '🌱' },
      { value: 'noBeef', label: '不吃牛肉', icon: '🐄' },
      { value: 'noPork', label: '不吃猪肉', icon: '🐖' },
      { value: 'noSeafood', label: '不吃海鲜', icon: '🦐' },
      { value: 'noSpicy', label: '不吃辛辣食物', icon: '🌶️' },
      { value: 'lowSalt', label: '低盐饮食', icon: '🧂' },
      { value: 'lowSugar', label: '低糖饮食', icon: '🍬' },
      { value: 'other', label: '其他', icon: '❓' },
      { value: 'none', label: '无特殊忌口', icon: '✅' },
    ],
    description: '了解您的个人忌口有助于我们为您提供更符合个人需求的饮食计划'
  },
  
  // 步骤14：饮食偏好 - 药膳饮食
  {
    id: 'medicinalDietPlan',
    title: '您是否愿意加入药膳饮食计划？',
    options: [
      { value: 'daily', label: '一日一次', icon: '🌿' },
      { value: 'threedays', label: '三日一次', icon: '🍵' },
      { value: 'weekly', label: '一周一次', icon: '🌱' },
      { value: 'monthly', label: '一月一次', icon: '🍲' },
      { value: 'none', label: '不愿意', icon: '❌' },
    ],
    description: '药膳是将中药材与食材相结合，有助于调理身体机能'
  },
  // 常点的外卖类型
  {
    id: 'takeoutTypes',
    title: '您常点的外卖类型是？',
    type: 'multiSelect',
    options: [
      { value: 'chinese', label: '中式快餐', icon: '🥡' },
      { value: 'western', label: '西式快餐', icon: '🍔' },
      { value: 'japanese', label: '日式料理', icon: '🍱' },
      { value: 'korean', label: '韩式料理', icon: '🍲' },
      { value: 'salad', label: '沙拉轻食', icon: '🥗' },
      { value: 'hotpot', label: '火锅外卖', icon: '🍲' },
      { value: 'bbq', label: '烧烤', icon: '🍢' },
      { value: 'dessert', label: '甜点饮品', icon: '🧁' },
      { value: 'none', label: '很少点外卖', icon: '🏠' },
    ],
    description: '了解您的外卖习惯有助于我们为您提供更适合的饮食建议'
  },
  // 对方便快捷烹饪食材的偏好
  {
    id: 'quickCookingPreference',
    title: '您偏好哪些方便快捷的烹饪食材？',
    type: 'multiSelect',
    options: [
      { value: 'frozen', label: '冷冻半成品', icon: '❄️' },
      { value: 'instant', label: '方便速食', icon: '🥫' },
      { value: 'preCut', label: '预切蔬菜/水果', icon: '🥕' },
      { value: 'marinated', label: '腌制好的肉类', icon: '🥩' },
      { value: 'salad', label: '即食沙拉', icon: '🥗' },
      { value: 'canned', label: '罐头食品', icon: '🥫' },
      { value: 'readyMeal', label: '预制餐', icon: '🍱' },
      { value: 'none', label: '不喜欢速食类食材', icon: '👨‍🍳' },
    ],
    description: '了解您对快捷食材的偏好有助于我们推荐适合忙碌工作日的食谱'
  },
  // 可接受的每餐烹饪时间
  {
    id: 'acceptableCookingTime',
    title: '您平时可接受的每餐烹饪时间是？',
    options: [
      { value: 'within15', label: '15分钟以内', icon: '⏱️' },
      { value: 'within30', label: '15-30分钟', icon: '⏲️' },
      { value: 'within60', label: '30-60分钟', icon: '🕐' },
      { value: 'over60', label: '60分钟以上', icon: '🕑' },
    ],
    description: '为忙碌的职场人士提供符合时间预算的烹饪建议'
  },
  // 早餐是否经常不吃
  {
    id: 'skipBreakfast',
    title: '您是否经常不吃早餐？',
    options: [
      { value: 'often', label: '经常（每周4次以上）', icon: '⏰' },
      { value: 'sometimes', label: '有时（每周1-3次）', icon: '🍳' },
      { value: 'rarely', label: '很少（基本每天都吃）', icon: '🥐' },
    ],
    description: '早餐对维持能量水平和健康体重至关重要'
  },
  // 午餐是否在公司食堂或外卖解决
  {
    id: 'lunchSolution',
    title: '您的工作日午餐通常如何解决？',
    options: [
      { value: 'canteen', label: '公司食堂', icon: '🏢' },
      { value: 'takeout', label: '外卖', icon: '🛵' },
      { value: 'homemade', label: '自带午餐', icon: '🍱' },
      { value: 'mixed', label: '混合以上几种方式', icon: '🔄' },
    ],
    description: '了解您的午餐模式有助于我们提供更实用的饮食建议'
  },
  // 晚餐用餐时间是否固定
  {
    id: 'dinnerTimeRegular',
    title: '您的晚餐时间是否固定？',
    options: [
      { value: 'regular', label: '固定（基本每天同一时间）', icon: '🕖' },
      { value: 'irregular', label: '不固定（受工作影响变动较大）', icon: '🔄' },
    ],
    description: '固定的晚餐时间有助于身体建立健康的代谢节律'
  },
  // 是否有下午茶习惯
  {
    id: 'afternoonTeaHabit',
    title: '您是否有下午茶习惯？',
    options: [
      { value: 'yes', label: '是（几乎每天）', icon: '☕' },
      { value: 'sometimes', label: '偶尔（每周1-3次）', icon: '🍰' },
      { value: 'no', label: '几乎没有', icon: '❌' },
    ],
    description: '下午茶时间的食物选择对控制全天热量摄入很重要'
  },

  // 步骤19：健康状况采集 - 食物过敏史
  {
    id: 'foodAllergies',
    title: '您是否有食物过敏史？',
    type: 'multiSelect',
    options: [
      { value: 'dairy', label: '乳制品', icon: '🥛' },
      { value: 'gluten', label: '麸质', icon: '🌾' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'soy', label: '大豆', icon: '🫘' },
      { value: 'other', label: '其他', icon: '⚠️' },
      { value: 'none', label: '无过敏史', icon: '✅' },
    ],
    description: '了解您的食物过敏情况有助于我们避免在饮食计划中包含可能引起过敏的食材'
  },
  // 步骤20：健康状况采集 - 慢性疾病
  {
    id: 'chronicDiseases',
    title: '您是否患有以下慢性疾病？',
    type: 'multiSelect',
    options: [
      { value: 'hypertension', label: '高血压', icon: '🩸' },
      { value: 'diabetes', label: '糖尿病', icon: '🍬' },
      { value: 'heartDisease', label: '心脏疾病', icon: '❤️' },
      { value: 'thyroid', label: '甲状腺疾病', icon: '🦋' },
      { value: 'liver', label: '肝脏疾病', icon: '🫁' },
      { value: 'kidney', label: '肾脏疾病', icon: '🫘' },
      { value: 'other', label: '其他', icon: '🏥' },
      { value: 'none', label: '无慢性疾病', icon: '✅' },
    ],
    description: '了解您的慢性疾病情况有助于我们制定安全、合适的减重计划'
  },
  // 步骤21：健康状况采集 - 用药情况
  {
    id: 'medications',
    title: '您目前是否正在服用以下药物？',
    type: 'multiSelect',
    options: [
      { value: 'antihypertensive', label: '降压药', icon: '💊' },
      { value: 'antidiabetic', label: '降糖药', icon: '💉' },
      { value: 'steroid', label: '类固醇', icon: '🧪' },
      { value: 'antidepressant', label: '抗抑郁药', icon: '😊' },
      { value: 'thyroid', label: '甲状腺药物', icon: '🦋' },
      { value: 'other', label: '其他长期用药', icon: '💊' },
      { value: 'none', label: '无长期用药', icon: '✅' },
    ],
    description: '某些药物可能会影响体重变化，了解您的用药情况有助于我们调整减重计划'
  },
  // 步骤22：健康状况采集 - 睡眠质量
  {
    id: 'sleepQuality',
    title: '您的睡眠质量如何？',
    options: [
      { value: 'good', label: '良好（睡眠充足，深沉）', icon: '😴' },
      { value: 'fair', label: '一般（睡眠时间不足或质量一般）', icon: '😐' },
      { value: 'poor', label: '较差（经常失眠或睡眠浅）', icon: '😫' },
      { value: 'irregular', label: '不规律（作息时间不固定）', icon: '🔄' },
    ],
    description: '睡眠质量与代谢、饮食习惯和体重管理密切相关'
  },
  // 步骤23：中医体质相关症状 - 颈椎腰椎不适
  {
    id: 'spineDiscomfort',
    title: '是否有颈椎、腰椎等因久坐导致的不适？',
    options: [
      { value: 'yes', label: '是', icon: '🤕' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '久坐工作容易引起颈椎、腰椎不适，需要适当调整工作姿势和休息'
  },
  // 步骤24：中医体质相关症状 - 疲劳感
  {
    id: 'fatigue',
    title: '是否经常感到疲劳、精神不振？',
    options: [
      { value: 'yes', label: '是', icon: '😴' },
      { value: 'no', label: '否', icon: '😊' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '长时间工作可能导致疲劳、精神不振，需要合理安排工作和休息时间'
  },
  // 步骤25：中医体质相关症状 - 消化问题
  {
    id: 'digestiveIssues',
    title: '是否有消化不良等肠胃问题？',
    options: [
      { value: 'yes', label: '是', icon: '😣' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '工作压力和不规律饮食可能导致消化不良等肠胃问题'
  },
  // 步骤26：中医体质相关症状 - 工作压力
  {
    id: 'workStressLevel',
    title: '工作压力水平？',
    options: [
      { value: 'low', label: '低', icon: '😊' },
      { value: 'medium', label: '中', icon: '😐' },
      { value: 'high', label: '高', icon: '😫' },
    ],
    description: '了解工作压力水平有助于我们为您提供更合适的饮食和生活建议'
  },
  // 步骤26：中医体质相关症状 - 出汗
  {
    id: 'sweating',
    title: '您是否容易出汗，且汗后感觉更累？',
    options: [
      { value: 'yes', label: '是', icon: '💦' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'onlyExercise', label: '仅运动后出汗', icon: '🏃' },
    ],
    description: '容易出汗且汗后乏力可能与中医气虚体质相关'
  },
  // 步骤27：中医体质相关症状 - 腰痛
  {
    id: 'backPain',
    title: '您是否经常感到腰部酸痛、四肢无力？',
    options: [
      { value: 'yes', label: '是', icon: '🤕' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '腰部酸痛、四肢无力可能与中医肾虚体质相关'
  },
  // 步骤28：中医体质相关症状 - 食欲
  {
    id: 'appetite',
    title: '您吃饭时是否容易没胃口，或吃一点就饱？',
    options: [
      { value: 'yes', label: '是', icon: '🍽️' },
      { value: 'no', label: '否', icon: '🙅‍♂️' },
      { value: 'normal', label: '正常', icon: '👋' },
    ],
    description: '食欲不振可能与中医脾胃虚弱相关'
  },

  // 步骤29：中医体质相关症状 - 月经规律性（仅女性）
  {
    id: 'menstrualRegularity',
    title: '月经周期是否规律？',
    options: [
      { value: 'yes', label: '是', icon: '👩‍🦰' },
      { value: 'no', label: '否', icon: '👩‍��' },
      { value: 'sometimes', label: '偶尔', icon: '👩‍🦰' },
    ],
    description: '月经不规律可能与中医气血失调相关（此问题仅女性需要回答）'
  }
];

// 定义采集阶段
const COLLECTION_PHASES = {
  BODY_DATA: 'bodyData',
  HEALTH_GOALS: 'healthGoals',
  DIET_PREFERENCES: 'dietPreferences',
  HEALTH_STATUS: 'healthStatus',
  TCM_SYMPTOMS: 'tcmSymptoms'
} as const

type CollectionPhase = typeof COLLECTION_PHASES[keyof typeof COLLECTION_PHASES]

// 身体数据采集步骤（前8步 + 总结步骤）
const bodyDataSteps = formSteps.slice(0, 8)
// 添加引导步骤和总结步骤
const bodyDataStepsWithSummary = [
  {
    id: 'bodyDataIntro',
    title: '身体数据采集',
    type: 'intro'
  },
  ...bodyDataSteps,
  {
    id: 'bodyDataSummary',
    title: '基于您的回答的个人总结',
    type: 'summary'
  }
]

// 健康目标采集步骤（第9-12步）
const healthGoalsStepsOriginal = formSteps.slice(8, 12)
// 添加引导步骤和总结步骤到健康目标采集
const healthGoalsSteps = [
  {
    id: 'healthGoalsIntro',
    title: '健康目标设定',
    type: 'intro'
  },
  ...healthGoalsStepsOriginal,
  {
    id: 'healthGoalsSummary',
    title: '您的健康目标总结',
    type: 'summary'
  }
]

// 饮食偏好采集步骤（第13-22步）
const dietPreferencesStepsOriginal = formSteps.slice(12, 22)
// 添加引导步骤和总结步骤到饮食偏好采集
const dietPreferencesSteps = [
  {
    id: 'dietPreferencesIntro',
    title: '饮食偏好了解',
    type: 'intro'
  },
  ...dietPreferencesStepsOriginal,
  {
    id: 'dietPreferencesSummary',
    title: '您的饮食偏好总结',
    type: 'summary'
  }
]

// 健康状况采集步骤（食物过敏史、慢性疾病、用药情况、睡眠质量）
const healthStatusStepsOriginal = formSteps.filter(step =>
  step.id === 'foodAllergies' ||
  step.id === 'chronicDiseases' ||
  step.id === 'medications' ||
  step.id === 'sleepQuality'
)
// 添加引导步骤和总结步骤到健康状况采集
const healthStatusSteps = [
  {
    id: 'healthStatusIntro',
    title: '健康状况了解',
    type: 'intro'
  },
  ...healthStatusStepsOriginal,
  {
    id: 'healthStatusSummary',
    title: '您的健康状况总结',
    type: 'summary'
  }
]

// 中医体质相关症状采集步骤（步骤23-29：7个中医体质症状）
const tcmSymptomsStepsOriginal = formSteps.filter(step =>
  step.id === 'spineDiscomfort' ||
  step.id === 'fatigue' ||
  step.id === 'digestiveIssues' ||
  step.id === 'workStressLevel' ||
  step.id === 'sweating' ||
  step.id === 'backPain' ||
  step.id === 'appetite' ||
  step.id === 'menstrualRegularity'
)
// 添加引导步骤和总结步骤到中医体质相关症状采集
const tcmSymptomsSteps = [
  {
    id: 'tcmSymptomsIntro',
    title: '中医体质相关症状了解',
    type: 'intro'
  },
  ...tcmSymptomsStepsOriginal,
  {
    id: 'tcmSymptomsSummary',
    title: '您的中医体质相关症状总结',
    type: 'summary'
  }
]

export default function UrbanWorkForm({ lang, onBack }: UrbanWorkFormProps) {
  const [currentPhase, setCurrentPhase] = useState<CollectionPhase>(COLLECTION_PHASES.BODY_DATA)
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  const router = useRouter()

  const [currentStep, setCurrentStep] = useState(0)

  // 获取当前阶段的步骤数
  const getCurrentPhaseSteps = () => {
    switch(currentPhase) {
      case COLLECTION_PHASES.BODY_DATA:
        return bodyDataStepsWithSummary
      case COLLECTION_PHASES.HEALTH_GOALS:
        return healthGoalsSteps
      case COLLECTION_PHASES.DIET_PREFERENCES:
        return dietPreferencesSteps
      case COLLECTION_PHASES.HEALTH_STATUS:
        return healthStatusSteps
      case COLLECTION_PHASES.TCM_SYMPTOMS:
        return tcmSymptomsSteps
      default:
        return formSteps
    }
  }

  const currentPhaseSteps = getCurrentPhaseSteps()
  const totalSteps = currentPhaseSteps.length
  const [calculatedBMI, setCalculatedBMI] = useState<number | null>(null)
  const [bmiCategory, setBMICategory] = useState<string>('')
  const [heightUnit, setHeightUnit] = useState('cm')
  const [weightUnit, setWeightUnit] = useState('kg')
  const [selectedGender, setSelectedGender] = useState<string | null>(null)
  const [knowsBodyFatSelection, setKnowsBodyFatSelection] = useState<string | null>(null)
  const [selectedWeightLossUnit, setSelectedWeightLossUnit] = useState<string | null>(null)
  const [selectedDietReactions, setSelectedDietReactions] = useState<string[]>(['none']) // 默认为"没有明显反应"
  const [selectedFavoriteFoods, setSelectedFavoriteFoods] = useState<string[]>([])
  const [selectedDislikedFoods, setSelectedDislikedFoods] = useState<string[]>([])
  const [selectedReligiousDietaryRestriction, setSelectedReligiousDietaryRestriction] = useState<string | null>(null)
  const [selectedPersonalDietaryRestrictions, setSelectedPersonalDietaryRestrictions] = useState<string[]>([])
  
  // 添加本地状态来存储输入值，避免跨字段影响
  const [inputValues, setInputValues] = useState({
    height: '170',
    weight: '65',
    weightLossGoal: '5',
    goalTimeframe: '3',
    bodyFatPercentage: ''
  });
  
  // 初始化表单
  const form = useForm<WeightLossFormValues>({
    resolver: zodResolver(weightLossFormSchema),
    defaultValues: {
      heightUnit: 'cm',
      weightUnit: 'kg',
      knowsBodyFat: 'no',
      gender: 'male',
      birthDate: new Date(new Date().setFullYear(new Date().getFullYear() - 30)),
      height: inputValues.height,
      weight: inputValues.weight,
      workNature: 'sedentary',
      dailySteps: '2000to5000',
      healthGoalType: 'maintain',
      weightLossAmount: '3',
      goalAchieveTime: '3',
      isWorkPressureAffectWeight: 'not_sure',
      weightLossGoal: inputValues.weightLossGoal,
      weightLossGoalUnit: 'kg',
      goalTimeframe: inputValues.goalTimeframe,
      hasDietHistory: 'no',
      dietReactions: ['none'], // 默认设置为"没有明显反应"
      favoriteFoods: [],
      dislikedFoods: [],
      religiousDietaryRestrictions: 'none',
      personalDietaryRestrictions: [],
      dietaryRestrictions: [],
      takeoutTypes: ['chinese'],
      quickCookingPreference: ['preCut'],
      acceptableCookingTime: 'within30',
      medicinalDietPlan: 'none',
      skipBreakfast: 'sometimes',
      lunchSolution: 'mixed',
      dinnerTimeRegular: 'irregular',
      afternoonTeaHabit: 'sometimes',
      foodAllergies: [],
      chronicDiseases: [],
      medications: [],
      sleepQuality: 'fair',
      spineDiscomfort: 'no',
      fatigue: 'no',
      digestiveIssues: 'no',
      workStressLevel: 'medium',
      sweating: 'no',
      backPain: 'no',
      appetite: 'normal',
      menstrualRegularity: 'yes',
    },
  })
  
  // 监听单位变化
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.heightUnit) setHeightUnit(value.heightUnit);
      if (value.weightUnit) setWeightUnit(value.weightUnit);
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);
  
  // 监听表单值变化，计算BMI
  useEffect(() => {
    // 确保有身高和体重值时计算BMI
    if (inputValues.height && inputValues.weight) {
      const heightUnit = form.getValues('heightUnit');
      const weightUnit = form.getValues('weightUnit');
      if (heightUnit && weightUnit) {
        calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
      }
    }
  }, [inputValues.height, inputValues.weight]);
  
  // 监听表单单位变化
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if ((name === 'heightUnit' || name === 'weightUnit') 
          && inputValues.height && inputValues.weight) {
        const heightUnit = name === 'heightUnit' ? value.heightUnit : form.getValues('heightUnit');
        const weightUnit = name === 'weightUnit' ? value.weightUnit : form.getValues('weightUnit');
        if (heightUnit && weightUnit) {
          calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, inputValues.height, inputValues.weight]);
  
  // 监听体脂率输入值变化，自动清除错误
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      // 当体脂率字段变化且有值时
      if (name === 'bodyFatPercentage' && value.bodyFatPercentage) {
        const bodyFatValue = value.bodyFatPercentage;
        // 如果输入了有效的体脂率数值，清除错误
        const numericValue = parseFloat(bodyFatValue);
        if (!isNaN(numericValue) && numericValue > 0 && numericValue <= 100) {
          if (form.formState.errors.bodyFatPercentage) {
            form.clearErrors('bodyFatPercentage');
          }
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);
  
  // 设置步骤10（节食反应）的默认值
  useEffect(() => {
    // 在组件加载后立即设置默认值
    if (currentStep === 9) { // 节食反应是步骤10，索引为9
      // 确保默认选中"没有明显反应"
      form.setValue('dietReactions', ['none'], {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true
      });
      setSelectedDietReactions(['none']);
    }
  }, [currentStep, form]);
  
  // 计算BMI
  const calculateBMI = (height: string, heightUnit: string, weight: string, weightUnit: string) => {
    try {
      let heightInMeters: number
      let weightInKg: number
      
      // 转换身高为米
      if (heightUnit === 'cm') {
        heightInMeters = parseFloat(height) / 100
      } else {
        heightInMeters = parseFloat(height) * 0.0254 // 英寸转米
      }
      
      // 转换体重为千克
      if (weightUnit === 'kg') {
        weightInKg = parseFloat(weight)
      } else {
        weightInKg = parseFloat(weight) * 0.453592 // 磅转千克
      }
      
      if (heightInMeters <= 0 || weightInKg <= 0) {
        setCalculatedBMI(null)
        setBMICategory('')
        return
      }
      
      // 计算BMI: 体重(kg) / 身高(m)的平方
      const bmi = weightInKg / (heightInMeters * heightInMeters)
      setCalculatedBMI(parseFloat(bmi.toFixed(1)))
      
      // 确定BMI类别
      if (bmi < 18.5) {
        setBMICategory('体重过轻')
      } else if (bmi >= 18.5 && bmi < 24) {
        setBMICategory('正常范围')
      } else if (bmi >= 24 && bmi < 28) {
        setBMICategory('超重')
      } else {
        setBMICategory('肥胖')
      }
    } catch (error) {
      setCalculatedBMI(null)
      setBMICategory('')
    }
  }
  
  // 生成用户数据摘要
  const generateUserDataSummary = (formData: any) => {
    // 计算BMI
    const bmi = formData.height && formData.weight
      ? (parseFloat(formData.weight) / Math.pow(parseFloat(formData.height) / 100, 2)).toFixed(1)
      : '未知'

    // 计算年龄
    const calculateAge = (birthDate: string) => {
      if (!birthDate) return '未提供'
      const birth = new Date(birthDate)
      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age.toString()
    }

    // 获取用户选择的群体并映射到中文名称
    const selectedGroup = typeof window !== 'undefined' ? localStorage.getItem('selected_audience_group') : null
    const getGroupIdentity = (groupId: string | null) => {
      switch(groupId) {
        case 'weight-loss':
          return '希望健康瘦身人群'
        case 'fitness':
          return '健身爱好者'
        case 'white-collar':
          return '都市白领'
        default:
          return '都市白领' // 默认值
      }
    }

    // 格式化体脂率信息
    const formatBodyFat = () => {
      if (formData.knowsBodyFat === 'yes' && formData.bodyFatPercentage) {
        return `${formData.bodyFatPercentage}%`
      }
      return '不清楚'
    }

    return `
基础数据信息：
- 身份：${getGroupIdentity(selectedGroup)}
- 性别：${formData.gender === 'male' ? '男' : '女'}
- 年龄：${calculateAge(formData.birthDate)}岁
- 身高：${formData.height || '未提供'}${formData.heightUnit || 'cm'}
- 体重：${formData.weight || '未提供'}${formData.weightUnit || 'kg'}
- BMI：${bmi}
- 体脂率：${formatBodyFat()}

工作特征：
- 工作性质：${formData.workNature === 'sedentary' ? '久坐办公' : formData.workNature === 'light' ? '轻度活动' : formData.workNature === 'moderate' ? '中度活动' : formData.workNature === 'active' ? '重度活动' : '未填写'}
- 每日步数：${formData.dailySteps === 'less2000' ? '少于2000步' : formData.dailySteps === '2000to5000' ? '2000-5000步' : formData.dailySteps === '5000to10000' ? '5000-10000步' : formData.dailySteps === 'more10000' ? '10000步以上' : '未填写'}
- 健康目标：${formData.healthGoalType === 'maintain' ? '维持健康体重' : formData.healthGoalType === 'mild_weight_loss' ? '轻度减脂' : formData.healthGoalType === 'vitality' ? '提升身体活力' : '未设定'}
- 工作压力对体重影响：${formData.isWorkPressureAffectWeight === 'yes' ? '是的，明显相关' : formData.isWorkPressureAffectWeight === 'no' ? '没有明显关系' : '不确定'}

饮食习惯：
- 早餐习惯：${formData.skipBreakfast === 'often' ? '经常不吃早餐' : formData.skipBreakfast === 'sometimes' ? '有时不吃早餐' : '很少不吃早餐'}
- 午餐解决方案：${formData.lunchSolution === 'canteen' ? '公司食堂' : formData.lunchSolution === 'takeout' ? '外卖' : formData.lunchSolution === 'homemade' ? '自带午餐' : '混合方式'}
- 晚餐时间规律性：${formData.dinnerTimeRegular === 'regular' ? '固定' : '不固定'}
- 下午茶习惯：${formData.afternoonTeaHabit === 'yes' ? '是，几乎每天' : formData.afternoonTeaHabit === 'sometimes' ? '偶尔' : '几乎没有'}
- 常点外卖类型：${formData.takeoutTypes?.join('、') || '未选择'}
- 快捷烹饪偏好：${formData.quickCookingPreference?.join('、') || '未选择'}
- 可接受烹饪时间：${formData.acceptableCookingTime === 'within15' ? '15分钟以内' : formData.acceptableCookingTime === 'within30' ? '15-30分钟' : formData.acceptableCookingTime === 'within60' ? '30-60分钟' : '60分钟以上'}

健康状况：
- 食物过敏史：${formData.foodAllergies?.includes('none') ? '无过敏史' : formData.foodAllergies?.join('、') || '未填写'}
- 慢性疾病：${formData.chronicDiseases?.includes('none') ? '无慢性疾病' : formData.chronicDiseases?.join('、') || '未填写'}
- 用药情况：${formData.medications?.includes('none') ? '无长期用药' : formData.medications?.join('、') || '未填写'}
- 睡眠质量：${formData.sleepQuality === 'good' ? '良好' : formData.sleepQuality === 'fair' ? '一般' : formData.sleepQuality === 'poor' ? '较差' : formData.sleepQuality === 'irregular' ? '不规律' : '未填写'}

中医体质相关症状：
- 脊椎不适：${formData.spineDiscomfort === 'yes' ? '是，经常感到颈椎或腰椎不适' : formData.spineDiscomfort === 'sometimes' ? '偶尔感到脊椎不适' : '否，脊椎状况良好'}
- 疲劳程度：${formData.fatigue === 'heat' ? '怕热明显' : formData.fatigue === 'cold' ? '怕冷明显' : '无特殊'}
- 消化问题：${formData.digestiveIssues === 'yes' ? '是，经常有消化不良等问题' : formData.digestiveIssues === 'sometimes' ? '偶尔有消化问题' : '否，消化功能正常'}
- 工作压力水平：${formData.workStressLevel === 'low' ? '压力较小' : formData.workStressLevel === 'medium' ? '压力中等' : '压力较大'}
- 出汗情况：${formData.sweating === 'yes' ? '是，容易出汗且汗后感觉更累' : formData.sweating === 'onlyExercise' ? '仅运动后出汗' : '否，出汗正常'}
- 腰部状况：${formData.backPain === 'yes' ? '是，经常感到腰部酸痛、四肢无力' : formData.backPain === 'sometimes' ? '偶尔腰部酸痛' : '否，腰部状况正常'}
- 食欲状况：${formData.appetite === 'yes' ? '是，吃饭时容易没胃口或吃一点就饱' : formData.appetite === 'normal' ? '食欲正常' : '否，食欲良好'}
${formData.gender === 'female' ? `- 月经规律性：${formData.menstrualRegularity === 'yes' ? '是，月经周期规律' : '否，月经周期不规律'}` : ''}
    `.trim()
  }

  // 生成健康评估报告
  const generateHealthReport = async () => {
    setIsGeneratingReport(true)

    try {
      const formData = form.getValues()
      const userDataSummary = generateUserDataSummary(formData)

      // 获取选择的群体信息
      const selectedGroup = typeof window !== 'undefined' ? localStorage.getItem('selected_audience_group') : null

      const response = await fetch('/api/generate-health-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userData: userDataSummary,
          formData: {
            ...formData,
            selectedGroup: selectedGroup
          }
        }),
      })

      if (!response.ok) {
        throw new Error('生成报告失败，请稍后重试')
      }

      const data = await response.json()

      // 健康评估完成后，标记用户已完成信息填写，避免再次弹出对话框
      if (typeof window !== 'undefined') {
        localStorage.setItem('profile_completed', 'true')
      }

      // 重定向到主页，添加时间戳参数强制刷新
      router.push(`/?refresh=${Date.now()}`)

    } catch (error) {
      console.error('生成健康报告时出错:', error)
      alert(error instanceof Error ? error.message : '生成报告时发生未知错误')
    } finally {
      setIsGeneratingReport(false)
    }
  }

  // 处理下一步
  const handleNext = () => {
    const currentStepId = currentPhaseSteps[currentStep].id

    // 对于体脂率特殊处理
    if (currentStepId === 'knowsBodyFat' && form.getValues('knowsBodyFat') === 'yes') {
      // 如果用户知道体脂率，需要先填写体脂率后才能进入下一步
      const bodyFatValue = form.getValues('bodyFatPercentage');
      // 检查输入值是否存在且不为空字符串
      if (!bodyFatValue || bodyFatValue.trim() === '') {
        form.setError('bodyFatPercentage', {
          type: 'manual',
          message: '请输入您的体脂率'
        });
        return;
      }

      // 检查输入值是否为有效数字
      const numericValue = parseFloat(bodyFatValue);
      if (isNaN(numericValue) || numericValue <= 0 || numericValue > 100) {
        form.setError('bodyFatPercentage', {
          type: 'manual',
          message: '请输入有效的体脂率数值（1-100之间）'
        });
        return;
      }
    }

    // 健康目标特殊处理：如果选择非"轻度减脂"，则跳过减重数值和达成时间字段
    if (currentStepId === 'healthGoalType' && form.getValues('healthGoalType') !== 'mild_weight_loss') {
      // 如果不是选择轻度减脂，设置默认值并跳过减重数值和达成时间字段
      form.setValue('weightLossAmount', '0');
      form.setValue('goalAchieveTime', '3');

      // 计算需要跳过的步骤数：跳过weightLossAmount和goalAchieveTime两个步骤
      if (currentStep < totalSteps - 3) {
        setCurrentStep(prev => prev + 3); // 跳过两个步骤到isWorkPressureAffectWeight
        return;
      }
    }

    // 对于体重减少数值的特殊处理
    if (currentStepId === 'weightLossAmount' && form.getValues('healthGoalType') === 'mild_weight_loss') {
      const weightLossAmount = form.getValues('weightLossAmount');
      if (!weightLossAmount || weightLossAmount.trim() === '') {
        form.setError('weightLossAmount', {
          type: 'manual',
          message: '请输入您希望减少的体重数值'
        });
        return;
      }

      // 检查输入值是否为有效数字且在合理范围内
      const numericValue = parseFloat(weightLossAmount);
      if (isNaN(numericValue) || numericValue <= 0 || numericValue > 20) {
        form.setError('weightLossAmount', {
          type: 'manual',
          message: '请输入合理的体重减少数值（1-20千克之间）'
        });
        return;
      }
    }

    // 如果当前步骤是appetite，且用户性别不是女性，则跳过menstrualRegularity步骤
    if (currentStepId === 'appetite' && form.getValues('gender') !== 'female') {
      // 设置默认值并跳过月经规律性问题
      form.setValue('menstrualRegularity', 'yes');
      if (currentStep < totalSteps - 2) {
        setCurrentStep(prev => prev + 2); // 跳过月经规律性步骤
        return;
      }
    }

    // 特殊处理引导步骤
    if (currentStepId === 'bodyDataIntro' || currentStepId === 'healthGoalsIntro' || currentStepId === 'dietPreferencesIntro' || currentStepId === 'healthStatusIntro' || currentStepId === 'tcmSymptomsIntro') {
      setCurrentStep(prev => prev + 1);
      return;
    }

    // 特殊处理总结步骤
    if (currentStepId === 'bodyDataSummary') {
      // 直接进入健康目标采集阶段
      setCurrentPhase(COLLECTION_PHASES.HEALTH_GOALS);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'healthGoalsSummary') {
      // 健康目标采集完成，直接进入饮食偏好采集阶段
      setCurrentPhase(COLLECTION_PHASES.DIET_PREFERENCES);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'dietPreferencesSummary') {
      // 饮食偏好采集完成，直接进入健康状况采集阶段
      setCurrentPhase(COLLECTION_PHASES.HEALTH_STATUS);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'healthStatusSummary') {
      // 健康状况采集完成，直接进入中医体质相关症状采集阶段
      setCurrentPhase(COLLECTION_PHASES.TCM_SYMPTOMS);
      setCurrentStep(0);
      return;
    }

    if (currentStepId === 'tcmSymptomsSummary') {
      // 中医体质相关症状采集完成，生成健康评估报告
      console.log('中医体质相关症状采集完成，生成健康评估报告');
      generateHealthReport();
      return;
    }

    // 处理其他步骤
    const stepValue = form.getValues(currentStepId as keyof WeightLossFormValues);
    if (stepValue !== undefined) {
      if (currentStep < totalSteps - 1) {
        setCurrentStep(prev => prev + 1);
      } else {
        // 当前阶段完成
        // 提交表单
        onSubmit(form.getValues());
      }
    } else {
      // 显示必填项提示
      form.trigger(currentStepId as any);
    }
  }

  // 处理上一步
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    } else {
      // 当前是各阶段的第一步，需要跨阶段返回
      if (currentPhase === COLLECTION_PHASES.HEALTH_GOALS) {
        // 从健康目标采集阶段返回到身体数据采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.BODY_DATA);
        setCurrentStep(bodyDataStepsWithSummary.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        // 从饮食偏好采集阶段返回到健康目标采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.HEALTH_GOALS);
        setCurrentStep(healthGoalsSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        // 从健康状况采集阶段返回到饮食偏好采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.DIET_PREFERENCES);
        setCurrentStep(dietPreferencesSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        // 从中医体质相关症状采集阶段返回到健康状况采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.HEALTH_STATUS);
        setCurrentStep(healthStatusSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.BODY_DATA && onBack) {
        // 从身体数据采集阶段返回到群体选择组件
        onBack()
      }
    }
  }

  // 提交表单
  function onSubmit(data: WeightLossFormValues) {
    console.log('表单数据:', data)
    // 生成健康评估报告
    generateHealthReport()
  }

  // 如果正在生成报告，显示加载状态
  if (isGeneratingReport) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
        <Card className="w-full max-w-md">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              正在生成您的健康评估报告
            </h3>
            <p className="text-gray-600 mb-4">
              我们的中医专家正在根据您的信息制定个性化健康方案...
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
            <p className="text-sm text-gray-500 mt-2">预计需要 30-60 秒</p>
          </div>
        </Card>
      </div>
    )
  }

  // 渲染当前步骤
  const renderCurrentStep = () => {
    const currentStepConfig = currentPhaseSteps[currentStep]

    // 如果是引导步骤，渲染引导内容
    if (currentStepConfig.type === 'intro') {
      // 根据当前阶段显示不同的引导内容
      if (currentPhase === COLLECTION_PHASES.BODY_DATA) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-blue-100 to-green-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-green-500 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-blue-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">💼</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-orange-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>

              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                让我们开始收集您的身体数据！
              </h2>

              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                作为都市白领，我们需要了解您的基本身体信息和工作特征，为您制定适合忙碌生活的健康计划。
              </p>

              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  💡 提示：请如实填写，这些信息将帮助我们为您制定符合职场生活的健康方案。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_GOALS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-yellow-100 to-orange-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-red-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-red-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🎯</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-yellow-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>

              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                设定您的健康目标！
              </h2>

              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                现在让我们设定适合都市白领的健康目标。考虑到您的工作特点，我们将为您制定实用的健康计划。
              </p>

              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  🎯 提示：合理的目标设定是成功的关键，我们会考虑您的工作压力和时间安排。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-green-400 to-blue-500 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-green-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🍽️</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-blue-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>

              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                现在了解您的饮食偏好！
              </h2>

              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                接下来我们将了解您的饮食偏好和习惯，特别是适合忙碌工作生活的饮食解决方案。
              </p>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-red-100 to-pink-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-red-400 to-pink-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-pink-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🏥</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-red-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>

              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                了解您的健康状况！
              </h2>

              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                现在我们需要了解您的健康状况，包括食物过敏史、慢性疾病、用药情况和睡眠质量，这将帮助我们制定更安全、有效的健康计划。
              </p>

              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  🏥 提示：了解您的健康状况有助于我们制定安全、个性化的健康方案。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-purple-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🌿</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-pink-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>

              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                中医体质相关症状了解！
              </h2>

              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                最后，我们将从中医角度了解您的体质特征，特别是与都市白领工作相关的身体症状，为您提供更全面的健康调理建议。
              </p>

              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  🌿 提示：中医体质辨识有助于我们为您提供更精准的饮食和生活调理建议。
                </p>
              </div>
            </div>
          </div>
        );
      }
    }

    // 如果是总结步骤，渲染总结内容
    if (currentStepConfig.type === 'summary') {
      const formData = form.getValues()

      // 根据不同阶段显示不同的总结内容
      if (currentPhase === COLLECTION_PHASES.BODY_DATA) {
        // 计算BMI
        const bmi = formData.height && formData.weight
          ? (parseFloat(formData.weight) / Math.pow(parseFloat(formData.height) / 100, 2)).toFixed(1)
          : null

        // 计算年龄
        const calculateAge = (birthDate: Date | string) => {
          if (!birthDate) return null
          const birth = new Date(birthDate)
          const today = new Date()
          let age = today.getFullYear() - birth.getFullYear()
          const monthDiff = today.getMonth() - birth.getMonth()
          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--
          }
          return age
        }

        const age = calculateAge(formData.birthDate)

        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                基于您的回答的个人总结
              </h2>
            </div>

            {/* BMI 卡片 */}
            {bmi && (
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <span className="text-orange-600">📊</span>
                  </div>
                  <h3 className="font-medium text-gray-800">身体质量指数 (BMI)</h3>
                </div>
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl font-bold text-orange-600">{bmi}</span>
                  <span className="text-orange-500 text-sm font-medium">
                    {parseFloat(bmi) < 18.5 ? '偏瘦' :
                     parseFloat(bmi) < 24 ? '正常范围' :
                     parseFloat(bmi) < 28 ? '超重' : '肥胖'}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  BMI计算基于您提供的身高体重数据
                </p>
              </div>
            )}

            {/* 基本信息卡片 */}
            <div className="grid grid-cols-2 gap-4">
              {/* 性别 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-blue-600">👤</span>
                  <span className="text-sm font-medium text-gray-700">性别</span>
                </div>
                <span className="text-lg font-semibold text-gray-800">
                  {formData.gender === 'male' ? '男性' : formData.gender === 'female' ? '女性' : '其他'}
                </span>
              </div>

              {/* 年龄 */}
              {age && (
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-green-600">🎂</span>
                    <span className="text-sm font-medium text-gray-700">年龄</span>
                  </div>
                  <span className="text-lg font-semibold text-gray-800">{age} 岁</span>
                </div>
              )}

              {/* 身高 */}
              {formData.height && (
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-purple-600">📏</span>
                    <span className="text-sm font-medium text-gray-700">身高</span>
                  </div>
                  <span className="text-lg font-semibold text-gray-800">
                    {formData.height} {formData.heightUnit || 'cm'}
                  </span>
                </div>
              )}

              {/* 体重 */}
              {formData.weight && (
                <div className="bg-pink-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-pink-600">⚖️</span>
                    <span className="text-sm font-medium text-gray-700">体重</span>
                  </div>
                  <span className="text-lg font-semibold text-gray-800">
                    {formData.weight} {formData.weightUnit || 'kg'}
                  </span>
                </div>
              )}

              {/* 体脂率 */}
              {formData.knowsBodyFat === 'yes' && formData.bodyFatPercentage && (
                <div className="bg-yellow-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-yellow-600">📈</span>
                    <span className="text-sm font-medium text-gray-700">体脂率</span>
                  </div>
                  <span className="text-lg font-semibold text-gray-800">{formData.bodyFatPercentage}%</span>
                </div>
              )}

              {/* 工作性质 */}
              {formData.workNature && (
                <div className="bg-indigo-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-indigo-600">💼</span>
                    <span className="text-sm font-medium text-gray-700">工作性质</span>
                  </div>
                  <span className="text-lg font-semibold text-gray-800">
                    {formData.workNature === 'sedentary' ? '久坐办公室' :
                     formData.workNature === 'light' ? '轻度活动' :
                     formData.workNature === 'moderate' ? '中度活动' :
                     formData.workNature === 'active' ? '重度活动' : formData.workNature}
                  </span>
                </div>
              )}
            </div>

            {/* 总结文字 */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6 text-center">
              <p className="text-gray-700 mb-4">
                感谢您完成身体数据采集！我们已经记录了您的相关信息，这将帮助我们为您制定更精准的健康方案。
              </p>
              <p className="text-sm text-gray-600">
                点击"继续"继续下一阶段的信息收集。
              </p>
            </div>
          </div>
        )
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_GOALS) {
        // 健康目标阶段总结
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                您的健康目标总结
              </h2>
            </div>

            {/* 健康目标卡片 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600">🎯</span>
                </div>
                <h3 className="font-medium text-gray-800">健康目标</h3>
              </div>

              <div className="space-y-3">
                {formData.healthGoalType && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">目标类型</span>
                    <span className="font-medium text-gray-800">
                      {formData.healthGoalType === 'maintain' ? '维持健康体重' :
                       formData.healthGoalType === 'mild_weight_loss' ? '轻度减脂' :
                       formData.healthGoalType === 'vitality' ? '提升身体活力' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.healthGoalType === 'mild_weight_loss' && formData.weightLossAmount && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">减重目标</span>
                    <span className="font-medium text-gray-800">
                      {formData.weightLossAmount} 千克
                    </span>
                  </div>
                )}

                {formData.goalAchieveTime && formData.healthGoalType === 'mild_weight_loss' && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">目标时间</span>
                    <span className="font-medium text-gray-800">{formData.goalAchieveTime} 个月</span>
                  </div>
                )}

                {formData.isWorkPressureAffectWeight && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">工作压力对体重影响</span>
                    <span className="font-medium text-gray-800">
                      {formData.isWorkPressureAffectWeight === 'yes' ? '是，有影响' :
                       formData.isWorkPressureAffectWeight === 'no' ? '否，无影响' :
                       formData.isWorkPressureAffectWeight === 'not_sure' ? '不确定' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.medicinalDietPlan && (
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600">药膳饮食计划</span>
                    <span className="font-medium text-gray-800">
                      {formData.medicinalDietPlan === 'daily' ? '一日一次' :
                       formData.medicinalDietPlan === 'threedays' ? '三日一次' :
                       formData.medicinalDietPlan === 'weekly' ? '一周一次' :
                       formData.medicinalDietPlan === 'monthly' ? '一月一次' :
                       formData.medicinalDietPlan === 'none' ? '不愿意' : '未选择'}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 总结文字 */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6 text-center">
              <p className="text-gray-700 mb-4">
                感谢您完成健康目标设定！我们已经记录了您的目标，这将帮助我们为您制定更有针对性的健康方案。
              </p>
              <p className="text-sm text-gray-600">
                点击"继续"继续下一阶段的信息收集。
              </p>
            </div>
          </div>
        )
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        // 饮食偏好阶段总结
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                您的饮食偏好总结
              </h2>
            </div>

            {/* 饮食偏好卡片 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600">🍽️</span>
                </div>
                <h3 className="font-medium text-gray-800">饮食习惯与偏好</h3>
              </div>

              <div className="space-y-3">
                {formData.takeoutTypes && formData.takeoutTypes.length > 0 && (
                  <div className="py-2 border-b border-gray-100">
                    <span className="text-gray-600 block mb-1">外卖类型偏好</span>
                    <div className="flex flex-wrap gap-2">
                      {formData.takeoutTypes.map((type: string) => {
                        const typeMap: {[key: string]: string} = {
                          'chinese': '中式菜品',
                          'western': '西式快餐',
                          'japanese': '日韩料理',
                          'healthy': '健康轻食',
                          'hotpot': '火锅烧烤',
                          'dessert': '甜品饮品'
                        }
                        return (
                          <span key={type} className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm">
                            {typeMap[type] || type}
                          </span>
                        )
                      })}
                    </div>
                  </div>
                )}

                {formData.quickCookingPreference && formData.quickCookingPreference.length > 0 && (
                  <div className="py-2 border-b border-gray-100">
                    <span className="text-gray-600 block mb-1">快手烹饪偏好</span>
                    <div className="flex flex-wrap gap-2">
                      {formData.quickCookingPreference.map((pref: string) => {
                        const prefMap: {[key: string]: string} = {
                          'frozen': '冷冻半成品',
                          'instant': '方便速食',
                          'preCut': '预切蔬菜',
                          'canned': '罐装食品'
                        }
                        return (
                          <span key={pref} className="bg-green-100 text-green-800 px-2 py-1 rounded-md text-sm">
                            {prefMap[pref] || pref}
                          </span>
                        )
                      })}
                    </div>
                  </div>
                )}

                {formData.acceptableCookingTime && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">可接受烹饪时间</span>
                    <span className="font-medium text-gray-800">
                      {formData.acceptableCookingTime === 'within15' ? '15分钟以内' :
                       formData.acceptableCookingTime === 'within30' ? '15-30分钟' :
                       formData.acceptableCookingTime === 'within60' ? '30-60分钟' :
                       formData.acceptableCookingTime === 'over60' ? '60分钟以上' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.skipBreakfast && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">早餐习惯</span>
                    <span className="font-medium text-gray-800">
                      {formData.skipBreakfast === 'often' ? '经常不吃早餐' :
                       formData.skipBreakfast === 'sometimes' ? '有时不吃早餐' :
                       formData.skipBreakfast === 'rarely' ? '很少不吃早餐' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.lunchSolution && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">午餐解决方案</span>
                    <span className="font-medium text-gray-800">
                      {formData.lunchSolution === 'canteen' ? '公司食堂' :
                       formData.lunchSolution === 'takeout' ? '外卖' :
                       formData.lunchSolution === 'homemade' ? '自带午餐' :
                       formData.lunchSolution === 'mixed' ? '混合方式' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.dinnerTimeRegular && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">晚餐时间规律性</span>
                    <span className="font-medium text-gray-800">
                      {formData.dinnerTimeRegular === 'regular' ? '时间规律' :
                       formData.dinnerTimeRegular === 'irregular' ? '时间不规律' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.afternoonTeaHabit && (
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600">下午茶习惯</span>
                    <span className="font-medium text-gray-800">
                      {formData.afternoonTeaHabit === 'yes' ? '是，几乎每天' :
                       formData.afternoonTeaHabit === 'sometimes' ? '偶尔' :
                       formData.afternoonTeaHabit === 'no' ? '几乎没有' : '未设定'}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 总结文字 */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6 text-center">
              <p className="text-gray-700 mb-4">
                感谢您完成饮食偏好采集！我们已经了解了您的饮食习惯，这将帮助我们为您推荐更适合的健康饮食方案。
              </p>
              <p className="text-sm text-gray-600">
                点击"继续"继续下一阶段的信息收集。
              </p>
            </div>
          </div>
        )
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        // 健康状况采集阶段总结

        // 创建映射函数
        const getFoodAllergyLabel = (value: string) => {
          const map: {[key: string]: string} = {
            'dairy': '乳制品',
            'gluten': '麸质',
            'nuts': '坚果',
            'seafood': '海鲜',
            'eggs': '鸡蛋',
            'soy': '大豆',
            'other': '其他',
            'none': '无过敏史'
          }
          return map[value] || value
        }

        const getChronicDiseaseLabel = (value: string) => {
          const map: {[key: string]: string} = {
            'hypertension': '高血压',
            'diabetes': '糖尿病',
            'heartDisease': '心脏疾病',
            'thyroid': '甲状腺疾病',
            'liver': '肝脏疾病',
            'kidney': '肾脏疾病',
            'other': '其他',
            'none': '无慢性疾病'
          }
          return map[value] || value
        }

        const getMedicationLabel = (value: string) => {
          const map: {[key: string]: string} = {
            'antihypertensive': '降压药',
            'antidiabetic': '降糖药',
            'steroid': '类固醇',
            'antidepressant': '抗抑郁药',
            'thyroid': '甲状腺药物',
            'other': '其他长期用药',
            'none': '无长期用药'
          }
          return map[value] || value
        }

        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                您的健康状况总结
              </h2>
            </div>

            {/* 健康状况卡片 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-red-600">🏥</span>
                </div>
                <h3 className="font-medium text-gray-800">健康状况信息</h3>
              </div>

              <div className="space-y-3">
                {formData.foodAllergies && formData.foodAllergies.length > 0 && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">食物过敏史</span>
                    <span className="font-medium text-gray-800">
                      {formData.foodAllergies.includes('none')
                        ? '无食物过敏'
                        : formData.foodAllergies.map(getFoodAllergyLabel).join('、')}
                    </span>
                  </div>
                )}

                {formData.chronicDiseases && formData.chronicDiseases.length > 0 && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">慢性疾病</span>
                    <span className="font-medium text-gray-800">
                      {formData.chronicDiseases.includes('none')
                        ? '无慢性疾病'
                        : formData.chronicDiseases.map(getChronicDiseaseLabel).join('、')}
                    </span>
                  </div>
                )}

                {formData.medications && formData.medications.length > 0 && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">用药情况</span>
                    <span className="font-medium text-gray-800">
                      {formData.medications.includes('none')
                        ? '无用药'
                        : formData.medications.map(getMedicationLabel).join('、')}
                    </span>
                  </div>
                )}

                {formData.sleepQuality && (
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600">睡眠质量</span>
                    <span className="font-medium text-gray-800">
                      {formData.sleepQuality === 'good' ? '良好' :
                       formData.sleepQuality === 'fair' ? '一般' :
                       formData.sleepQuality === 'poor' ? '较差' :
                       formData.sleepQuality === 'irregular' ? '不规律' : '未设定'}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 总结文字 */}
            <div className="bg-gradient-to-r from-red-50 to-pink-50 rounded-lg p-6 text-center">
              <p className="text-gray-700 mb-4">
                感谢您完成健康状况采集！我们已经了解了您的健康状况，这将帮助我们为您制定更安全、更有效的健康计划。
              </p>
              <p className="text-sm text-gray-600">
                点击"继续"继续下一阶段的信息收集。
              </p>
            </div>
          </div>
        )
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        // 中医体质相关症状阶段总结
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                您的中医体质相关症状总结
              </h2>
            </div>

            {/* 中医体质症状卡片 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600">🌿</span>
                </div>
                <h3 className="font-medium text-gray-800">中医体质相关症状</h3>
              </div>

              <div className="space-y-3">
                {formData.spineDiscomfort && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">脊椎不适</span>
                    <span className="font-medium text-gray-800">
                      {formData.spineDiscomfort === 'yes' ? '是，经常感到颈椎或腰椎不适' :
                       formData.spineDiscomfort === 'sometimes' ? '偶尔感到脊椎不适' :
                       formData.spineDiscomfort === 'no' ? '否，脊椎状况良好' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.fatigue && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">疲劳感</span>
                    <span className="font-medium text-gray-800">
                      {formData.fatigue === 'yes' ? '是，经常感到疲劳、精神不振' :
                       formData.fatigue === 'no' ? '否' :
                       formData.fatigue === 'sometimes' ? '偶尔' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.digestiveIssues && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">消化问题</span>
                    <span className="font-medium text-gray-800">
                      {formData.digestiveIssues === 'yes' ? '是，经常有消化不良等问题' :
                       formData.digestiveIssues === 'sometimes' ? '偶尔有消化问题' :
                       formData.digestiveIssues === 'no' ? '否，消化功能正常' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.workStressLevel && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">工作压力水平</span>
                    <span className="font-medium text-gray-800">
                      {formData.workStressLevel === 'high' ? '高压力' :
                       formData.workStressLevel === 'medium' ? '中等压力' :
                       formData.workStressLevel === 'low' ? '低压力' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.sweating && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">出汗情况</span>
                    <span className="font-medium text-gray-800">
                      {formData.sweating === 'yes' ? '是，容易出汗且汗后感觉更累' :
                       formData.sweating === 'no' ? '否' :
                       formData.sweating === 'onlyExercise' ? '仅运动后出汗' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.backPain && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">腰痛四肢无力</span>
                    <span className="font-medium text-gray-800">
                      {formData.backPain === 'yes' ? '是，经常感到腰部酸痛、四肢无力' :
                       formData.backPain === 'sometimes' ? '偶尔' :
                       formData.backPain === 'no' ? '否' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.appetite && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">食欲状况</span>
                    <span className="font-medium text-gray-800">
                      {formData.appetite === 'yes' ? '是，容易没胃口，或吃一点就饱' :
                       formData.appetite === 'no' ? '否' :
                       formData.appetite === 'normal' ? '正常' : '未设定'}
                    </span>
                  </div>
                )}

                {formData.menstrualRegularity && formData.gender === 'female' && (
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600">月经规律性</span>
                    <span className="font-medium text-gray-800">
                      {formData.menstrualRegularity === 'yes' ? '规律' :
                       formData.menstrualRegularity === 'no' ? '不规律' :
                       formData.menstrualRegularity === 'sometimes' ? '偶尔' : '未设定'}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 总结文字 */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 text-center">
              <p className="text-gray-700 mb-4">
                感谢您完成中医体质相关症状采集！我们已经收集了您的完整健康信息，现在可以为您生成个性化的健康评估报告。
              </p>
              <p className="text-sm text-gray-600">
                点击"开始评估"生成您的健康评估报告。
              </p>
            </div>
          </div>
        )
      } else {
        // 其他阶段的简单总结
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-green-500 to-blue-500 rounded-full mx-auto mb-8 flex items-center justify-center">
                <span className="text-4xl">✅</span>
              </div>

              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                {currentStepConfig.title}
              </h2>

              <div className="bg-white bg-opacity-70 rounded-lg p-6 mx-4 mb-6 text-left">
                <p className="text-gray-700 mb-4">
                  感谢您完成本阶段的信息收集！我们已经记录了您的相关信息，这将帮助我们为您制定更精准的健康方案。
                </p>
                <p className="text-sm text-gray-600">
                  点击"继续"继续下一阶段的信息收集。
                </p>
              </div>
            </div>
          </div>
        );
      }
    }

    // 显示健康目标相关建议
    if (currentStepConfig.id === 'weightLossGoal' && inputValues.weightLossGoal) {
      const goalValue = parseFloat(inputValues.weightLossGoal);
      const unit = form.getValues('weightLossGoalUnit');
      let recommendedTime = '';
      
      if (!isNaN(goalValue)) {
        if (unit === 'kg') {
          if (goalValue <= 5) {
            recommendedTime = '3-4个月';
          } else if (goalValue <= 10) {
            recommendedTime = '6-8个月';
          } else {
            recommendedTime = '8-12个月';
          }
        } else if (unit === 'lb') {
          const kgEquivalent = goalValue * 0.453592;
          if (kgEquivalent <= 5) {
            recommendedTime = '3-4个月';
          } else if (kgEquivalent <= 10) {
            recommendedTime = '6-8个月';
          } else {
            recommendedTime = '8-12个月';
          }
        } else if (unit === 'bodyFat') {
          if (goalValue <= 3) {
            recommendedTime = '3-4个月';
          } else if (goalValue <= 6) {
            recommendedTime = '6-8个月';
          } else {
            recommendedTime = '8-12个月';
          }
        }
      }
      
      if (recommendedTime) {
        setTimeout(() => {
          const recommendationElement = document.getElementById('goal-recommendation');
          if (recommendationElement) {
            recommendationElement.classList.remove('opacity-0');
          }
        }, 500);
      }
    }
    
    // 月经规律性问题只对女性显示
    if (currentStepConfig.id === 'menstrualRegularity' && form.getValues('gender') !== 'female') {
      return (
        <div className="text-center text-gray-500 italic">
          <p>此问题仅适用于女性用户，系统已自动跳过。</p>
        </div>
      );
    }
    
    // 出生日期选择
    if (currentStepConfig.type === 'birthDate') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col items-center">
                    <DatePicker
                      value={field.value}
                      onChange={(date) => {
                        field.onChange(date);
                      }}
                      minYear={1940}
                      maxYear={new Date().getFullYear()}
                    />
                    <div className="mt-4 text-center text-gray-500">
                      <p>您的年龄: {new Date().getFullYear() - field.value.getFullYear()} 岁</p>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="text-center mt-4">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 体重选择
    if (currentStepConfig.id === 'weight') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="weight"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col items-center">
                    <WeightGauge
                      value={parseFloat(inputValues.weight) || 65}
                      onChange={(value) => {
                        const newValue = value.toString();
                        // 更新本地状态
                        setInputValues(prev => ({
                          ...prev,
                          weight: newValue
                        }));
                        
                        // 同时更新表单值
                        field.onChange(newValue);
                        
                        // 重新计算BMI
                        if (inputValues.height) {
                          calculateBMI(inputValues.height, form.getValues('heightUnit'), newValue, form.getValues('weightUnit'));
                        }
                      }}
                      minWeight={form.getValues('weightUnit') === 'kg' ? 30 : 66}
                      maxWeight={form.getValues('weightUnit') === 'kg' ? 200 : 440}
                      label={form.getValues('weightUnit') === 'kg' ? '千克' : '磅'}
                      rangeFormat={`范围: ${form.getValues('weightUnit') === 'kg' ? '30 - 200' : '66 - 440'} ${form.getValues('weightUnit')}`}
                    />
                    
                    {/* 单位选择 */}
                    <div className="flex gap-2 mt-6">
                      {currentStepConfig.units?.map((unit) => (
                        <Button
                          key={unit.value}
                          type="button"
                          variant={form.getValues('weightUnit') === unit.value ? "default" : "outline"}
                          onClick={() => {
                            const currentUnit = form.getValues('weightUnit');
                            const newUnit = unit.value;
                            const currentValue = inputValues.weight;
                            
                            // 只有当单位发生变化且有当前值时才进行转换
                            if (currentUnit !== newUnit && currentValue) {
                              let newValue = '';
                              
                                                          // 体重单位转换
                            if (currentUnit === 'kg' && newUnit === 'lb') {
                              // 千克转磅 (1千克 ≈ 2.2046磅)
                              const lbValue = parseFloat(currentValue) * 2.2046;
                              // 直接使用转换值，不强制最小值
                              newValue = lbValue.toFixed(1);
                            } else if (currentUnit === 'lb' && newUnit === 'kg') {
                              // 磅转千克 (1磅 ≈ 0.4536千克)
                              const kgValue = parseFloat(currentValue) * 0.4536;
                              // 确保转换后的值在新的范围内
                              const minKg = 30;
                              const maxKg = 200;
                              const adjustedValue = Math.max(minKg, Math.min(maxKg, kgValue));
                              newValue = adjustedValue.toFixed(1);
                              }
                              
                              if (newValue) {
                                                              // 更新本地状态
                              setInputValues(prev => ({
                                ...prev,
                                weight: newValue
                              }));
                              
                              // 更新表单值
                              form.setValue('weight', newValue);
                              
                              // 强制刷新组件以更新仪表盘
                              setTimeout(() => {
                                form.trigger('weight');
                              }, 0);
                            }
                          }
                          
                          // 更新单位
                          form.setValue('weightUnit', unit.value as any);
                          
                          // 如果有身高值，立即重新计算BMI
                          if (inputValues.height) {
                            calculateBMI(inputValues.height, form.getValues('heightUnit'), inputValues.weight, unit.value);
                          }
                          }}
                          className="min-w-[100px]"
                        >
                          {unit.label}
                        </Button>
                      ))}
                    </div>
                    
                    {/* BMI显示 */}
                    {calculatedBMI !== null && (
                      <div className="bg-muted p-4 rounded-md mt-6 w-full max-w-md">
                        <h4 className="font-medium mb-1">您的BMI指数</h4>
                        <div className="flex items-center justify-between">
                          <p className="text-2xl font-bold">{calculatedBMI}</p>
                          <p className={`text-sm font-medium ${
                            bmiCategory === '正常范围' ? 'text-green-600' : 
                            bmiCategory === '体重过轻' ? 'text-yellow-600' : 
                            'text-red-600'
                          }`}>
                            {bmiCategory}
                          </p>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          BMI是体重指数(Body Mass Index)的缩写，根据身高和体重来计算
                        </p>
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 时间范围选择
    if (currentStepConfig.type === 'timeframe') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name={currentStepConfig.id as any}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <div className="grid grid-cols-4 gap-2">
                    {Array.from({ length: 6 }, (_, i) => i + 1).map((month) => (
                      <Button
                        key={month}
                        type="button"
                        variant={field.value === month.toString() ? "default" : "outline"}
                        onClick={() => {
                          form.setValue(currentStepConfig.id as any, month.toString());
                        }}
                        className="h-12"
                      >
                        {month} 个月
                      </Button>
                    ))}
                  </div>
                </FormControl>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription>{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
          {/* 已移除健康目标时间建议 */}
        </div>
      )
    }
    
    // 多选功能
    if (currentStepConfig.type === 'multiSelect') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name={currentStepConfig.id as any}
            render={({ field }) => (
              <FormItem>
                <div className="grid grid-cols-2 gap-3">
                  {currentStepConfig.options?.map((option) => {
                    // 对于需要使用本地状态的字段
                    let isSelected = false;
                    switch(currentStepConfig.id) {
                      case 'favoriteFoods':
                        isSelected = selectedFavoriteFoods.includes(option.value);
                        break;
                      case 'dislikedFoods':
                        isSelected = selectedDislikedFoods.includes(option.value);
                        break;
                      case 'personalDietaryRestrictions':
                        isSelected = selectedPersonalDietaryRestrictions.includes(option.value);
                        break;
                      default:
                        isSelected = field.value?.includes(option.value);
                        break;
                    }
                    
                    return (
                      <Card
                        key={option.value}
                        className={cn(
                          "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                          isSelected
                            ? "ring-2 ring-blue-500 shadow-md"
                            : "hover:border-blue-200"
                        )}
                        onClick={() => {
                          const currentValues = Array.isArray(field.value) ? [...field.value] : [];
                          let newValues: string[] = [];
                          
                          if (option.value === 'none') {
                            // 如果选择"没有明显反应"，清除其他选项
                            newValues = isSelected ? [] : ['none'];
                          } else {
                            // 如果选择其他选项，移除"没有明显反应"
                            newValues = isSelected
                              ? currentValues.filter(val => val !== option.value)
                              : [...currentValues.filter(val => val !== 'none'), option.value];
                          }
                          
                          // 更新表单值
                          form.setValue(currentStepConfig.id as any, newValues, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          
                          // 更新相应的本地状态
                          switch(currentStepConfig.id) {
                            case 'dietReactions':
                              setSelectedDietReactions(newValues);
                              break;
                            case 'favoriteFoods':
                              setSelectedFavoriteFoods(newValues);
                              break;
                            case 'dislikedFoods':
                              setSelectedDislikedFoods(newValues);
                              break;
                            case 'personalDietaryRestrictions':
                              setSelectedPersonalDietaryRestrictions(newValues);
                              break;
                          }
                        }}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div className="text-xl">{option.icon}</div>
                          <span className="text-sm">{option.label}</span>
                        </div>
                      </Card>
                    );
                  })}
                </div>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="mt-3">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
          {/* 已移除节食和零食习惯相关提示 */}
        </div>
      );
    }
    
    if (currentStepConfig.type === 'input') {
      return (
        <FormField
          control={form.control}
          name={currentStepConfig.id as any}
          render={({ field }) => (
            <FormItem className="w-full">
              <FormControl>
                <div className="flex gap-2">
                  <Input 
                    type="number" 
                    placeholder={currentStepConfig.placeholder}
                    className="text-lg p-6"
                    value={inputValues[currentStepConfig.id as keyof typeof inputValues] || ''}
                    onChange={(e) => {
                      // 更新本地状态
                      setInputValues(prev => ({
                        ...prev,
                        [currentStepConfig.id]: e.target.value
                      }));
                      
                      // 同时更新表单值
                      form.setValue(currentStepConfig.id as any, e.target.value);
                    }}
                    onBlur={field.onBlur}
                    name={field.name}
                    ref={field.ref}
                  />
                  {currentStepConfig.units && (
                    <div className="flex gap-2">
                      {currentStepConfig.units.map((unit) => (
                        <Button
                          key={unit.value}
                          type="button"
                          variant={
                            // 已移除对减重目标单位的处理
                            form.getValues(`${currentStepConfig.id}Unit` as any) === unit.value 
                              ? "default" 
                              : "outline"
                          }
                          onClick={() => {
                            const currentUnit = form.getValues(`${currentStepConfig.id}Unit` as any);
                            const newUnit = unit.value;
                            const currentValue = inputValues[currentStepConfig.id as keyof typeof inputValues];
                            
                            // 已移除减重目标单位更新逻辑
                            
                            // 只有当单位发生变化且有当前值时才进行转换
                            if (currentUnit !== newUnit && currentValue) {
                              let newValue = '';
                              
                              // 身高单位转换
                              if (currentStepConfig.id === 'height') {
                                if (currentUnit === 'cm' && newUnit === 'inch') {
                                  // 厘米转英寸 (1厘米 ≈ 0.3937英寸)
                                  newValue = (parseFloat(currentValue) * 0.3937).toFixed(1);
                                } else if (currentUnit === 'inch' && newUnit === 'cm') {
                                  // 英寸转厘米 (1英寸 ≈ 2.54厘米)
                                  newValue = (parseFloat(currentValue) * 2.54).toFixed(1);
                                }
                              }
                              // 体重单位转换
                              else if (currentStepConfig.id === 'weight') {
                                if (currentUnit === 'kg' && newUnit === 'lb') {
                                  // 千克转磅 (1千克 ≈ 2.2046磅)
                                  newValue = (parseFloat(currentValue) * 2.2046).toFixed(1);
                                } else if (currentUnit === 'lb' && newUnit === 'kg') {
                                  // 磅转千克 (1磅 ≈ 0.4536千克)
                                  newValue = (parseFloat(currentValue) * 0.4536).toFixed(1);
                                } else if ((currentUnit === 'kg' || currentUnit === 'lb') && newUnit === 'bodyFat') {
                                  // 切换到体脂率时，设置默认值为5%
                                  newValue = '5';
                                } else if (newUnit === 'kg' && currentUnit === 'bodyFat') {
                                  // 从体脂率切换到千克，设置默认值为5千克
                                  newValue = '5';
                                } else if (newUnit === 'lb' && currentUnit === 'bodyFat') {
                                  // 从体脂率切换到磅，设置默认值为10磅
                                  newValue = '11';
                                }
                              }
                              
                              if (newValue) {
                                // 更新本地状态
                                setInputValues(prev => ({
                                  ...prev,
                                  [currentStepConfig.id]: newValue
                                }));
                                
                                // 更新表单值
                                form.setValue(currentStepConfig.id as any, newValue, {
                                  shouldValidate: true,
                                  shouldDirty: true,
                                  shouldTouch: true
                                });
                              }
                            }
                            
                            // 更新单位
                            form.setValue(`${currentStepConfig.id}Unit` as any, unit.value as any, {
                              shouldValidate: true,
                              shouldDirty: true,
                              shouldTouch: true
                            });
                            
                            // 如果是身高或体重单位改变，立即重新计算BMI
                            if ((currentStepConfig.id === 'height' || currentStepConfig.id === 'weight') && 
                                inputValues.height && inputValues.weight) {
                              const heightUnit = currentStepConfig.id === 'height' ? newUnit : form.getValues('heightUnit');
                              const weightUnit = currentStepConfig.id === 'weight' ? newUnit : form.getValues('weightUnit');
                              calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
                            }
                          }}
                        >
                          {unit.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )
    }
    
    // 渲染体脂率输入（当用户选择"知道体脂率"时）
    if (currentStepConfig.id === 'knowsBodyFat' && (knowsBodyFatSelection === 'yes' || form.getValues('knowsBodyFat') === 'yes')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsBodyFatSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsBodyFatSelection(option.value);
                  
                  // 因为知道体脂率选择"是"后不需要立即前进，所以这里不添加自动跳转
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormField
            control={form.control}
            name="bodyFatPercentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>体脂率 (%)</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入您的体脂率" 
                    value={inputValues.bodyFatPercentage}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      setInputValues(prev => ({
                        ...prev,
                        bodyFatPercentage: newValue
                      }));
                      // 使用正确的参数设置表单值，确保触发验证
                      form.setValue('bodyFatPercentage', newValue, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true
                      });
                      
                      // 如果有错误，清除错误
                      if (form.formState.errors.bodyFatPercentage) {
                        form.clearErrors('bodyFatPercentage');
                      }
                    }}
                    onKeyDown={(e) => {
                      // 当用户按下回车键时，如果输入有效，自动进入下一步
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const value = inputValues.bodyFatPercentage;
                        if (value && parseFloat(value) > 0 && parseFloat(value) <= 100) {
                          handleNext();
                        }
                      }
                    }}
                    onBlur={(e) => {
                      field.onBlur();
                      // 在失去焦点时也验证一次
                      const value = e.target.value;
                      if (!value || value.trim() === '') {
                        form.setError('bodyFatPercentage', {
                          type: 'manual',
                          message: '请输入您的体脂率'
                        });
                      } else {
                        const numericValue = parseFloat(value);
                        if (isNaN(numericValue) || numericValue <= 0 || numericValue > 100) {
                          form.setError('bodyFatPercentage', {
                            type: 'manual',
                            message: '请输入有效的体脂率数值（1-100之间）'
                          });
                        } else {
                          form.clearErrors('bodyFatPercentage');
                        }
                      }
                    }}
                    name={field.name}
                    ref={field.ref}
                  />
                </FormControl>
                <FormDescription>
                  体脂率是指体内脂肪重量在总体重中所占的百分比
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )
    }
    
    // 为"不知道体脂率"选项提供链接
    if (currentStepConfig.id === 'knowsBodyFat' && (knowsBodyFatSelection === 'no' || form.getValues('knowsBodyFat') === 'no')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsBodyFatSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsBodyFatSelection(option.value);
                  
                  // 为了更好的用户体验，选择后自动前进到下一步
                  setTimeout(() => {
                    handleNext();
                  }, 300);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <div className="text-sm text-blue-600">
            <Link href="#" className="underline">
              了解如何测量体脂率
            </Link>
          </div>
        </div>
      )
    }
    
    return (
      <div className="w-full flex flex-col gap-3">
        {currentStepConfig.options?.map((option) => (
          <Card
            key={option.value}
            className={cn(
              "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
              // 对于需要使用本地状态的字段，使用本地状态来确定是否选中，否则使用表单值
              (currentStepConfig.id === 'gender' && selectedGender === option.value) || 
              (currentStepConfig.id === 'religiousDietaryRestrictions' && selectedReligiousDietaryRestriction === option.value) || 
              form.getValues(currentStepConfig.id as any) === option.value
                ? "ring-2 ring-blue-500 shadow-md"
                : "hover:border-blue-200"
            )}
            onClick={() => {
              // 更新表单值
              form.setValue(currentStepConfig.id as any, option.value as any, {
                shouldValidate: true, // 立即触发验证
                shouldDirty: true,    // 标记为已修改
                shouldTouch: true     // 标记为已交互
              });
              
              // 添加视觉反馈并触发表单验证
              const selectedValue = form.getValues(currentStepConfig.id as any);
              console.log("选择了:", option.value, "当前值:", selectedValue);
              
              // 更新相应的本地状态以确保UI响应
              switch(currentStepConfig.id) {
                case 'gender':
                  setSelectedGender(option.value);
                  break;
                case 'religiousDietaryRestrictions':
                  setSelectedReligiousDietaryRestriction(option.value);
                  break;
              }
              
              // 为了解决点击不响应问题，强制更新表单状态
              setTimeout(() => {
                // 对于某些步骤，可以自动前进到下一步
                if (currentStepConfig.id === 'gender' || 
                    currentStepConfig.id === 'religiousDietaryRestrictions') {
                  handleNext();
                }
              }, 200);
            }}
          >
            <div className="flex items-center gap-4 w-full justify-between">
              <div className="flex items-center gap-4">
                <div className="text-2xl">{option.icon}</div>
                <span className="text-base">{option.label}</span>
              </div>
            </div>
          </Card>
        ))}
      </div>
    )
  }

  // 计算进度百分比
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100

  return (
    <div className="w-full max-w-xl mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <span>都市白领健康信息采集</span>
        </h2>
        <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-md inline-block mt-1 mb-2 text-sm font-medium">
          {currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS ? '中医体质相关症状' :
           currentPhase === COLLECTION_PHASES.HEALTH_STATUS ? '健康状况采集' :
           currentPhase === COLLECTION_PHASES.DIET_PREFERENCES ? '饮食偏好采集' :
           currentPhase === COLLECTION_PHASES.HEALTH_GOALS ? '健康目标采集' : '身体数据采集'}
        </div>
        <p className="text-gray-600 mt-2">
          {currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS
            ? '请提供您的中医体质相关症状信息，以便我们为您提供更全面的健康评估。'
            : currentPhase === COLLECTION_PHASES.HEALTH_STATUS
            ? '请提供您的健康状况信息，以便我们为您制定更安全、更有效的健康计划。'
            : currentPhase === COLLECTION_PHASES.DIET_PREFERENCES
              ? '请提供您的饮食偏好信息，以便我们为您定制更适合办公室工作的饮食方案。'
              : currentPhase === COLLECTION_PHASES.HEALTH_GOALS
                ? '请设置您的健康目标，以便我们为您制定科学合理的健康计划。'
                : '请提供以下身体数据，以便我们为您制定适合都市白领的健康饮食计划。'
          }
        </p>
      </div>
      
      {/* 进度指示器 */}
      <div className="mb-12">
        <h3 className="text-base font-medium text-gray-700 mb-2">
          {currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS ? '中医体质相关症状进度' :
           currentPhase === COLLECTION_PHASES.HEALTH_STATUS ? '健康状况采集进度' :
           currentPhase === COLLECTION_PHASES.DIET_PREFERENCES ? '饮食偏好采集进度' :
           currentPhase === COLLECTION_PHASES.HEALTH_GOALS ? '健康目标采集进度' : '身体数据采集进度'}
        </h3>
        <div className="flex justify-between items-center mb-1 text-sm">
          <span>步骤 {currentStep + 1} / {totalSteps}</span>
          <span>{progressPercentage.toFixed(0)}%</span>
        </div>
        <div className="mb-1 text-xs text-gray-500">
          <span>点击下方进度条可快速跳转到任意步骤</span>
        </div>
        <div className="relative w-full h-8 py-3">
          <div className="relative w-full bg-gray-200 h-2 rounded-full overflow-hidden cursor-pointer hover:bg-gray-300 transition-colors"
            title="点击跳转到相应步骤"
            onClick={(e) => {
              // 获取点击位置相对于进度条的百分比
              const rect = e.currentTarget.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const percentPosition = x / rect.width;
              
              // 将百分比位置转换为步骤索引
              const stepIndex = Math.min(
                Math.max(Math.floor(percentPosition * totalSteps), 0), 
                totalSteps - 1
              );
              
              // 允许跳转到任意步骤
              setCurrentStep(stepIndex);
            }}
          >
          <div 
            className="h-full bg-gradient-to-r from-blue-500 to-green-400"
            style={{ width: `${progressPercentage}%` }}
          ></div>
            
            {/* 添加步骤标记 */}
            <div className="absolute top-0 left-0 w-full h-full pointer-events-none flex items-center">
              {/* 显示所有步骤的点 */}
              {Array.from({ length: totalSteps }).map((_, index) => {
                const isCompleted = index < currentStep;
                const isCurrent = index === currentStep;
                // 主要步骤位置
                const isMainStep = [0, 7, 11, 23, 27, totalSteps-1].includes(index);
                
                return (
                  <div 
                    key={index}
                    className={`absolute rounded-full transform -translate-y-1/2 -translate-x-1/2 ${
                      isCurrent 
                        ? 'bg-blue-600 ring-2 ring-blue-300' 
                        : isCompleted 
                          ? 'bg-green-500 hover:ring-1 hover:ring-green-200' 
                          : 'bg-gray-400 hover:ring-1 hover:ring-gray-200'
                    } transition-all`}
                    style={{ 
                      left: `${(index / (totalSteps - 1)) * 100}%`, 
                      top: '50%',
                      width: isMainStep ? '12px' : '8px',
                      height: isMainStep ? '12px' : '8px'
                    }}
                    title={`步骤 ${index + 1}`}
                  ></div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      
      <Form {...form}>
        <form className="space-y-8">
          {/* 问题标题 */}
          <h3 className="text-xl font-medium text-center">
            {currentPhaseSteps[currentStep].title}
          </h3>
          
          {/* 当前步骤内容 */}
          {renderCurrentStep()}
          
          {/* 导航按钮 */}
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              className="flex items-center gap-1"
            >
              <ChevronLeft size={16} /> 上一步
            </Button>
            
            <Button
              type="button"
              onClick={handleNext}
              className="flex items-center gap-1"
            >
              {currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS && currentStep === totalSteps - 1
                ? '开始评估'
                : currentStep === totalSteps - 1
                  ? '继续'
                  : '下一步'} <ChevronRight size={16} />
            </Button>
          </div>
          
        </form>
      </Form>
    </div>
  )
}
'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Loader2, ShoppingCart, Package, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ShoppingList, ShoppingListResponse } from '@/types/shopping-list'
import { ShoppingListViewer } from './shopping-list-viewer'

interface ShoppingListGeneratorProps {
  userId: string
  className?: string
  onListGenerated?: (shoppingList: ShoppingList) => void
}

export function ShoppingListGenerator({ 
  userId, 
  className = '', 
  onListGenerated 
}: ShoppingListGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [includeDietPlan, setIncludeDietPlan] = useState(true)
  const [includeHerbal<PERSON>lan, setIncludeHerbalPlan] = useState(true)
  const [shoppingList, setShoppingList] = useState<ShoppingList | null>(null)
  const [error, setError] = useState<string>('')

  const handleGenerateList = async () => {
    if (!userId) {
      setError('用户ID不能为空')
      return
    }

    if (!includeDietPlan && !includeHerbalPlan) {
      setError('请至少选择一种计划类型')
      return
    }

    setIsGenerating(true)
    setError('')

    try {
      const response = await fetch('/api/generate-shopping-list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          includeDietPlan,
          includeHerbalPlan
        })
      })

      const result: ShoppingListResponse = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '生成购物清单失败')
      }

      if (result.success && result.shoppingList) {
        setShoppingList(result.shoppingList)
        setError('')
        // 通知父组件清单已生成
        if (onListGenerated) {
          onListGenerated(result.shoppingList)
        }
      } else {
        throw new Error(result.error || '生成购物清单失败')
      }
    } catch (error) {
      console.error('生成购物清单错误:', error)
      setError(error instanceof Error ? error.message : '生成购物清单失败，请稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleRegenerate = () => {
    setShoppingList(null)
    handleGenerateList()
  }

  if (shoppingList) {
    return (
      <ShoppingListViewer 
        shoppingList={shoppingList} 
        onRegenerate={handleRegenerate}
        isRegenerating={isGenerating}
        className={className}
      />
    )
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-purple-50">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <ShoppingCart className="w-8 h-8 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-800">
            智能购物清单生成
          </CardTitle>
          <CardDescription className="text-lg text-gray-600 max-w-2xl mx-auto">
            基于您的饮食计划和中药方案，自动生成分类购物清单
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 选项设置 */}
          <div className="bg-white rounded-lg p-6 border border-blue-100">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Package className="w-5 h-5 text-blue-600 mr-2" />
              清单内容选择
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="diet-plan" className="text-base font-medium">
                    包含饮食计划食材
                  </Label>
                  <p className="text-sm text-gray-600">
                    从您的一周饮食计划中提取所需食材
                  </p>
                </div>
                <Switch
                  id="diet-plan"
                  checked={includeDietPlan}
                  onCheckedChange={setIncludeDietPlan}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="herbal-plan" className="text-base font-medium">
                    包含中药方案药材
                  </Label>
                  <p className="text-sm text-gray-600">
                    从您的中医药方案中提取所需药材
                  </p>
                </div>
                <Switch
                  id="herbal-plan"
                  checked={includeHerbalPlan}
                  onCheckedChange={setIncludeHerbalPlan}
                />
              </div>
            </div>
          </div>

          {/* 功能介绍 */}
          <div className="bg-white rounded-lg p-6 border border-purple-100">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              购物清单特色功能
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">智能分类</div>
                  <div>按食材和药材类别自动分类整理</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">数量汇总</div>
                  <div>自动计算所需食材和药材数量</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">来源追踪</div>
                  <div>显示每个食材在哪些菜品中使用</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">便捷编辑</div>
                  <div>支持添加、删除、修改清单项目</div>
                </div>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 生成按钮 */}
          <div className="text-center">
            <Button
              onClick={handleGenerateList}
              disabled={isGenerating || (!includeDietPlan && !includeHerbalPlan)}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 text-lg font-medium"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin mr-2" />
                  正在生成购物清单...
                </>
              ) : (
                <>
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  生成智能购物清单
                </>
              )}
            </Button>
          </div>

          {/* 提示信息 */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <div className="font-medium mb-1">温馨提示</div>
                <div>
                  购物清单将根据您的饮食计划和中药方案自动生成，您可以在生成后进行编辑调整。
                  清单支持PDF导出和打印功能，方便您购物时使用。
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

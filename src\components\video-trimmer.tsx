'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON>lider } from '@/components/ui/slider'
import { Progress } from '@/components/ui/progress'
import { Play, Pause, Scissors, AlertTriangle } from 'lucide-react'

interface VideoTrimmerProps {
  videoFile: File
  onTrimComplete: (trimmedFile: File) => void
  onCancel: () => void
  i18n: any // 新增i18n参数
}

export function VideoTrimmer({ videoFile, onTrimComplete, onCancel, i18n }: VideoTrimmerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [videoUrl, setVideoUrl] = useState<string>('')

  // 初始化视频
  useEffect(() => {
    const url = URL.createObjectURL(videoFile)
    setVideoUrl(url)

    return () => {
      URL.revokeObjectURL(url)
    }
  }, [videoFile])

  // 视频加载完成
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      const videoDuration = videoRef.current.duration
      setDuration(videoDuration)
      setEndTime(Math.min(videoDuration, 30)) // 默认30秒或视频总长度
    }
  }

  // 播放/暂停切换
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  // 时间更新
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime
      setCurrentTime(current)
      
      // 如果播放超过结束时间，暂停视频
      if (current >= endTime) {
        videoRef.current.pause()
        setIsPlaying(false)
        videoRef.current.currentTime = startTime
      }
    }
  }

  // 跳转到指定时间
  const seekTo = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time
      setCurrentTime(time)
    }
  }

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 计算预估文件大小
  const estimateFileSize = (): string => {
    const trimDuration = endTime - startTime
    const originalSize = videoFile.size
    const originalDuration = duration
    
    if (originalDuration === 0) return '计算中...'
    
    const estimatedSize = (originalSize * trimDuration) / originalDuration
    return (estimatedSize / (1024 * 1024)).toFixed(1) + 'MB'
  }

  // 裁剪视频
  const trimVideo = async () => {
    setIsProcessing(true)
    setProgress(0)

    try {
      const trimDuration = endTime - startTime
      const originalSize = videoFile.size
      const estimatedSize = (originalSize * trimDuration) / duration

      setProgress(10)

      // 如果预估大小超过7.5MB，Base64编码后会超过10MB限制
      const maxSizeBeforeBase64 = 7.5 * 1024 * 1024 // 7.5MB
      if (estimatedSize > maxSizeBeforeBase64) {
        setProgress(100)
        alert(`视频片段预估大小为 ${(estimatedSize / (1024 * 1024)).toFixed(1)}MB，Base64编码后会超过10MB限制。\n\n建议：\n1. 选择更短的时间段\n2. 使用外部视频编辑工具压缩后重新上传\n3. 或者选择关键帧截图进行识别`)
        setIsProcessing(false)
        setProgress(0)
        return
      }

      setProgress(20)
      
      // 开始真正的视频裁剪
      await performVideoTrim()
      
    } catch (error) {
      console.error('视频裁剪失败:', error)
      alert('视频裁剪失败，请重试')
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  // 执行实际的视频裁剪
  const performVideoTrim = async () => {
    return new Promise<void>((resolve, reject) => {
      const video = videoRef.current
      const canvas = canvasRef.current
      
      if (!video || !canvas) {
        reject(new Error('视频或画布元素未找到'))
        return
      }

      setProgress(30)

      // 设置canvas尺寸，保持视频比例
      const videoWidth = video.videoWidth || video.clientWidth
      const videoHeight = video.videoHeight || video.clientHeight
      canvas.width = videoWidth
      canvas.height = videoHeight
      
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('无法获取画布上下文'))
        return
      }

      setProgress(40)

      // 创建MediaRecorder来录制裁剪的视频
      const stream = canvas.captureStream(30) // 30fps
      
      // 检查浏览器支持的编码格式，优先使用MP4
      let mimeType = 'video/mp4'
      let outputExtension = '.mp4'
      
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        // 如果不支持MP4，使用WebM（可能需要后续转换）
        mimeType = 'video/webm;codecs=vp9'
        outputExtension = '.webm'
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'video/webm;codecs=vp8'
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'video/webm'
          }
        }
      }
      
      // 计算比特率限制，确保文件大小可控
      const trimDurationSec = endTime - startTime
      const targetSizeMB = 6 // 目标6MB，给Base64编码留出余量
      const maxBitrate = Math.min(
        (targetSizeMB * 8 * 1024 * 1024) / trimDurationSec, // 根据时长计算最大比特率
        1500000 // 最大1.5Mbps，保持合理质量
      )
      
      const mediaRecorder = new MediaRecorder(stream, { 
        mimeType,
        videoBitsPerSecond: Math.floor(maxBitrate * 0.9), // 预留10%给音频
        audioBitsPerSecond: 64000 // 64kbps音频
      })
      const chunks: Blob[] = []
      let hasStarted = false
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        console.log('MediaRecorder stopped, creating file...')
        const blob = new Blob(chunks, { type: mimeType })
        const trimmedFile = new File([blob], `trimmed_${videoFile.name.replace(/\.[^/.]+$/, outputExtension)}`, {
          type: mimeType
        })
        
        // 如果输出的是WebM格式，给用户提示
        if (outputExtension === '.webm') {
          console.warn('输出格式为WebM，可能需要后续转换为MP4以兼容百炼API')
        }
        
        // 延迟一下，确保进度显示到100%
        setTimeout(() => {
          setProgress(100)
          setTimeout(() => {
            onTrimComplete(trimmedFile)
            resolve()
          }, 500) // 让用户看到100%完成
        }, 100)
      }

      mediaRecorder.onerror = (error) => {
        console.error('MediaRecorder error:', error)
        reject(new Error('录制失败: ' + error))
      }

      setProgress(50)

      // 暂停视频，准备跳转
      video.pause()
      
      // 跳转到开始时间
      video.currentTime = startTime
      
      const trimDurationMs = (endTime - startTime) * 1000
      let recordingStartTime: number
      let animationFrame: number
      let isRecording = false

      // 连续渲染函数
      const renderFrame = () => {
        if (!isRecording) return

        // 更新进度
        const elapsed = Date.now() - recordingStartTime
        const progressPercent = Math.min((elapsed / trimDurationMs) * 100, 100)
        const currentProgress = Math.round(50 + (progressPercent * 0.35)) // 四舍五入为整数，50-85% 的进度
        setProgress(currentProgress)

        // 检查是否录制完成
        if (elapsed >= trimDurationMs || video.currentTime >= endTime) {
          console.log('Recording completed, stopping...')
          isRecording = false
          video.pause()
          mediaRecorder.stop()
          cancelAnimationFrame(animationFrame)
          setProgress(90) // 设置为90%，等待文件生成
          return
        }

        // 绘制当前帧到canvas
        if (video.readyState >= 2) { // 确保视频数据可用
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        }
        
        // 继续下一帧
        animationFrame = requestAnimationFrame(renderFrame)
      }

      // 开始录制的函数
      const startRecording = () => {
        if (hasStarted) return
        hasStarted = true
        
        console.log('Starting recording from', startTime, 'to', endTime)
        recordingStartTime = Date.now()
        isRecording = true
        
        // 开始录制
        mediaRecorder.start(100) // 每100ms一个数据块
        
        // 开始播放视频
        video.play().then(() => {
          // 开始渲染循环
          renderFrame()
        }).catch((error) => {
          console.error('Video play failed:', error)
          reject(new Error('视频播放失败'))
        })
      }

      // 监听视频跳转完成
      const onSeeked = () => {
        video.removeEventListener('seeked', onSeeked)
        setTimeout(startRecording, 100) // 稍微延迟确保视频准备好
      }

      video.addEventListener('seeked', onSeeked)

      // 如果视频已经在正确位置，直接开始
      if (Math.abs(video.currentTime - startTime) < 0.1) {
        setTimeout(startRecording, 100)
      }

      // 设置超时保护
      setTimeout(() => {
        if (isRecording) {
          console.log('Recording timeout, stopping...')
          isRecording = false
          video.pause()
          mediaRecorder.stop()
          cancelAnimationFrame(animationFrame)
        }
      }, trimDurationMs + 5000) // 额外5秒保护
    })
  }

  return (
    <div className="w-full space-y-6 p-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">{i18n.videoCrop.subtitle||'视频预览与选择'}</h3>
        <p className="text-sm text-muted-foreground">
          {i18n.videoCrop.description||'选择要识别的视频片段时间段，我们会检查文件大小并给出建议'}
        </p>
      </div>

     

      {/* 视频播放器 */}
      <div className="relative bg-black rounded-lg overflow-hidden">
        {videoUrl ? (
          <video
            ref={videoRef}
            src={videoUrl}
            className="w-full h-auto max-h-64"
            onLoadedMetadata={handleLoadedMetadata}
            onTimeUpdate={handleTimeUpdate}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            controls
          />
        ) : (
          <div className="w-full h-64 flex items-center justify-center text-white">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <p>{i18n.videoCrop.player.loading||'正在加载视频...'}</p>
            </div>
          </div>
        )}
        
        {/* 播放控制覆盖层 */}
        {videoUrl && (
          <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
            <Button
              variant="secondary"
              size="lg"
              onClick={togglePlay}
              className="rounded-full pointer-events-auto"
            >
              {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
            </Button>
          </div>
        )}
      </div>

      {/* 时间轴和裁剪控制 */}
      {videoUrl && duration > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span>{i18n.videoCrop.timeline.currentTime||'当前时间'}: {formatTime(currentTime)}</span>
            <span>{i18n.videoCrop.timeline.duration||'总时长'}: {formatTime(duration)}</span>
          </div>

          {/* 播放进度条 */}
          <div className="relative">
            <Slider
              value={[currentTime]}
              onValueChange={([value]) => seekTo(value)}
              max={duration}
              step={0.1}
              className="w-full"
            />
          </div>

          {/* 裁剪范围选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{i18n.videoCrop.timeline.selectRange||'选择关注的时间段'}:</label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-xs text-muted-foreground">{i18n.videoCrop.timeline.startTime||'开始时间'}</label>
                <Slider
                  value={[startTime]}
                  onValueChange={([value]) => {
                    setStartTime(value)
                    if (value >= endTime) {
                      setEndTime(Math.min(value + 1, duration))
                    }
                  }}
                  max={duration}
                  step={0.1}
                  className="w-full"
                />
                <span className="text-xs text-muted-foreground">{formatTime(startTime)}</span>
              </div>
              <div>
                <label className="text-xs text-muted-foreground">{i18n.videoCrop.timeline.endTime||'结束时间'}</label>
                <Slider
                  value={[endTime]}
                  onValueChange={([value]) => {
                    setEndTime(value)
                    if (value <= startTime) {
                      setStartTime(Math.max(value - 1, 0))
                    }
                  }}
                  max={duration}
                  step={0.1}
                  className="w-full"
                />
                <span className="text-xs text-muted-foreground">{formatTime(endTime)}</span>
              </div>
            </div>
          </div>

          {/* 裁剪信息 */}
          <div className="grid grid-cols-3 gap-4 text-center p-4 bg-muted rounded-lg">
            <div>
              <div className="text-sm font-medium">{i18n.videoCrop.clipInfo.selectedDuration||'选择时长'}</div>
              <div className="text-lg">{formatTime(endTime - startTime)}</div>
            </div>
            <div>
              <div className="text-sm font-medium">{i18n.videoCrop.clipInfo.estimatedSize||'预估大小'}</div>
              <div className="text-lg">{estimateFileSize()}</div>
            </div>
            <div>
              <div className="text-sm font-medium">{i18n.videoCrop.clipInfo.fileStatus||'文件状态'}</div>
              <div className="text-lg">
                {parseFloat(estimateFileSize()) >= 7.5 ? i18n.videoCrop.clipInfo.needsOptimization||'需要优化' : i18n.videoCrop.clipInfo.meetsRequirements||'符合要求'}
              </div>
            </div>
          </div>

          {/* 处理进度 */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>
                  {progress < 30 ? i18n.videoCrop.processing.preparing||'正在准备...' : 
                   progress < 50 ? i18n.videoCrop.processing.settingUp||'正在设置录制...' : 
                   progress < 90 ? i18n.videoCrop.processing.trimming||'正在裁剪视频...' : 
                   i18n.videoCrop.processing.generating||'正在生成文件...'}
                </span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={Math.round(progress)} className="w-full" />
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-4 justify-center">
            <Button variant="outline" onClick={onCancel} disabled={isProcessing}>
              {i18n.videoCrop.actions.cancel||'取消'}
            </Button>
            <Button 
              onClick={trimVideo} 
              disabled={isProcessing || endTime <= startTime}
              className="flex items-center gap-2"
            >
              <Scissors className="h-4 w-4" />
              {isProcessing ? i18n.videoCrop.actions.trimming||'裁剪中...' : i18n.videoCrop.actions.trimAndContinue||'裁剪并继续'}
            </Button>
          </div>
        </div>
      )}
      
      {/* 隐藏的canvas，用于未来的高级功能 */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  )
} 
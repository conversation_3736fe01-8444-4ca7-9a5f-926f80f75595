'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, FlaskConical, Leaf, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { HerbalWeightLossPlan, HerbalPlanResponse } from '@/types/herbal-plan'
import { HerbalPlanViewer } from './herbal-plan-viewer'

interface HerbalPlanGeneratorProps {
  userId: string
  className?: string
  onPlanGenerated?: (herbalPlan: HerbalWeightLossPlan) => void // 新增回调函数
}

export function HerbalPlanGenerator({ userId, className = '', onPlanGenerated }: HerbalPlanGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [herbalPlan, setHerbalPlan] = useState<HerbalWeightLossPlan | null>(null)
  const [error, setError] = useState<string>('')

  const handleGeneratePlan = async () => {
    if (!userId) {
      setError('用户ID不能为空')
      return
    }

    setIsGenerating(true)
    setError('')

    try {
      const response = await fetch('/api/generate-herbal-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId
        })
      })

      const result: HerbalPlanResponse = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '生成中药方案失败')
      }

      if (result.success && result.herbalPlan) {
        setHerbalPlan(result.herbalPlan)
        setError('')
        // 通知父组件方案已生成
        if (onPlanGenerated) {
          onPlanGenerated(result.herbalPlan)
        }
      } else {
        throw new Error(result.error || '生成中药方案失败')
      }
    } catch (error) {
      console.error('生成中药方案错误:', error)
      setError(error instanceof Error ? error.message : '生成中药方案失败，请稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleRegenerate = () => {
    setHerbalPlan(null)
    handleGeneratePlan()
  }

  if (herbalPlan) {
    return (
      <HerbalPlanViewer 
        herbalPlan={herbalPlan} 
        onRegenerate={handleRegenerate}
        isRegenerating={isGenerating}
        className={className}
      />
    )
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-amber-50">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <FlaskConical className="w-8 h-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-800">
            AI 智能中药方案生成
          </CardTitle>
          <CardDescription className="text-lg text-gray-600 max-w-2xl mx-auto">
            基于您的健康评估报告，为您量身定制专业的一周中医药减肥方案
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 功能介绍 */}
          <div className="bg-white rounded-lg p-6 border border-green-100">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Leaf className="w-5 h-5 text-green-600 mr-2" />
              中医药减肥方案特色
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">辨证施治</div>
                  <div>根据体质特点制定个性化方案</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">标本兼治</div>
                  <div>既减肥又调理身体根本问题</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">安全有效</div>
                  <div>天然中药材，副作用小</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800">专业指导</div>
                  <div>详细的用法用量和注意事项</div>
                </div>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 生成按钮 */}
          <div className="text-center">
            <Button
              onClick={handleGeneratePlan}
              disabled={isGenerating}
              size="lg"
              className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 text-lg font-medium"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin mr-2" />
                  AI正在生成中药方案...
                </>
              ) : (
                <>
                  <FlaskConical className="w-5 h-5 mr-2" />
                  生成专属中药方案
                </>
              )}
            </Button>
          </div>

          {/* 免责声明 */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <div className="font-medium mb-1">重要提示</div>
                <div>
                  本系统生成的中药方案仅供参考，实际用药前请务必咨询专业中医师。
                  中药使用需要根据个人具体情况调整，请勿自行用药。
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

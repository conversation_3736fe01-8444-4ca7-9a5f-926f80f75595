'use server';
import { sql } from '@/lib/postgres-client';
import { dishFeedbackSchema } from '@/lib/validations/dish-feedback';
import { FEEDBACK_OPTIONS } from '@/types/dish-feedback';
import { getCurrentUser } from '@/lib/auth';

export async function submitDishFeedback(data: unknown) {
  try {
    const result = dishFeedbackSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        message: '表单数据无效，请检查您的输入。',
      }
    }

    // 获取当前登录用户信息
    const currentUser = await getCurrentUser();
    
    const { dishName, feedbackType, customMessage } = result.data;
    
    // 找到反馈类型的中文标签
    const feedbackLabel = FEEDBACK_OPTIONS.find(option => option.value === feedbackType)?.label || feedbackType;
    
    // 构造存储到message字段的JSON数据
    const feedbackData = {
      type: 'dish_feedback',
      dishName,
      feedbackType,
      feedbackLabel,
      customMessage,
      userId: currentUser?.userId || 'anonymous',
      userEmail: currentUser?.email || 'unknown',
      timestamp: new Date().toISOString()
    };

    const { rows } = await sql`
      INSERT INTO nf_contact (name, email, message)
      VALUES (${'菜品反馈'}, ${currentUser?.email || '<EMAIL>'}, ${JSON.stringify(feedbackData)})
      RETURNING id
    `;

    if (!rows[0]) {
      return {
        success: false,
        message: '提交反馈失败，请重试。',
      };
    }

    return {
      success: true,
      message: '反馈已收到，将尽快优化饮食计划'
    };
  } catch (error) {
    console.error('Dish feedback submission error:', error);
    return {
      success: false,
      message: '发生错误，请稍后重试。',
    };
  }
} 
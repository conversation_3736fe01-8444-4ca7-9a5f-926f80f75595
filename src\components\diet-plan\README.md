# 饮食计划生成组件

## 概述

这是一个基于AI的智能饮食计划生成系统，支持生成普通营养计划和中医膳食计划。

## 功能特性

- ✅ AI智能生成一周详细饮食计划
- ✅ 支持普通营养计划和中医膳食计划两种模式
- ✅ 详细的营养成分分析（热量、蛋白质、碳水化合物、脂肪）
- ✅ 具体的食材分量和烹饪方式指导
- ✅ 美观的响应式界面设计
- ✅ 完整的数据库存储和管理

## 组件结构

```
src/components/diet-plan/
├── diet-plan-generator.tsx    # 饮食计划生成器组件
├── diet-plan-viewer.tsx       # 饮食计划展示组件
├── index.ts                   # 组件导出入口
└── README.md                  # 使用说明
```

## 快速开始

### 1. 安装依赖

确保项目已安装以下依赖：
- Next.js 14+
- TypeScript
- Tailwind CSS
- Radix UI组件
- Lucide React图标

### 2. 数据库设置

执行SQL文件创建必要的数据表：

```sql
-- 执行 src/sql/diet_plans.sql 创建饮食计划表
-- 确保已有 nf_health_assessment_reports 表
```

### 3. 环境变量配置

在 `.env.local` 中配置：

```env
# 通义千问API密钥
DASHSCOPE_API_KEY=your_api_key_here

# 数据库连接
POSTGRES_URL=your_database_url

# NextAuth URL
NEXTAUTH_URL=http://localhost:3000
```

### 4. 基本使用

```tsx
import { DietPlanGenerator } from '@/components/diet-plan'

export default function DietPlanPage() {
  const userId = "user_123" // 用户ID

  return (
    <div className="container mx-auto py-8">
      <DietPlanGenerator userId={userId} />
    </div>
  )
}
```

### 5. 高级使用

```tsx
import { DietPlanGenerator, DietPlanViewer } from '@/components/diet-plan'
import { getUserDietPlan } from '@/actions/diet-plan'

export default async function DietPlanPage({ params }: { params: { userId: string } }) {
  // 检查用户是否已有饮食计划
  const existingPlan = await getUserDietPlan(params.userId)

  if (existingPlan) {
    // 如果已有计划，直接展示
    return (
      <DietPlanViewer 
        dietPlan={existingPlan}
        onRegenerate={() => {/* 重新生成逻辑 */}}
        isRegenerating={false}
      />
    )
  }

  // 如果没有计划，显示生成器
  return <DietPlanGenerator userId={params.userId} />
}
```

## API接口

### POST /api/generate-diet-plan

生成饮食计划的API接口。

**请求体：**
```json
{
  "userId": "string",
  "includeHerbalPlan": boolean
}
```

**响应：**
```json
{
  "success": true,
  "dietPlan": {
    "id": 1,
    "user_id": "user_123",
    "plan_type": "herbal",
    "weekly_plan": { /* 详细一周计划 */ },
    "special_notes": "特别说明",
    "tcm_principles": "中医膳食原理"
  }
}
```

## Action函数

```tsx
import { 
  getUserDietPlan,
  generateDietPlan,
  removeUserDietPlan,
  checkUserHasDietPlan 
} from '@/actions/diet-plan'

// 获取用户饮食计划
const plan = await getUserDietPlan(userId)

// 生成新计划
const result = await generateDietPlan(userId, true) // true表示包含中医膳食

// 删除计划
const success = await removeUserDietPlan(userId)

// 检查是否有计划
const hasPlan = await checkUserHasDietPlan(userId)
```

## 数据模型

### DietPlan 接口

```typescript
interface DietPlan {
  id?: number
  user_id: string
  plan_type: 'normal' | 'herbal'          // 计划类型
  weekly_plan: WeeklyDietPlan             // 一周计划详情
  special_notes: string                    // 特别说明
  tcm_principles?: string                  // 中医膳食原理
  basic_analysis: string                   // 基础健康分析
  diet_recommendations?: string            // 饮食建议
  created_at?: Date
  updated_at?: Date
}
```

### 营养信息结构

```typescript
interface Nutrition {
  calories: number    // 热量(kcal)
  protein: number     // 蛋白质(g)
  carbs: number       // 碳水化合物(g)
  fat: number         // 脂肪(g)
}
```

## 样式自定义

组件使用Tailwind CSS构建，支持通过className传递自定义样式：

```tsx
<DietPlanGenerator 
  userId={userId}
  className="my-custom-class"
/>
```

## 注意事项

1. **前置条件**：用户必须先完成健康评估，系统才能生成个性化饮食计划
2. **API限制**：每次生成需要调用AI API，请注意配额限制
3. **数据存储**：每个用户只能有一个饮食计划，新生成会覆盖旧计划
4. **中医膳食**：只有当健康评估包含diet_recommendations时才能生成中医膳食计划

## 故障排除

### 常见问题

1. **生成失败**：检查是否配置了正确的API密钥
2. **数据库错误**：确保数据表已正确创建
3. **组件不显示**：检查用户ID是否有效
4. **样式问题**：确保Tailwind CSS正确加载

### 调试日志

在浏览器开发者工具中查看控制台日志，API调用会输出详细信息：

```
=== AI返回的饮食计划结果 ===
=== 消耗时间 ===
=== 饮食计划生成完成 ===
```

## 更新日志

- v1.0.0: 初始版本发布
  - 基础饮食计划生成功能
  - 支持普通和中医膳食两种模式
  - 完整的UI组件和数据存储 
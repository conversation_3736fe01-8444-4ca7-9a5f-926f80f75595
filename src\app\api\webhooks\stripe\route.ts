import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers'
import { IOrderDetail, createOrderDetailsFromStripe, refundedOrderDetailsFromStripe, subscriptionCancelledFromStripe } from "@/actions/stripe-billing";
import { setKeyWithExpiry, getKey } from "@/lib/redis-client";
import Stripe from 'stripe';

const expirySeconds = 60 * 60 * 1; // 1 hour

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { stripeAccount: process.env.STRIPE_ACCOUNT_ID });

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {

    try {
        if (!webhookSecret) {
            return new Response("Stripe Webhook Secret not set in .env", {
                status: 500,
            });
        }

        const body = await request.text();
        //const payload = JSON.parse(body);


        const headerPayload = await headers();
        const signature = headerPayload.get('stripe-signature') || '';

        let event = null;

        try {
            event = stripe.webhooks.constructEvent(
                body,
                signature,
                webhookSecret
            );
        } catch (err) {
            return new Response('Invalid signature.', {
                status: 400
            })
        }

        // 防重复处理机制
        const processingKey = `webhook_processed_${event.id}`;
        
        // 检查是否已经处理过
        const alreadyProcessed = await getKey(processingKey);
        if(alreadyProcessed){
          console.log(`Webhook事件 ${event.id} 已处理过，跳过重复处理`);
          return new Response('Webhook already processed', {
            status: 200
          })
        }
        
        // 立即标记为正在处理，防止并发处理
        await setKeyWithExpiry(processingKey, Date.now().toString(), expirySeconds);
        console.log(`标记Webhook事件开始处理: ${event.id}`);

        switch (event.type) {
            case 'payment_intent.succeeded':
                console.log('处理支付成功事件');
                await handlePaymentSuccess(event.data.object as Stripe.PaymentIntent);
                break;
            // case 'charge.succeeded':
            //     console.log('处理订阅付款成功事件');
            //     await handleChargeSuccess(event.data.object as Stripe.Charge);
            //     break;
            // case 'invoice.paid':
            //     console.log('处理订阅发票支付事件');
            //     await handleInvoicePaid(event.data.object as Stripe.Invoice); //subscription
            //     break;
            case 'charge.refunded':
                console.log('处理退款事件');
                await handleRefund(event.data.object as Stripe.Charge);
                break;
            case 'customer.subscription.deleted':
                console.log('处理订阅取消事件');
                await handleSubscriptionCancelled(event.data.object as Stripe.Subscription);
                break;
            default:
                console.log('未处理的事件类型:', event.type, '- 已忽略');
                // 对于未处理的事件类型，返回200状态码避免Stripe重复发送
                break;
        }

        console.log(`Webhook事件 ${event.id} 处理完成`);
        return new Response('Webhook processed successfully', { status: 200 });
    } catch (error: unknown) {
        console.error(error);
        return new Response(`Webhook error: ${error}`, {
            status: 400,
        });
    }
}



async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    console.log("=== 处理支付成功事件 ===");
    console.log("paymentIntent:", JSON.stringify(paymentIntent, null, 2));

    // 检查是否是订阅支付
    if (paymentIntent.invoice) {
        console.log('检测到这是订阅支付的PaymentIntent，直接处理积分');
        console.log('发票ID:', paymentIntent.invoice);
        
        // 从发票中获取订阅和价格信息
        let user_id = paymentIntent.metadata?.userId;
        let priceId = paymentIntent.metadata?.priceId;
        
        // 如果 payment intent 中没有 metadata，从发票和订阅中获取
        if (!user_id || !priceId) {
            console.log('从发票中获取订阅和价格信息');
            try {
                const invoice = await stripe.invoices.retrieve(paymentIntent.invoice as string, {
                    expand: ['lines.data.price', 'subscription'],
                });
                
                // 获取价格ID
                if (invoice.lines.data.length > 0 && invoice.lines.data[0].price) {
                    const lineItem = invoice.lines.data[0];
                    if (lineItem.price && typeof lineItem.price !== 'string') {
                        priceId = lineItem.price.id;
                    }
                }
                
                // 获取用户ID
                if (invoice.subscription) {
                    // 检查是否是字符串ID还是扩展的对象
                    let subscriptionId: string;
                    if (typeof invoice.subscription === 'string') {
                        subscriptionId = invoice.subscription;
                    } else {
                        subscriptionId = invoice.subscription.id;
                        // 如果已经是扩展的对象，直接获取metadata
                        user_id = invoice.subscription.metadata?.userId || '';
                    }
                    
                    // 如果还没有获取到用户ID，再次查询订阅
                    if (!user_id) {
                        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
                        user_id = subscription.metadata?.userId || '';
                    }
                }
                
                console.log('从发票获取的信息:', { user_id, priceId });
            } catch (error) {
                console.error('获取发票信息失败:', error);
                return;
            }
        }
        
        const transaction_id = paymentIntent.payment_method as string;
        const invoice = paymentIntent.invoice as string;
        const price = (paymentIntent.amount || 0) / 100;
        const date = new Date(paymentIntent.created * 1000).toISOString();
        
        console.log('订阅支付解析信息:', {
            transaction_id,
            user_id,
            invoice,
            priceId,
            price,
            date
        });
        
        if (!user_id) {
            console.error('订阅支付中未找到用户ID');
            return;
        }
        
        if (!priceId) {
            console.error('订阅支付中未找到价格ID');
            return;
        }
        
        const orderDetails: IOrderDetail = {
            userId: user_id,
            transactionId: transaction_id,
            invoice: invoice,
            priceId: priceId,
            price: price,
            date: date
        };
        
        console.log('订阅支付订单详情:', JSON.stringify(orderDetails, null, 2));
        await createOrderDetailsFromStripe(orderDetails);
        return;
    }

    // 判断是否为订阅支付（通过description判断）
    const isSubscription = paymentIntent.description?.includes('Subscription');
    const paymentType = isSubscription ? '订阅支付' : '一次性支付';
    
    console.log(`=== 确认为${paymentType}，开始处理 ===`);

    const transaction_id = paymentIntent.payment_method as string;
    let user_id = paymentIntent.metadata?.userId;
    const invoice = paymentIntent.invoice as string;
    let priceId = paymentIntent.metadata?.priceId;
    const price = (paymentIntent.amount || 0) / 100;
    const date = new Date(paymentIntent.created * 1000).toISOString();
    
    // 如果是订阅支付但metadata不完整，从subscription中获取
    if ((!user_id || !priceId) && isSubscription && paymentIntent.customer) {
        console.log("从客户订阅中获取metadata");
        try {
            const subscriptions = await stripe.subscriptions.list({
                customer: typeof paymentIntent.customer === 'string' 
                    ? paymentIntent.customer 
                    : paymentIntent.customer.id,
                limit: 1,
                status: 'active'
            });
            
            if (subscriptions.data.length > 0) {
                const subscription = subscriptions.data[0];
                
                if (!user_id) {
                    user_id = subscription.metadata?.userId || '';
                }
                if (!priceId) {
                    priceId = subscription.metadata?.priceId || '';
                }
            }
        } catch (error) {
            console.error('获取订阅信息失败:', error);
        }
    }
    
    console.log(`${paymentType}解析信息:`, {
        transaction_id,
        user_id,
        invoice,
        priceId,
        price,
        date
    });
    
    if (!user_id) {
        console.error(`${paymentType}中未找到用户ID`);
        return;
    }
    
    if (!priceId) {
        console.error(`${paymentType}中未找到价格ID`);
        return;
    }
    
    const orderDetails: IOrderDetail = {
        userId: user_id,
        transactionId: transaction_id,
        invoice: invoice || '',
        priceId: priceId,
        price: price,
        date: date,
        customerId: typeof paymentIntent.customer === 'string' 
            ? paymentIntent.customer 
            : paymentIntent.customer?.id
    };
    
    console.log(`${paymentType}订单详情:`, JSON.stringify(orderDetails, null, 2));
    await createOrderDetailsFromStripe(orderDetails);
}

// async function handleChargeSuccess(charge: Stripe.Charge) {
//     console.log("=== 处理付款成功事件 ===");

//     if (charge.payment_intent) {
//         try {
//             const paymentIntent = await stripe.paymentIntents.retrieve(
//                 typeof charge.payment_intent === 'string' 
//                     ? charge.payment_intent 
//                     : charge.payment_intent.id
//             );
//             await handlePaymentSuccess(paymentIntent);
//         } catch (error) {
//             console.error('获取payment_intent失败:', error);
//         }
//     } else {
//         console.log('charge事件无关联payment_intent');
//     }
// }

async function handleRefund(charge: Stripe.Charge) {
    //console.log(charge);
    const transaction_id = charge.payment_method as string; // stripe transaction id
    const user_id = charge.metadata.userId; // login user id
    const invoice = charge.invoice as string; // stripe invoice id
    const priceId = charge.metadata.priceId; // price id
    const price = (charge.amount || 0) / 100; // price
    const date = new Date(charge.created * 1000).toISOString();
    const orderDetails: IOrderDetail = {
        userId: user_id,
        transactionId: transaction_id,
        invoice: invoice,
        priceId: priceId,
        price: price,
        date: date
    };
    await refundedOrderDetailsFromStripe(orderDetails);
}

async function handleSubscriptionCancelled(subscription: Stripe.Subscription) {
    const subscriptionId = subscription.id; // subscription id
    await subscriptionCancelledFromStripe(subscriptionId);
}


export async function GET(request: NextRequest) {
    return new Response("", { status: 200 });
}
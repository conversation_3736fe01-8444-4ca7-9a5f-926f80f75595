'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  ShoppingCart, 
  Edit3, 
  Download, 
  Printer, 
  Plus, 
  Trash2, 
  Check, 
  X,
  RefreshCw, 
  Loader2,
  Package,
  Leaf,
  Eye,
  Save
} from 'lucide-react'
import { 
  ShoppingList, 
  ShoppingListItem, 
  CategorizedShoppingList,
  FoodCategory,
  HerbalCategory,
  FoodCategoryNames,
  HerbalCategoryNames
} from '@/types/shopping-list'
import { exportShoppingListToPDF } from '@/lib/pdf-export'

interface ShoppingListViewerProps {
  shoppingList: ShoppingList
  onRegenerate?: () => void
  isRegenerating?: boolean
  className?: string
}

export function ShoppingListViewer({ 
  shoppingList, 
  onRegenerate, 
  isRegenerating = false, 
  className = '' 
}: ShoppingListViewerProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedItems, setEditedItems] = useState<ShoppingListItem[]>(shoppingList.items)
  const [selectedItem, setSelectedItem] = useState<ShoppingListItem | null>(null)
  const [showItemDetails, setShowItemDetails] = useState(false)
  const [newItemName, setNewItemName] = useState('')
  const [newItemQuantity, setNewItemQuantity] = useState('')
  const [showAddDialog, setShowAddDialog] = useState(false)

  // 将购物清单按类别分组
  const categorizeItems = (items: ShoppingListItem[]): CategorizedShoppingList => {
    const categorized: CategorizedShoppingList = {
      food: {},
      herbal: {}
    }

    items.forEach(item => {
      if (item.type === 'food') {
        const category = item.category as FoodCategory
        if (!categorized.food[category]) {
          categorized.food[category] = []
        }
        categorized.food[category]!.push(item)
      } else {
        const category = item.category as HerbalCategory
        if (!categorized.herbal[category]) {
          categorized.herbal[category] = []
        }
        categorized.herbal[category]!.push(item)
      }
    })

    return categorized
  }

  const categorizedItems = categorizeItems(editedItems)

  // 切换项目购买状态
  const toggleItemPurchased = (itemName: string) => {
    setEditedItems(prev => 
      prev.map(item => 
        item.name === itemName 
          ? { ...item, is_purchased: !item.is_purchased }
          : item
      )
    )
  }

  // 删除项目
  const removeItem = (itemName: string) => {
    setEditedItems(prev => prev.filter(item => item.name !== itemName))
  }

  // 更新项目数量
  const updateItemQuantity = (itemName: string, newQuantity: string) => {
    setEditedItems(prev => 
      prev.map(item => 
        item.name === itemName 
          ? { ...item, quantity: newQuantity }
          : item
      )
    )
  }

  // 添加新项目
  const addNewItem = () => {
    if (!newItemName.trim() || !newItemQuantity.trim()) return

    const newItem: ShoppingListItem = {
      name: newItemName.trim(),
      quantity: newItemQuantity.trim(),
      unit: '份',
      category: FoodCategory.OTHER,
      type: 'food',
      is_purchased: false
    }

    setEditedItems(prev => [...prev, newItem])
    setNewItemName('')
    setNewItemQuantity('')
    setShowAddDialog(false)
  }

  // 保存编辑
  const saveEdits = async () => {
    try {
      const response = await fetch('/api/generate-shopping-list', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: shoppingList.user_id,
          items: editedItems
        })
      })

      if (response.ok) {
        setIsEditing(false)
        // 这里可以添加成功提示
      } else {
        console.error('保存失败')
        // 这里可以添加错误提示
      }
    } catch (error) {
      console.error('保存购物清单失败:', error)
    }
  }

  // 取消编辑
  const cancelEdits = () => {
    setEditedItems(shoppingList.items)
    setIsEditing(false)
  }

  // 查看项目详情
  const viewItemDetails = (item: ShoppingListItem) => {
    setSelectedItem(item)
    setShowItemDetails(true)
  }

  // 导出PDF
  const exportToPDF = () => {
    exportShoppingListToPDF(shoppingList)
  }

  // 打印清单
  const printList = () => {
    window.print()
  }

  // 渲染类别项目
  const renderCategoryItems = (items: ShoppingListItem[], categoryName: string) => (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold text-gray-800 flex items-center">
        {categoryName.includes('类') ? (
          <Package className="w-5 h-5 mr-2 text-blue-600" />
        ) : (
          <Leaf className="w-5 h-5 mr-2 text-green-600" />
        )}
        {categoryName}
        <Badge variant="secondary" className="ml-2">
          {items.length}
        </Badge>
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {items.map((item, index) => (
          <div 
            key={`${item.name}-${index}`}
            className={`bg-white rounded-lg border p-4 transition-all duration-200 ${
              item.is_purchased 
                ? 'bg-green-50 border-green-200 opacity-75' 
                : 'border-gray-200 hover:border-blue-300'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => toggleItemPurchased(item.name)}
                    className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                      item.is_purchased
                        ? 'bg-green-500 border-green-500 text-white'
                        : 'border-gray-300 hover:border-green-400'
                    }`}
                  >
                    {item.is_purchased && <Check className="w-3 h-3" />}
                  </button>
                  <button
                    onClick={() => viewItemDetails(item)}
                    className={`font-medium hover:text-blue-600 transition-colors ${
                      item.is_purchased ? 'line-through text-gray-500' : 'text-gray-800'
                    }`}
                  >
                    {item.name}
                  </button>
                </div>
                <div className="mt-1">
                  {isEditing ? (
                    <Input
                      value={item.quantity}
                      onChange={(e) => updateItemQuantity(item.name, e.target.value)}
                      className="h-8 text-sm"
                    />
                  ) : (
                    <span className="text-sm text-gray-600">{item.quantity}</span>
                  )}
                </div>
                {item.source_dishes && item.source_dishes.length > 0 && (
                  <div className="mt-2">
                    <span className="text-xs text-blue-600">
                      用于: {item.source_dishes.slice(0, 2).join(', ')}
                      {item.source_dishes.length > 2 && '...'}
                    </span>
                  </div>
                )}
                {item.source_formulas && item.source_formulas.length > 0 && (
                  <div className="mt-2">
                    <span className="text-xs text-green-600">
                      用于: {item.source_formulas.slice(0, 2).join(', ')}
                      {item.source_formulas.length > 2 && '...'}
                    </span>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-1 ml-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => viewItemDetails(item)}
                  className="h-8 w-8 p-0"
                >
                  <Eye className="w-4 h-4" />
                </Button>
                {isEditing && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeItem(item.name)}
                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const totalItems = editedItems.length
  const purchasedItems = editedItems.filter(item => item.is_purchased).length
  const progress = totalItems > 0 ? (purchasedItems / totalItems) * 100 : 0

  return (
    <div className={`max-w-6xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0">
        <CardHeader className="text-center space-y-4 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <ShoppingCart className="w-8 h-8 text-blue-600" />
              <div className="text-left">
                <CardTitle className="text-2xl font-bold text-gray-800">
                  您的购物清单
                </CardTitle>
                <CardDescription className="text-gray-600">
                  共 {totalItems} 项，已购买 {purchasedItems} 项 ({progress.toFixed(0)}%)
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isEditing ? (
                <>
                  <Button 
                    onClick={saveEdits}
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <Save className="w-4 h-4" />
                    <span>保存</span>
                  </Button>
                  <Button 
                    onClick={cancelEdits}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <X className="w-4 h-4" />
                    <span>取消</span>
                  </Button>
                </>
              ) : (
                <>
                  <Button 
                    onClick={() => setIsEditing(true)}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <Edit3 className="w-4 h-4" />
                    <span>编辑清单</span>
                  </Button>
                  <Button 
                    onClick={exportToPDF}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <Download className="w-4 h-4" />
                    <span>导出PDF</span>
                  </Button>
                  <Button 
                    onClick={printList}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-1"
                  >
                    <Printer className="w-4 h-4" />
                    <span>打印</span>
                  </Button>
                  {onRegenerate && (
                    <Button 
                      onClick={onRegenerate}
                      disabled={isRegenerating}
                      variant="outline"
                      size="sm"
                      className="flex items-center space-x-1"
                    >
                      {isRegenerating ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4" />
                      )}
                      <span>重新生成</span>
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* 进度条 */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>购买进度</span>
              <span>{purchasedItems}/{totalItems}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* 添加项目按钮 */}
          {isEditing && (
            <div className="mb-6">
              <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="flex items-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span>添加项目</span>
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>添加购物项目</DialogTitle>
                    <DialogDescription>
                      添加新的食材或药材到购物清单中
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">项目名称</label>
                      <Input
                        value={newItemName}
                        onChange={(e) => setNewItemName(e.target.value)}
                        placeholder="请输入食材或药材名称"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">数量</label>
                      <Input
                        value={newItemQuantity}
                        onChange={(e) => setNewItemQuantity(e.target.value)}
                        placeholder="请输入数量，如：500g、3个"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                      取消
                    </Button>
                    <Button onClick={addNewItem}>
                      添加
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          )}

          {/* 分类展示 */}
          <Tabs defaultValue="food" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="food" className="flex items-center space-x-2">
                <Package className="w-4 h-4" />
                <span>食材清单</span>
              </TabsTrigger>
              <TabsTrigger value="herbal" className="flex items-center space-x-2">
                <Leaf className="w-4 h-4" />
                <span>药材清单</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="food" className="mt-6">
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-8">
                  {Object.entries(categorizedItems.food).map(([category, items]) => (
                    <div key={category}>
                      {renderCategoryItems(items!, FoodCategoryNames[category as FoodCategory])}
                      <Separator className="my-6" />
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="herbal" className="mt-6">
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-8">
                  {Object.entries(categorizedItems.herbal).map(([category, items]) => (
                    <div key={category}>
                      {renderCategoryItems(items!, HerbalCategoryNames[category as HerbalCategory])}
                      <Separator className="my-6" />
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 项目详情对话框 */}
      <Dialog open={showItemDetails} onOpenChange={setShowItemDetails}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedItem?.name}</DialogTitle>
            <DialogDescription>
              {selectedItem?.type === 'food' ? '食材详情' : '药材详情'}
            </DialogDescription>
          </DialogHeader>
          {selectedItem && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">数量</label>
                <p className="text-lg">{selectedItem.quantity}</p>
              </div>
              {selectedItem.source_dishes && selectedItem.source_dishes.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-gray-600">用于菜品</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedItem.source_dishes.map((dish, index) => (
                      <Badge key={index} variant="secondary">{dish}</Badge>
                    ))}
                  </div>
                </div>
              )}
              {selectedItem.source_formulas && selectedItem.source_formulas.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-gray-600">用于方剂</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedItem.source_formulas.map((formula, index) => (
                      <Badge key={index} variant="secondary">{formula}</Badge>
                    ))}
                  </div>
                </div>
              )}
              {selectedItem.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-600">备注</label>
                  <p className="text-sm text-gray-700">{selectedItem.notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

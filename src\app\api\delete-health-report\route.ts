import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { 
  deleteHealthAssessmentReport
} from '@/lib/db/health-assessment'

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: '缺少用户ID参数' },
        { status: 400 }
      )
    }

    const deleted = await deleteHealthAssessmentReport(userId)
    
    return NextResponse.json({
      success: true,
      deleted: deleted
    })

  } catch (error) {
    console.error('删除健康报告时出错:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '删除报告时发生未知错误',
        success: false 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取当前用户信息
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })
    
    // 只允许登录用户删除报告
    if (!token?.userId) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const userId = token.userId as string
    console.log('=== 删除登录用户报告 ===', userId)
    
    const deleted = await deleteHealthAssessmentReport(userId)
    
    return NextResponse.json({
      success: true,
      deleted: deleted
    })

  } catch (error) {
    console.error('删除健康报告时出错:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '删除报告时发生未知错误',
        success: false 
      },
      { status: 500 }
    )
  }
} 
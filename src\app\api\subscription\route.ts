import { NextResponse } from 'next/server';
import { updateSubscriptionByOrderId } from "@/actions/user-order";
export async function POST(request: Request) {

    try {
        const { subscriptionId } = await request.json();
        const result = await updateSubscriptionByOrderId(subscriptionId);
        if(result){
            return NextResponse.json({result: result}, {status: 200});
        }
        return NextResponse.json(
            { error: 'Failed to cancel subscription' },
            { status: 500 }
        );
    } catch (error) {
        console.log("cancel subscription error", error)
        return NextResponse.json(
            { error: 'Failed to cancel subscription' },
            { status: 500 }
        );
    }
}
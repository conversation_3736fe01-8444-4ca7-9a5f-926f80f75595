'use server';
import { sql, db } from '@/lib/postgres-client';
import { getKey, setKeyWithExpiry } from '@/lib/redis-client';
import { cache } from 'react'
import { getCurrentUser } from '@/lib/auth';
import { findUserCreditsByUserId } from '@/actions/user';
import { IUserCredit } from '@/types/user-credit';

const expirySeconds = 60 * 60 * 1; // 1 hour
const prefix = 'credit_';

export async function findUserCreditByUserId(id: string) {
    const { rowCount } = await sql`SELECT * FROM nf_credits WHERE user_id = ${id}`;
    return rowCount;
}

export const addUserCredit = cache(async (data: IUserCredit): Promise<number> => {
    try {
        const result = await sql`INSERT INTO nf_credits 
            (user_id, order_number, order_price, credit_amount, credit_type, credit_transaction_type, credit_desc, order_date, product_name) 
            VALUES (
                ${data.user_id},
                ${data.order_number},
                ${data.order_price},
                ${data.credit_amount},
                ${data.credit_type},
                ${data.credit_transaction_type},
                ${data.credit_desc},
                ${data.order_date},
                ${data.product_name || ''}
            )`;
        const rowCount = result.rowCount;
        return rowCount !== null && rowCount !== undefined ? rowCount : 0;
    } catch (error) {
        console.error('Error adding user credit:', error);
        return 0;
    }
});

export async function updateUserCredits(userId: string, newCredits: number): Promise<boolean> {
    try {
        const { rowCount } = await sql`UPDATE nf_users SET credits = ${newCredits} WHERE user_id = ${userId}`;
        
        // 清除缓存
        const key = prefix + userId;
        await setKeyWithExpiry(key, String(newCredits), expirySeconds);
        
        return rowCount !== null && rowCount !== undefined && rowCount > 0;
        // return rowCount > 0;
    } catch (error) {
        console.error('Error updating user credits:', error);
        return false;
    }
}

export async function deductUserCredits(userId: string, amount: number, description: string): Promise<boolean> {
    try {
        // 获取当前积分
        const currentCredits = await findUserCreditsByUserId(userId);
        
        if (currentCredits < amount) {
            return false; // 积分不足
        }
        
        // 扣减积分
        const newCredits = currentCredits - amount;
        const updateSuccess = await updateUserCredits(userId, newCredits);
        
        if (updateSuccess) {
            // 记录积分扣减
            await addUserCredit({
                user_id: userId,
                order_number: `deduct_${userId}_${Date.now()}`,
                order_price: 0,
                credit_amount: amount,
                credit_type: '4', // 扣减积分
                credit_transaction_type: '0', // 消费积分
                credit_desc: description,
                order_date: new Date().toISOString(),
                product_name: '积分消费'
            });
        }
        
        return updateSuccess;
    } catch (error) {
        console.error('Error deducting user credits:', error);
        return false;
    }
}

export async function countMemberCreditsByUserId(userId?: string): Promise<number> {
    try {
        let targetUserId = userId;
        
        if (!targetUserId) {
            const user = await getCurrentUser();
            if (!user) return 0;
            targetUserId = user.userId;
        }
        
        const { rows } = await sql`
            SELECT SUM(credit_amount) as total_credits 
            FROM nf_credits 
            WHERE user_id = ${targetUserId} 
            AND credit_type = '1' 
            AND credit_transaction_type = '1'
        `;
        
        return parseInt(rows[0]?.total_credits || '0');
    } catch (error) {
        console.error('Error counting member credits:', error);
        return 0;
    }
}

// 兼容性函数（将会被弃用）
export async function findUserCreditByClerkId(id: string) {
    console.warn('findUserCreditByClerkId is deprecated, use findUserCreditByUserId instead');
    return findUserCreditByUserId(id);
}

export async function countMemberCreditsByClerkId(clerkId?: string): Promise<number> {
    console.warn('countMemberCreditsByClerkId is deprecated, use countMemberCreditsByUserId instead');
    return countMemberCreditsByUserId(clerkId);
}

// 兼容性函数（将会被弃用）
export async function findUserClerkIdByOrderNumber(order_number: string) {
    console.warn('findUserClerkIdByOrderNumber is deprecated');
    const { rows } = await sql`SELECT user_id FROM nf_credits WHERE order_number = ${order_number}`;
    return rows[0]?.user_id;
}

export async function addSubscription(data: IUserCredit) {
    try {
        const { rows } = await sql`
        SELECT subscription_id FROM nf_subscription 
        WHERE subscription_id = ${data.subscriptionId}`;

        if (rows.length > 0) {
            return 0;
        }

        const { rowCount } = await sql`INSERT INTO nf_subscription 
        (user_id,order_number,subscription_id,order_price,credit_amount,order_type,order_desc,order_date) 
        VALUES (
            ${data.user_id},
            ${data.order_number},
            ${data.subscriptionId},
            ${data.order_price},
            ${data.credit_amount},
            '1',
            ${data.credit_desc},
            ${data.order_date}
        )`;
        return rowCount || 0;
    } catch (error) {
        console.error('Error adding subscription:', error);
    }
    return 0;
}

export async function deleteUserCreditByUserId(userId: string) {
    const { rowCount } = await sql`DELETE FROM nf_credits WHERE user_id = ${userId}`;
    return rowCount || 0;
}

export async function addUserCreditBalance(credit: number, userId: string) {
    try {
        const success = await updateUserCredits(userId, await findUserCreditsByUserId(userId) + credit);
        if (success) {
            // 记录积分增加
            await addUserCredit({
                user_id: userId,
                order_number: `add_${userId}_${Date.now()}`,
                order_price: 0,
                credit_amount: credit,
                credit_type: '5', // 系统添加积分
                credit_transaction_type: '1', // 获得积分
                credit_desc: '系统添加积分',
                order_date: new Date().toISOString(),
                product_name: '积分充值'
            });
        }
        return success ? 1 : 0;
    } catch (error) {
        console.error('Error adding user credit balance:', error);
        return 0;
    }
}

export async function refundCredit(refund_credit: number, userId: string, taskId: string) {
    try {
        const success = await addUserCreditBalance(refund_credit, userId);
        if (success) {
            await addUserCredit({
                user_id: userId,
                credit_amount: refund_credit,
                credit_type: '3', // 退款积分
                credit_transaction_type: '1', // 获得积分
                credit_desc: `refund ${refund_credit} credit for task ${taskId}`,
                order_price: 0,
                order_number: `refund_${userId}_${Date.now()}`,
                order_date: new Date().toISOString(),
                product_name: '退款'
            });
        }
    } catch (e) {
        console.error("refund credit error:", e)
    }
}

export async function updateSubscription(subscriptionId: string) {
    try {
        const { rowCount } = await sql`UPDATE nf_subscription SET order_type = '0' WHERE subscription_id=${subscriptionId}`;
        return rowCount || 0;
    } catch (e) {
        console.error("subscription cancel error:", e);
        return 0;
    }
}

// 兼容性函数（将会被弃用）
export async function deleteUserCreditByClerkId(clerkId: string) {
    console.warn('deleteUserCreditByClerkId is deprecated, use deleteUserCreditByUserId instead');
    return deleteUserCreditByUserId(clerkId);
}

export async function addUserCreditBalance_Legacy(credit: number, clerkId: string) {
    console.warn('addUserCreditBalance with clerkId is deprecated, use addUserCreditBalance with userId instead');
    return addUserCreditBalance(credit, clerkId);
}

export async function deductUserCreditBalance(credit: number, userId: string) {
    console.warn('deductUserCreditBalance is deprecated, use deductUserCredits instead');
    return deductUserCredits(userId, credit, '系统扣减积分');
}

export async function updateUserCreditByClerkId(credit: number, userId: string, currentCredit?: number) {
    console.warn('updateUserCreditByClerkId is deprecated, use deductUserCredits instead');
    return deductUserCredits(userId, credit, '系统扣减积分');
}
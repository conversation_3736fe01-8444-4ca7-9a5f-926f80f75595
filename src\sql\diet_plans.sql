-- 饮食计划表
DROP TABLE IF EXISTS "public"."nf_diet_plans";
CREATE TABLE "public"."nf_diet_plans" (
  "id" int4 NOT NULL DEFAULT nextval('nf_diet_plans_id_seq'::regclass),
  "user_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "plan_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL CHECK (plan_type IN ('normal', 'herbal')),
  "weekly_plan" jsonb NOT NULL,
  "special_notes" text COLLATE "pg_catalog"."default" NOT NULL,
  "tcm_principles" text COLLATE "pg_catalog"."default",
  "basic_analysis" text COLLATE "pg_catalog"."default" NOT NULL,
  "diet_recommendations" text COLLATE "pg_catalog"."default",
  "herbal_recommendations" text COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS nf_diet_plans_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 添加注释
COMMENT ON COLUMN "public"."nf_diet_plans"."user_id" IS '用户ID（对应健康评估报告的用户）';
COMMENT ON COLUMN "public"."nf_diet_plans"."plan_type" IS '计划类型：normal=普通饮食计划, herbal=中医膳食计划';
COMMENT ON COLUMN "public"."nf_diet_plans"."weekly_plan" IS '一周饮食计划详细数据（JSON格式）';
COMMENT ON COLUMN "public"."nf_diet_plans"."special_notes" IS '特别说明和注意事项';
COMMENT ON COLUMN "public"."nf_diet_plans"."tcm_principles" IS '中医膳食原理（仅膳食计划包含）';
COMMENT ON COLUMN "public"."nf_diet_plans"."basic_analysis" IS '来源健康评估的基本分析';
COMMENT ON COLUMN "public"."nf_diet_plans"."diet_recommendations" IS '来源健康评估的饮食建议（膳食计划参考）';
COMMENT ON COLUMN "public"."nf_diet_plans"."herbal_recommendations" IS '来源健康评估的中药调理建议（药膳计划参考）';
COMMENT ON TABLE "public"."nf_diet_plans" IS '用户饮食计划表';

-- 创建索引
CREATE INDEX "idx_diet_plans_user_id" ON "public"."nf_diet_plans" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_diet_plans_plan_type" ON "public"."nf_diet_plans" USING btree (
  "plan_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_diet_plans_created_at" ON "public"."nf_diet_plans" USING btree (
  "created_at" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- 主键
ALTER TABLE "public"."nf_diet_plans" ADD CONSTRAINT "nf_diet_plans_pkey" PRIMARY KEY ("id");

-- 唯一约束：每个用户只能有一个饮食计划
ALTER TABLE "public"."nf_diet_plans" ADD CONSTRAINT "unique_user_diet_plan" UNIQUE ("user_id"); 
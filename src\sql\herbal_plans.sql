-- 1. 首先删除表（如果存在）
DROP TABLE IF EXISTS "public"."nf_herbal_plans";

-- 2. 创建序列（必须在创建表之前）
CREATE SEQUENCE IF NOT EXISTS public.nf_herbal_plans_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 3. 创建表
CREATE TABLE "public"."nf_herbal_plans" (
  "id" int4 NOT NULL DEFAULT nextval('nf_herbal_plans_id_seq'::regclass),
  "user_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "plan_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'weight_loss' CHECK (plan_type IN ('weight_loss')),
  "weekly_plan" jsonb NOT NULL,
  "tcm_diagnosis" text COLLATE "pg_catalog"."default" NOT NULL,
  "constitution_type" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "treatment_principle" text COLLATE "pg_catalog"."default" NOT NULL,
  "expected_effects" text COLLATE "pg_catalog"."default" NOT NULL,
  "precautions" text COLLATE "pg_catalog"."default" NOT NULL,
  "basic_analysis" text COLLATE "pg_catalog"."default" NOT NULL,
  "herbal_recommendations" text COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

-- 4. 添加注释
COMMENT ON COLUMN "public"."nf_herbal_plans"."user_id" IS '用户ID（对应健康评估报告的用户）';
COMMENT ON COLUMN "public"."nf_herbal_plans"."plan_type" IS '方案类型：weight_loss=中医药减肥方案';
COMMENT ON COLUMN "public"."nf_herbal_plans"."weekly_plan" IS '一周中药方案详细数据（JSON格式）';
COMMENT ON COLUMN "public"."nf_herbal_plans"."tcm_diagnosis" IS '中医诊断';
COMMENT ON COLUMN "public"."nf_herbal_plans"."constitution_type" IS '体质类型';
COMMENT ON COLUMN "public"."nf_herbal_plans"."treatment_principle" IS '治疗原则';
COMMENT ON COLUMN "public"."nf_herbal_plans"."expected_effects" IS '预期效果';
COMMENT ON COLUMN "public"."nf_herbal_plans"."precautions" IS '注意事项和禁忌';
COMMENT ON COLUMN "public"."nf_herbal_plans"."basic_analysis" IS '来源健康评估的基本分析';
COMMENT ON COLUMN "public"."nf_herbal_plans"."herbal_recommendations" IS '来源健康评估的中药调理建议';
COMMENT ON TABLE "public"."nf_herbal_plans" IS '用户中医药减肥方案表';

-- 5. 创建索引
CREATE INDEX "idx_herbal_plans_user_id" ON "public"."nf_herbal_plans" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_herbal_plans_plan_type" ON "public"."nf_herbal_plans" USING btree (
  "plan_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_herbal_plans_created_at" ON "public"."nf_herbal_plans" USING btree (
  "created_at" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- 6. 添加主键
ALTER TABLE "public"."nf_herbal_plans" ADD CONSTRAINT "nf_herbal_plans_pkey" PRIMARY KEY ("id");

-- 7. 添加唯一约束
ALTER TABLE "public"."nf_herbal_plans" ADD CONSTRAINT "unique_user_herbal_plan" UNIQUE ("user_id");
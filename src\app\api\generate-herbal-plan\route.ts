import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { HERBAL_WEIGHT_LOSS_PLAN_PROMPT } from '@/actions/herbal-constants'
import { saveHerbalPlan } from '@/lib/db/herbal-plan'
import { getLatestHealthAssessmentReport } from '@/lib/db/health-assessment'
import { HerbalWeightLossPlan, HerbalPlanGeneration } from '@/types/herbal-plan'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: '缺少用户ID' },
        { status: 400 }
      )
    }

    // 获取用户的健康评估报告
    const healthReport = await getLatestHealthAssessmentReport(userId)
    if (!healthReport) {
      return NextResponse.json(
        { error: '未找到用户健康评估报告，请先完成健康评估' },
        { status: 404 }
      )
    }

    // 检查是否有中药调理建议
    if (!healthReport.herbal_recommendations) {
      return NextResponse.json(
        { error: '健康评估报告中缺少中药调理建议，无法生成中药方案' },
        { status: 400 }
      )
    }

    // 构建AI提示词
    const fullPrompt = HERBAL_WEIGHT_LOSS_PLAN_PROMPT
      .replace('{basicAnalysis}', healthReport.basic_analysis || '')
      .replace('{herbalRecommendations}', healthReport.herbal_recommendations || '')

    // 调用通义千问API
    const apiKey = process.env.DASHSCOPE_API_KEY
    if (!apiKey) {
      throw new Error('未配置DASHSCOPE API密钥')
    }

    const startTime = Date.now()

    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: process.env.VISION_MODEL || 'qwen-plus-latest',
        input: {
          messages: [
            {
              role: 'user',
              content: fullPrompt
            }
          ]
        },
        parameters: {
          max_tokens: 12000,
          temperature: 0.7,
          top_p: 0.8,
        }
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('通义千问API错误:', errorData)
      throw new Error(`通义千问API调用失败: ${response.status}`)
    }

    const data = await response.json()
    
    // 打印AI返回的JSON结果和消耗时间
    const endTime = Date.now()
    const duration = endTime - startTime
    console.log('=== AI返回的中药方案结果 ===')
    console.log(JSON.stringify(data, null, 2))
    console.log('=== 消耗时间 ===')
    console.log(`${duration}ms`)

    if (!data.output?.text) {
      throw new Error('AI返回数据格式错误')
    }

    // 检查是否因为长度限制被截断
    if (data.output?.finish_reason === 'length') {
      console.warn('⚠️ AI返回内容可能因长度限制被截断，正在尝试解析...')
      console.warn('返回文本长度:', data.output.text.length)
    }

    // 解析AI返回的中药方案
    const herbalPlanData = parseAIHerbalPlan(data.output.text)
    
    if (!herbalPlanData) {
      throw new Error('无法解析AI生成的中药方案')
    }

    // 确保周计划数据的完整性
    const fixedWeeklyPlan = ensureWeeklyHerbalPlanIntegrity(herbalPlanData.weeklyPlan)

    // 构建数据库存储对象
    const herbalPlan: HerbalWeightLossPlan = {
      user_id: userId,
      plan_type: herbalPlanData.planType,
      weekly_plan: fixedWeeklyPlan,
      tcm_diagnosis: herbalPlanData.tcmDiagnosis,
      constitution_type: herbalPlanData.constitutionType,
      treatment_principle: herbalPlanData.treatmentPrinciple,
      expected_effects: herbalPlanData.expectedEffects,
      precautions: herbalPlanData.precautions,
      basic_analysis: healthReport.basic_analysis || '',
      herbal_recommendations: healthReport.herbal_recommendations || ''
    }

    // 保存到数据库
    const herbalPlanId = await saveHerbalPlan(herbalPlan)
    herbalPlan.id = herbalPlanId

    console.log('=== 中药方案生成完成 ===')
    console.log('用户ID:', userId)
    console.log('数据库ID:', herbalPlanId)

    return NextResponse.json({
      success: true,
      herbalPlan: herbalPlan
    })

  } catch (error) {
    console.error('中药方案生成错误:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '中药方案生成失败'
      },
      { status: 500 }
    )
  }
}

/**
 * 解析AI返回的中药方案文本
 */
function parseAIHerbalPlan(responseText: string): HerbalPlanGeneration | null {
  try {
    console.log('开始解析AI返回的中药方案文本...')
    console.log('原始文本长度:', responseText.length)
    
    // 方法1: 尝试提取```json代码块
    const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/)
    if (jsonMatch && jsonMatch[1]) {
      console.log('找到JSON代码块，开始解析...')
      const jsonStr = jsonMatch[1].trim()
      console.log('提取的JSON字符串长度:', jsonStr.length)
      
      const parsed = JSON.parse(jsonStr)
      
      // 验证必要字段
      if (!parsed.planType || !parsed.weeklyPlan || !parsed.tcmDiagnosis) {
        console.error('中药方案数据缺少必要字段:', parsed)
        return null
      }
      
      console.log('JSON解析成功，方案类型:', parsed.planType)
      return parsed as HerbalPlanGeneration
    }

    // 方法2: 尝试查找第一个{到最后一个}之间的内容
    const firstBrace = responseText.indexOf('{')
    const lastBrace = responseText.lastIndexOf('}')
    
    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      console.log('尝试提取大括号间的JSON内容...')
      const jsonStr = responseText.substring(firstBrace, lastBrace + 1)
      console.log('提取的JSON字符串:', jsonStr.substring(0, 200) + '...')
      
      const parsed = JSON.parse(jsonStr)
      
      // 验证必要字段
      if (!parsed.planType || !parsed.weeklyPlan || !parsed.tcmDiagnosis) {
        console.error('中药方案数据缺少必要字段:', parsed)
        return null
      }
      
      console.log('JSON解析成功，方案类型:', parsed.planType)
      return parsed as HerbalPlanGeneration
    }

    console.error('无法从AI返回文本中提取有效的JSON数据')
    return null

  } catch (error) {
    console.error('解析AI返回的中药方案失败:', error)
    console.error('原始文本:', responseText.substring(0, 500) + '...')
    return null
  }
}

/**
 * 确保周中药方案数据的完整性
 */
function ensureWeeklyHerbalPlanIntegrity(weeklyPlan: any): any {
  const dayMapping: { [key: string]: string } = {
    'day1': '周一',
    'day2': '周二', 
    'day3': '周三',
    'day4': '周四',
    'day5': '周五',
    'day6': '周六',
    'day7': '周日'
  }

  const fixedPlan = { ...weeklyPlan }

  // 遍历每一天的计划
  Object.keys(fixedPlan).forEach(dayKey => {
    const dayPlan = fixedPlan[dayKey]
    if (dayPlan && typeof dayPlan === 'object') {
      // 确保有 date 字段
      if (!dayPlan.date) {
        dayPlan.date = dayMapping[dayKey] || dayKey
      }
    }
  })

  return fixedPlan
}

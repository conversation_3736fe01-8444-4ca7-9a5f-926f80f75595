// 食材类别枚举
export enum FoodCategory {
  VEGETABLES = 'vegetables', // 蔬菜
  FRUITS = 'fruits', // 水果
  MEAT = 'meat', // 肉类
  SEAFOOD = 'seafood', // 海鲜
  GRAINS = 'grains', // 谷物
  DAIRY = 'dairy', // 乳制品
  SEASONINGS = 'seasonings', // 调味品
  OILS = 'oils', // 油脂
  NUTS = 'nuts', // 坚果
  BEVERAGES = 'beverages', // 饮品
  OTHER = 'other' // 其他
}

// 药材类别枚举
export enum HerbalCategory {
  TONIFYING = 'tonifying', // 补益类
  CLEARING_HEAT = 'clearing_heat', // 清热类
  REGULATING_QI = 'regulating_qi', // 理气类
  PROMOTING_BLOOD = 'promoting_blood', // 活血类
  TRANSFORMING_PHLEGM = 'transforming_phlegm', // 化痰类
  DIURETIC = 'diuretic', // 利水类
  DIGESTIVE = 'digestive', // 消食类
  CALMING = 'calming', // 安神类
  EXTERIOR_RELEASING = 'exterior_releasing', // 解表类
  OTHER = 'other' // 其他
}

// 购物清单项目
export interface ShoppingListItem {
  id?: number
  name: string // 食材/药材名称
  quantity: string // 数量描述，如"5个（约500克）"、"300克"
  unit: string // 单位，如"个"、"克"、"斤"
  category: FoodCategory | HerbalCategory // 类别
  type: 'food' | 'herbal' // 类型：食材或药材
  source_dishes?: string[] // 来源菜品（食材）
  source_formulas?: string[] // 来源方剂（药材）
  is_purchased: boolean // 是否已购买
  notes?: string // 备注
  price?: number // 价格（可选）
}

// 分类后的购物清单
export interface CategorizedShoppingList {
  food: {
    [key in FoodCategory]?: ShoppingListItem[]
  }
  herbal: {
    [key in HerbalCategory]?: ShoppingListItem[]
  }
}

// 购物清单
export interface ShoppingList {
  id?: number
  user_id: string
  diet_plan_id?: number // 关联的饮食计划ID
  herbal_plan_id?: number // 关联的中药方案ID
  items: ShoppingListItem[]
  total_items: number // 总项目数
  purchased_items: number // 已购买项目数
  estimated_cost?: number // 预估总价
  created_at?: Date
  updated_at?: Date
}

// 购物清单生成请求
export interface ShoppingListGenerateRequest {
  userId: string
  includeDietPlan?: boolean // 是否包含饮食计划食材
  includeHerbalPlan?: boolean // 是否包含中药方案药材
}

// 购物清单更新请求
export interface ShoppingListUpdateRequest {
  userId: string
  items: ShoppingListItem[]
}

// 购物清单响应
export interface ShoppingListResponse {
  success: boolean
  shoppingList?: ShoppingList
  error?: string
}

// 食材类别显示名称映射
export const FoodCategoryNames: Record<FoodCategory, string> = {
  [FoodCategory.VEGETABLES]: '蔬菜类',
  [FoodCategory.FRUITS]: '水果类',
  [FoodCategory.MEAT]: '肉类',
  [FoodCategory.SEAFOOD]: '海鲜类',
  [FoodCategory.GRAINS]: '谷物类',
  [FoodCategory.DAIRY]: '乳制品',
  [FoodCategory.SEASONINGS]: '调味品',
  [FoodCategory.OILS]: '油脂类',
  [FoodCategory.NUTS]: '坚果类',
  [FoodCategory.BEVERAGES]: '饮品类',
  [FoodCategory.OTHER]: '其他'
}

// 药材类别显示名称映射
export const HerbalCategoryNames: Record<HerbalCategory, string> = {
  [HerbalCategory.TONIFYING]: '补益类',
  [HerbalCategory.CLEARING_HEAT]: '清热类',
  [HerbalCategory.REGULATING_QI]: '理气类',
  [HerbalCategory.PROMOTING_BLOOD]: '活血类',
  [HerbalCategory.TRANSFORMING_PHLEGM]: '化痰类',
  [HerbalCategory.DIURETIC]: '利水类',
  [HerbalCategory.DIGESTIVE]: '消食类',
  [HerbalCategory.CALMING]: '安神类',
  [HerbalCategory.EXTERIOR_RELEASING]: '解表类',
  [HerbalCategory.OTHER]: '其他'
}

// PDF导出选项
export interface PDFExportOptions {
  includeFood: boolean // 是否包含食材
  includeHerbal: boolean // 是否包含药材
  includePrices: boolean // 是否包含价格
  includeNotes: boolean // 是否包含备注
  groupByCategory: boolean // 是否按类别分组
}

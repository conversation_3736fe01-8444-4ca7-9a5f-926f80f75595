"use client"

import { useSession, signIn } from "next-auth/react"
import { useState } from 'react'
import CheckoutForm from './checkout-stripe'
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import styles from '@/styles/pricing/dialog.module.css'
import { Product } from '@/types/locales/pricing';

interface Props {
    btnlabel: string;
    lang: string;
    mode: "payment" | "recurring";
    paymentTips: string;
    product: Product;
    currency: string;
}

export function PaymentButton({ btnlabel, lang, mode, product, currency, paymentTips }: Props) {
    const { data: session } = useSession()
    const [isOpen, setIsOpen] = useState(false)

    async function onClickHandler() {
        if (!session?.user) {
            // 如果未登录，使用NextAuth的signIn方法
            signIn();
            return
        }
        setIsOpen(true)
    }

    return (
        <>
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className={`sm:max-w-[1024px] border-primary-gold/30 ${styles.lightDialog}`}>
                    <DialogHeader className="flex flex-row items-start justify-between">
                        <DialogTitle className="flex-1">   
                            <span className="text-sm font-semibold text-primary-gold break-words">
                                {paymentTips}
                            </span>
                        </DialogTitle>
                    </DialogHeader>
                    {isOpen && <CheckoutForm priceId={product.priceId!}
                    className={styles.lightDialog} mode={mode}/>}
                </DialogContent>
            </Dialog>

            <Button
                onClick={onClickHandler}
                className="w-full py-6 text-base bg-primary hover:bg-primary/90 text-white font-semibold dark:bg-primary/80 dark:hover:bg-primary"
                size="lg"
            >
                {btnlabel}
            </Button>
        </>
    )
}
import { sql } from '@/lib/postgres-client'
import { DietPlan } from '@/types/diet-plan'

/**
 * 保存饮食计划到数据库
 */
export async function saveDietPlan(data: DietPlan): Promise<number> {
  // 首先检查用户是否已有饮食计划
  const existingPlan = await getLatestDietPlan(data.user_id)
  
  if (existingPlan) {
    // 用户已有计划，更新现有记录
    const { rows } = await sql`
      UPDATE nf_diet_plans 
      SET 
        plan_type = ${data.plan_type},
        weekly_plan = ${JSON.stringify(data.weekly_plan)},
        special_notes = ${data.special_notes},
        tcm_principles = ${data.tcm_principles || null},
        basic_analysis = ${data.basic_analysis},
        diet_recommendations = ${data.diet_recommendations || null},
        herbal_recommendations = ${data.herbal_recommendations || null},
        updated_at = NOW()
      WHERE user_id = ${data.user_id}
      RETURNING id
    `
    
    console.log('=== 更新现有饮食计划 ===', data.user_id)
    return rows[0].id
  } else {
    // 用户没有计划，插入新记录
    const { rows } = await sql`
      INSERT INTO nf_diet_plans (
        user_id,
        plan_type,
        weekly_plan,
        special_notes,
        tcm_principles,
        basic_analysis,
        diet_recommendations,
        herbal_recommendations,
        created_at,
        updated_at
      ) VALUES (
        ${data.user_id},
        ${data.plan_type},
        ${JSON.stringify(data.weekly_plan)},
        ${data.special_notes},
        ${data.tcm_principles || null},
        ${data.basic_analysis},
        ${data.diet_recommendations || null},
        ${data.herbal_recommendations || null},
        NOW(),
        NOW()
      )
      RETURNING id
    `
    
    console.log('=== 创建新饮食计划 ===', data.user_id)
    return rows[0].id
  }
}

/**
 * 根据用户ID获取最新的饮食计划
 */
export async function getLatestDietPlan(userId: string): Promise<DietPlan | null> {
  const { rows } = await sql`
    SELECT * FROM nf_diet_plans 
    WHERE user_id = ${userId}
    ORDER BY created_at DESC 
    LIMIT 1
  `
  
  if (rows.length === 0) {
    return null
  }
  
  const row = rows[0]
  return {
    id: row.id,
    user_id: row.user_id,
    plan_type: row.plan_type,
    weekly_plan: row.weekly_plan,
    special_notes: row.special_notes,
    tcm_principles: row.tcm_principles,
    basic_analysis: row.basic_analysis,
    diet_recommendations: row.diet_recommendations,
    herbal_recommendations: row.herbal_recommendations,
    created_at: row.created_at,
    updated_at: row.updated_at
  }
}

/**
 * 检查用户是否已有饮食计划
 */
export async function hasDietPlan(userId: string): Promise<boolean> {
  const { rows } = await sql`
    SELECT COUNT(*) as count FROM nf_diet_plans 
    WHERE user_id = ${userId}
  `
  
  return parseInt(rows[0].count) > 0
}

/**
 * 删除用户的饮食计划
 */
export async function deleteDietPlan(userId: string): Promise<boolean> {
  const { rowCount } = await sql`
    DELETE FROM nf_diet_plans 
    WHERE user_id = ${userId}
  `
  
  return (rowCount || 0) > 0
}

/**
 * 更新饮食计划
 */
export async function updateDietPlan(userId: string, data: Partial<DietPlan>): Promise<boolean> {
  const updates: string[] = []
  const values: any[] = []
  let paramIndex = 1

  if (data.plan_type) {
    updates.push(`plan_type = $${paramIndex}`)
    values.push(data.plan_type)
    paramIndex++
  }

  if (data.weekly_plan) {
    updates.push(`weekly_plan = $${paramIndex}`)
    values.push(JSON.stringify(data.weekly_plan))
    paramIndex++
  }

  if (data.special_notes) {
    updates.push(`special_notes = $${paramIndex}`)
    values.push(data.special_notes)
    paramIndex++
  }

  if (data.tcm_principles !== undefined) {
    updates.push(`tcm_principles = $${paramIndex}`)
    values.push(data.tcm_principles)
    paramIndex++
  }

  if (updates.length === 0) {
    return false
  }

  updates.push(`updated_at = NOW()`)
  values.push(userId)

  const query = `
    UPDATE nf_diet_plans 
    SET ${updates.join(', ')}
    WHERE user_id = $${paramIndex}
  `

  const { db } = await import('@/lib/postgres-client')
  const { rowCount } = await db.query(query, values)
  return (rowCount || 0) > 0
} 
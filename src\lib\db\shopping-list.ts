import { sql } from '@/lib/postgres-client'
import { ShoppingList, ShoppingListItem } from '@/types/shopping-list'

/**
 * 保存购物清单到数据库
 */
export async function saveShoppingList(data: ShoppingList): Promise<number> {
  // 首先检查用户是否已有购物清单
  const existingList = await getLatestShoppingList(data.user_id)
  
  if (existingList) {
    // 用户已有清单，更新现有记录
    const { rows } = await sql`
      UPDATE nf_shopping_lists 
      SET 
        diet_plan_id = ${data.diet_plan_id || null},
        herbal_plan_id = ${data.herbal_plan_id || null},
        items = ${JSON.stringify(data.items)},
        total_items = ${data.total_items},
        purchased_items = ${data.purchased_items},
        estimated_cost = ${data.estimated_cost || null},
        updated_at = NOW()
      WHERE user_id = ${data.user_id}
      RETURNING id
    `
    
    console.log('=== 更新现有购物清单 ===', data.user_id)
    return rows[0].id
  } else {
    // 用户没有清单，插入新记录
    const { rows } = await sql`
      INSERT INTO nf_shopping_lists (
        user_id,
        diet_plan_id,
        herbal_plan_id,
        items,
        total_items,
        purchased_items,
        estimated_cost,
        created_at,
        updated_at
      ) VALUES (
        ${data.user_id},
        ${data.diet_plan_id || null},
        ${data.herbal_plan_id || null},
        ${JSON.stringify(data.items)},
        ${data.total_items},
        ${data.purchased_items},
        ${data.estimated_cost || null},
        NOW(),
        NOW()
      ) RETURNING id
    `
    
    console.log('=== 创建新购物清单 ===', data.user_id)
    return rows[0].id
  }
}

/**
 * 获取用户最新的购物清单
 */
export async function getLatestShoppingList(userId: string): Promise<ShoppingList | null> {
  const { rows } = await sql`
    SELECT * FROM nf_shopping_lists 
    WHERE user_id = ${userId}
    ORDER BY created_at DESC 
    LIMIT 1
  `
  
  if (rows.length === 0) {
    return null
  }
  
  const row = rows[0]
  
  return {
    id: row.id,
    user_id: row.user_id,
    diet_plan_id: row.diet_plan_id,
    herbal_plan_id: row.herbal_plan_id,
    items: row.items || [],
    total_items: row.total_items,
    purchased_items: row.purchased_items,
    estimated_cost: row.estimated_cost ? parseFloat(row.estimated_cost) : undefined,
    created_at: row.created_at,
    updated_at: row.updated_at
  }
}

/**
 * 更新购物清单项目
 */
export async function updateShoppingListItems(userId: string, items: ShoppingListItem[]): Promise<boolean> {
  const totalItems = items.length
  const purchasedItems = items.filter(item => item.is_purchased).length
  const estimatedCost = items.reduce((sum, item) => sum + (item.price || 0), 0)

  const { rowCount } = await sql`
    UPDATE nf_shopping_lists 
    SET 
      items = ${JSON.stringify(items)},
      total_items = ${totalItems},
      purchased_items = ${purchasedItems},
      estimated_cost = ${estimatedCost > 0 ? estimatedCost : null},
      updated_at = NOW()
    WHERE user_id = ${userId}
  `
  
  return (rowCount || 0) > 0
}

/**
 * 标记购物清单项目为已购买
 */
export async function markItemAsPurchased(userId: string, itemName: string): Promise<boolean> {
  const shoppingList = await getLatestShoppingList(userId)
  if (!shoppingList) {
    return false
  }

  const updatedItems = shoppingList.items.map(item => 
    item.name === itemName ? { ...item, is_purchased: true } : item
  )

  return await updateShoppingListItems(userId, updatedItems)
}

/**
 * 添加购物清单项目
 */
export async function addShoppingListItem(userId: string, newItem: ShoppingListItem): Promise<boolean> {
  const shoppingList = await getLatestShoppingList(userId)
  if (!shoppingList) {
    // 如果没有购物清单，创建一个新的
    const newList: ShoppingList = {
      user_id: userId,
      items: [newItem],
      total_items: 1,
      purchased_items: newItem.is_purchased ? 1 : 0,
      estimated_cost: newItem.price || 0
    }
    await saveShoppingList(newList)
    return true
  }

  const updatedItems = [...shoppingList.items, newItem]
  return await updateShoppingListItems(userId, updatedItems)
}

/**
 * 删除购物清单项目
 */
export async function removeShoppingListItem(userId: string, itemName: string): Promise<boolean> {
  const shoppingList = await getLatestShoppingList(userId)
  if (!shoppingList) {
    return false
  }

  const updatedItems = shoppingList.items.filter(item => item.name !== itemName)
  return await updateShoppingListItems(userId, updatedItems)
}

/**
 * 检查用户是否已有购物清单
 */
export async function hasShoppingList(userId: string): Promise<boolean> {
  const { rows } = await sql`
    SELECT COUNT(*) as count FROM nf_shopping_lists 
    WHERE user_id = ${userId}
  `
  
  return parseInt(rows[0].count) > 0
}

/**
 * 删除用户的购物清单
 */
export async function deleteShoppingList(userId: string): Promise<boolean> {
  const { rowCount } = await sql`
    DELETE FROM nf_shopping_lists 
    WHERE user_id = ${userId}
  `
  
  return (rowCount || 0) > 0
}

/**
 * 清空购物清单（保留清单但清空所有项目）
 */
export async function clearShoppingList(userId: string): Promise<boolean> {
  const { rowCount } = await sql`
    UPDATE nf_shopping_lists 
    SET 
      items = '[]'::jsonb,
      total_items = 0,
      purchased_items = 0,
      estimated_cost = NULL,
      updated_at = NOW()
    WHERE user_id = ${userId}
  `
  
  return (rowCount || 0) > 0
}

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface BloodPressureData {
  systolic: number;
  diastolic: number;
}

interface BloodPressurePickerProps {
  value: BloodPressureData;
  onChange: (value: BloodPressureData) => void;
  className?: string;
  systolicLabel?: string;
  diastolicLabel?: string;
}

export function BloodPressurePicker({
  value,
  onChange,
  className,
  systolicLabel = '收缩压',
  diastolicLabel = '舒张压'
}: BloodPressurePickerProps) {
  const systolicRef = useRef<HTMLDivElement>(null);
  const diastolicRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<'systolic' | 'diastolic' | null>(null);

  // 血压范围配置
  const systolicConfig = { min: 70, max: 200, label: systolicLabel };
  const diastolicConfig = { min: 40, max: 120, label: diastolicLabel };

  // 计算血压值对应的位置百分比
  const valueToPosition = (val: number, min: number, max: number): number => {
    return 100 - ((val - min) / (max - min)) * 100;
  };

  // 计算位置对应的血压值
  const positionToValue = (position: number, min: number, max: number): number => {
    const percentage = 100 - position;
    return Math.round(min + (percentage / 100) * (max - min));
  };

  // 处理拖动事件
  const handleDrag = (clientY: number, type: 'systolic' | 'diastolic') => {
    const containerRef = type === 'systolic' ? systolicRef : diastolicRef;
    const config = type === 'systolic' ? systolicConfig : diastolicConfig;
    
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const containerHeight = rect.height;
      const relativeY = clientY - rect.top;
      
      // 计算百分比位置
      let percentage = Math.max(0, Math.min(100, (relativeY / containerHeight) * 100));
      
      // 转换为血压值
      const newValue = positionToValue(percentage, config.min, config.max);
      
      const newBloodPressure = {
        ...value,
        [type]: newValue
      };
      
      onChange(newBloodPressure);
    }
  };

  // 鼠标事件处理
  const handleMouseDown = (e: React.MouseEvent, type: 'systolic' | 'diastolic') => {
    setIsDragging(type);
    handleDrag(e.clientY, type);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      handleDrag(e.clientY, isDragging);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(null);
  };

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent, type: 'systolic' | 'diastolic') => {
    setIsDragging(type);
    if (e.touches[0]) {
      handleDrag(e.touches[0].clientY, type);
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (isDragging && e.touches[0]) {
      e.preventDefault();
      handleDrag(e.touches[0].clientY, isDragging);
    }
  };

  // 添加事件监听
  useEffect(() => {
    if (isDragging) {
      // 阻止页面滚动
      document.body.style.overflow = 'hidden';
      
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
      window.addEventListener('touchmove', handleTouchMove, { passive: false });
      window.addEventListener('touchend', handleMouseUp);
    }

    return () => {
      // 恢复页面滚动
      document.body.style.overflow = '';
      
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('touchend', handleMouseUp);
    };
  }, [isDragging]);

  // 渲染单个温度计
  const renderThermometer = (
    type: 'systolic' | 'diastolic',
    config: { min: number; max: number; label: string },
    currentValue: number,
    color: string
  ) => {
    const position = valueToPosition(currentValue, config.min, config.max);
    const containerRef = type === 'systolic' ? systolicRef : diastolicRef;

    return (
      <div className="flex flex-col items-center space-y-4">
        {/* 数值显示 */}
        <div className="text-center">
          <div className="text-2xl sm:text-3xl font-bold" style={{ color }}>
            {currentValue}
          </div>
          <div className="text-xs sm:text-sm text-gray-500">mmHg</div>
        </div>

        {/* 温度计容器 */}
        <div 
          ref={containerRef}
          className="relative h-[240px] sm:h-[260px] md:h-[280px] w-12 bg-gray-200 rounded-full cursor-pointer shadow-inner"
          style={{ touchAction: 'none' }}
          onMouseDown={(e) => handleMouseDown(e, type)}
          onTouchStart={(e) => handleTouchStart(e, type)}
        >
          {/* 刻度线 */}
          <div className="absolute -left-8 top-0 h-full">
            {Array.from({ length: 6 }, (_, i) => {
              const scaleValue = config.max - i * ((config.max - config.min) / 5);
              const scalePosition = valueToPosition(scaleValue, config.min, config.max);
              return (
                <div 
                  key={i} 
                  className="absolute flex items-center"
                  style={{ top: `${scalePosition}%`, transform: 'translateY(-50%)' }}
                >
                  <span className="text-xs text-gray-500 w-6 text-right">{Math.round(scaleValue)}</span>
                  <div className="w-2 h-[1px] bg-gray-400 ml-1" />
                </div>
              );
            })}
          </div>

          {/* 温度计液体填充 */}
          <div 
            className="absolute bottom-0 left-0 right-0 rounded-full transition-all duration-200"
            style={{ 
              height: `${100 - position}%`,
              background: `linear-gradient(to top, ${color}, ${color}88)`
            }}
          />

          {/* 拖动手柄 - 仅在非最低值时显示 */}
          {currentValue > config.min && (
            <div 
              className="absolute left-1/2 transform -translate-x-1/2 transition-all duration-200"
              style={{ top: `${position}%` }}
            >
              <div className={cn(
                "w-8 h-8 rounded-full border-4 shadow-lg transition-all duration-200",
                isDragging === type 
                  ? "scale-125 border-white" 
                  : "border-white hover:scale-110"
              )} style={{ backgroundColor: color }}>
              </div>
            </div>
          )}

          {/* 温度计底部球形 */}
          <div 
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full"
            style={{ backgroundColor: color }}
          />
        </div>

        {/* 标签 */}
        <div className="text-xs sm:text-sm font-medium text-gray-700">
          {config.label}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("flex justify-center items-start space-x-8 sm:space-x-12", className)}>
      {/* 收缩压温度计 */}
      {renderThermometer('systolic', systolicConfig, value.systolic, '#ef4444')}
      
      {/* 舒张压温度计 */}
      {renderThermometer('diastolic', diastolicConfig, value.diastolic, '#3b82f6')}
    </div>
  );
} 
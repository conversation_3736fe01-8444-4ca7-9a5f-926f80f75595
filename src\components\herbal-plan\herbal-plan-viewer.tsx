'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  FlaskConical, 
  Leaf, 
  Clock, 
  AlertTriangle, 
  RefreshCw, 
  Loader2,
  Calendar,
  Sun,
  Moon,
  Heart,
  Target,
  Shield
} from 'lucide-react'
import { HerbalWeightLossPlan, HerbalFormula, HerbalIngredient } from '@/types/herbal-plan'

interface HerbalPlanViewerProps {
  herbalPlan: HerbalWeightLossPlan
  onRegenerate?: () => void
  isRegenerating?: boolean
  className?: string
}

export function HerbalPlanViewer({ 
  herbalPlan, 
  onRegenerate, 
  isRegenerating = false, 
  className = '' 
}: HerbalPlanViewerProps) {
  const [selectedDay, setSelectedDay] = useState('day1')

  const dayNames = {
    day1: '周一',
    day2: '周二',
    day3: '周三',
    day4: '周四',
    day5: '周五',
    day6: '周六',
    day7: '周日'
  }

  // 渲染药材信息
  const renderIngredient = (ingredient: HerbalIngredient) => (
    <div key={ingredient.name} className="bg-green-50 rounded-lg p-3 border border-green-100">
      <div className="flex justify-between items-start mb-2">
        <span className="font-medium text-gray-800">{ingredient.name}</span>
        <Badge variant="outline" className="text-green-700 border-green-300">
          {ingredient.dosage}
        </Badge>
      </div>
      <div className="text-sm text-gray-600 space-y-1">
        <div><span className="font-medium">药性：</span>{ingredient.properties}</div>
        <div><span className="font-medium">归经：</span>{ingredient.meridians}</div>
        <div><span className="font-medium">功效：</span>{ingredient.effects}</div>
      </div>
    </div>
  )

  // 渲染方剂信息
  const renderFormula = (formula: HerbalFormula, timeIcon: React.ReactNode, timeLabel: string) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          {timeIcon}
          <h4 className="text-lg font-semibold text-gray-800">{timeLabel}</h4>
        </div>
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          {formula.name}
        </Badge>
      </div>

      {/* 药材组成 */}
      <div className="mb-4">
        <h5 className="font-medium text-gray-700 mb-3 flex items-center">
          <Leaf className="w-4 h-4 mr-1 text-green-600" />
          药材组成
        </h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {formula.ingredients.map(renderIngredient)}
        </div>
      </div>

      {/* 用法用量 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="font-medium text-blue-800 mb-1 flex items-center">
            <FlaskConical className="w-4 h-4 mr-1" />
            制备方法
          </div>
          <div className="text-sm text-blue-700">{formula.preparation}</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-3">
          <div className="font-medium text-purple-800 mb-1 flex items-center">
            <Clock className="w-4 h-4 mr-1" />
            用法用量
          </div>
          <div className="text-sm text-purple-700">{formula.dosage}</div>
        </div>
      </div>

      {/* 功效和适应症 */}
      <div className="space-y-3">
        <div className="bg-amber-50 rounded-lg p-3">
          <div className="font-medium text-amber-800 mb-1 flex items-center">
            <Target className="w-4 h-4 mr-1" />
            主要功效
          </div>
          <div className="text-sm text-amber-700">{formula.effects}</div>
        </div>
        <div className="bg-green-50 rounded-lg p-3">
          <div className="font-medium text-green-800 mb-1 flex items-center">
            <Heart className="w-4 h-4 mr-1" />
            适应症
          </div>
          <div className="text-sm text-green-700">{formula.indications}</div>
        </div>
        {formula.notes && (
          <div className="bg-red-50 rounded-lg p-3">
            <div className="font-medium text-red-800 mb-1 flex items-center">
              <AlertTriangle className="w-4 h-4 mr-1" />
              特别说明
            </div>
            <div className="text-sm text-red-700">{formula.notes}</div>
          </div>
        )}
      </div>
    </div>
  )

  // 渲染每日方案
  const renderDayPlan = (dayPlan: any, dayKey: string) => {
    // 获取日期显示文本
    const getDateDisplay = (dayPlan: any, dayKey: string) => {
      if (dayPlan?.date) {
        return dayPlan.date
      }
      return dayNames[dayKey as keyof typeof dayNames] || dayKey
    }

    return (
      <div className="space-y-6">
        {/* 每日总览 */}
        <div className="bg-gradient-to-r from-green-50 to-amber-50 rounded-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center">
            <Calendar className="w-6 h-6 mr-2 text-green-600" />
            {getDateDisplay(dayPlan, dayKey)} 中药方案
          </h2>
        </div>

        {/* 早晚方剂 */}
        <div className="space-y-6">
          {/* 早上方剂 */}
          {dayPlan.morning && (
            <div>
              {renderFormula(
                dayPlan.morning.formula, 
                <Sun className="w-5 h-5 text-orange-500" />, 
                `早上方剂 - ${dayPlan.morning.timing}`
              )}
              {dayPlan.morning.notes && (
                <div className="mt-3 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="text-sm text-yellow-800">
                    <span className="font-medium">注意事项：</span>
                    {dayPlan.morning.notes}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 晚上方剂 */}
          {dayPlan.evening && (
            <div>
              {renderFormula(
                dayPlan.evening.formula, 
                <Moon className="w-5 h-5 text-blue-500" />, 
                `晚上方剂 - ${dayPlan.evening.timing}`
              )}
              {dayPlan.evening.notes && (
                <div className="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="text-sm text-blue-800">
                    <span className="font-medium">注意事项：</span>
                    {dayPlan.evening.notes}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 生活方式建议 */}
        {dayPlan.lifestyle && (
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Shield className="w-5 h-5 mr-2 text-gray-600" />
              配合生活方式
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-white rounded-lg p-3">
                <div className="font-medium text-gray-800 mb-1">运动建议</div>
                <div className="text-gray-600">{dayPlan.lifestyle.exercise}</div>
              </div>
              <div className="bg-white rounded-lg p-3">
                <div className="font-medium text-gray-800 mb-1">饮食注意</div>
                <div className="text-gray-600">{dayPlan.lifestyle.diet}</div>
              </div>
              <div className="bg-white rounded-lg p-3">
                <div className="font-medium text-gray-800 mb-1">作息建议</div>
                <div className="text-gray-600">{dayPlan.lifestyle.rest}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`max-w-6xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0">
        <CardHeader className="text-center space-y-4 bg-gradient-to-r from-green-50 to-amber-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FlaskConical className="w-8 h-8 text-green-600" />
              <div className="text-left">
                <CardTitle className="text-2xl font-bold text-gray-800">
                  您的专属中药方案
                </CardTitle>
                <CardDescription className="text-gray-600">
                  中医药减肥调理方案
                </CardDescription>
              </div>
            </div>
            {onRegenerate && (
              <Button 
                onClick={onRegenerate}
                disabled={isRegenerating}
                variant="outline"
                className="flex items-center space-x-2"
              >
                {isRegenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>重新生成中...</span>
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4" />
                    <span>重新生成</span>
                  </>
                )}
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* 方案概述 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold text-blue-800 mb-2">中医诊断</h3>
                <p className="text-blue-700 text-sm">{herbalPlan.tcm_diagnosis}</p>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="font-semibold text-green-800 mb-2">体质类型</h3>
                <p className="text-green-700 text-sm">{herbalPlan.constitution_type}</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="bg-purple-50 rounded-lg p-4">
                <h3 className="font-semibold text-purple-800 mb-2">治疗原则</h3>
                <p className="text-purple-700 text-sm">{herbalPlan.treatment_principle}</p>
              </div>
              <div className="bg-amber-50 rounded-lg p-4">
                <h3 className="font-semibold text-amber-800 mb-2">预期效果</h3>
                <p className="text-amber-700 text-sm">{herbalPlan.expected_effects}</p>
              </div>
            </div>
          </div>

          {/* 日程选择器和详情 */}
          <Tabs value={selectedDay} onValueChange={setSelectedDay}>
            <TabsList className="grid w-full grid-cols-7 mb-6">
              {Object.entries(dayNames).map(([dayKey, dayName]) => (
                <TabsTrigger key={dayKey} value={dayKey} className="text-sm">
                  {dayName}
                </TabsTrigger>
              ))}
            </TabsList>

            {Object.entries(herbalPlan.weekly_plan).map(([dayKey, dayPlan]) => (
              <TabsContent key={dayKey} value={dayKey}>
                <ScrollArea className="h-[800px] pr-4">
                  {renderDayPlan(dayPlan, dayKey)}
                </ScrollArea>
              </TabsContent>
            ))}
          </Tabs>

          {/* 注意事项 */}
          <div className="mt-8 bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-800 mb-3 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              重要注意事项
            </h3>
            <div className="text-sm text-red-700 whitespace-pre-line">
              {herbalPlan.precautions}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

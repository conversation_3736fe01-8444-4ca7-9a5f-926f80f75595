import { redirect } from 'next/navigation';
import { getDictionary } from '@/i18n';
import { i18nConfig, type Locale } from '@/i18n-config';
import { Suspense } from 'react';
import InfoCollectionForm from '@/components/collection/info-collection-form';

export async function generateMetadata({ params }: { params: { lang: Locale } }) {
  const lang = params.lang;

  return {
    title: '信息采集 - 专属健康饮食计划',
    description: '完善您的个人信息，获取专属健康饮食计划',
    alternates: {
      canonical: `/diet-plan/info-collection`,
      languages: Object.fromEntries(
        i18nConfig.locales.map((l) => [
          l,
          `/${l}/diet-plan/info-collection`,
        ])
      ),
    },
  };
}

// 客户端组件包装器，用于获取和显示用户信息
function InfoCollectionClient({ lang }: { lang: string }) {
  return (
    <div className="w-full max-w-4xl mx-auto p-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-4">健康度身信息采集</h1>
        <div className="bg-blue-50 text-blue-700 px-4 py-2 rounded-md inline-block mb-4 font-medium">
          第一阶段：身体数据采集
        </div>
        <p className="text-slate-600">
          请根据提示填写下列信息，我们将根据这些数据为您制定专属的饮食计划
        </p>
      </div>
      
      <Suspense fallback={<div className="text-center">加载中...</div>}>
        <InfoCollectionForm lang={lang} />
      </Suspense>
    </div>
  );
}

// 用于根据用户的受众群体选择显示对应的信息收集表单
export default async function InfoCollectionPage({ 
  params 
}: { 
  params: { lang: string } 
}) {
  const lang = params.lang;
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-emerald-50/20 py-10">
      <div className="container mx-auto">
        <InfoCollectionClient lang={lang} />
      </div>
    </div>
  );
} 
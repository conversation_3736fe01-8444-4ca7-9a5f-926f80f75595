'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import AudienceGroupSelector from '@/components/collection/audience-group-selector'
import HealthAssessmentReport from '@/components/collection/health-assessment-report'
import ConfirmDialog from '@/components/ui/confirm-dialog'
import { HealthAssessmentReport as DatabaseReport } from '@/lib/db/health-assessment'
import { Loader2 } from 'lucide-react'

interface HealthAssessmentSectionProps {
  lang: string
}

export default function HealthAssessmentSection({ lang }: HealthAssessmentSectionProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasReport, setHasReport] = useState(false)
  const [report, setReport] = useState<DatabaseReport | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const searchParams = useSearchParams()

  // 检查是否存在健康评估报告
  const checkExistingReport = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/check-health-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      const result = await response.json()
      
      if (result.success) {
        setHasReport(result.hasReport)
        setReport(result.report)
      } else {
        setError('检查报告时发生错误')
      }
    } catch (err) {
      console.error('检查现有报告时出错:', err)
      setError('检查报告时发生网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  // 检查是否已有评估报告
  useEffect(() => {
    checkExistingReport()
  }, [searchParams.get('refresh')])

  // 重新生成报告
  const handleRegenerate = () => {
    // 清除localStorage中的群体选择和完成状态
    if (typeof window !== 'undefined') {
      localStorage.removeItem('selected_audience_group')
      localStorage.removeItem('profile_completed')
    }
    
    // 重置状态，显示群体选择器
    setHasReport(false)
    setReport(null)
  }

  // 显示删除确认对话框
  const handleDelete = () => {
    setShowDeleteConfirm(true)
  }

  // 执行删除报告
  const confirmDelete = async () => {
    setIsDeleting(true)
    
    try {
      // 调用删除API
      const response = await fetch('/api/delete-health-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const data = await response.json()

      if (data.success) {
        // 删除成功，重置状态
        setHasReport(false)
        setReport(null)
        
        // 清除localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('selected_audience_group')
          localStorage.removeItem('profile_completed')
        }
      } else {
        throw new Error(data.error || '删除失败')
      }
    } catch (error) {
      console.error('删除报告时出错:', error)
      setError('删除报告时发生错误，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/50 p-6 md:p-8 shadow-xl mb-12">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">正在检查您的健康评估记录...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/50 p-6 md:p-8 shadow-xl mb-12">
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="text-blue-600 hover:underline"
          >
            重新尝试
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      {hasReport && report ? (
        <HealthAssessmentReport 
          report={report}
          onRegenerate={handleRegenerate}
          onDelete={handleDelete}
          isDeleting={isDeleting}
        />
      ) : (
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/50 p-6 md:p-8 shadow-xl mb-12">
          <AudienceGroupSelector lang={lang} />
        </div>
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDelete}
        title="删除健康评估报告"
        description="确定要删除您的健康评估报告吗？此操作不可撤销，删除后您需要重新进行健康评估。"
        confirmText={isDeleting ? "删除中..." : "确认删除"}
        cancelText="取消"
        isDestructive={true}
      />
    </>
  )
} 
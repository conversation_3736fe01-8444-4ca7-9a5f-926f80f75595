// 中药材信息
export interface HerbalIngredient {
  name: string // 药材名称，如"党参"
  dosage: string // 用量，如"15g"
  properties: string // 药性，如"甘、平"
  meridians: string // 归经，如"脾、肺经"
  effects: string // 功效，如"补中益气，健脾益肺"
}

// 中药方剂
export interface HerbalFormula {
  name: string // 方剂名称，如"参苓白术散加减方"
  ingredients: HerbalIngredient[] // 药材组成
  preparation: string // 制备方法，如"水煎服"
  dosage: string // 用法用量，如"每日1剂，分早晚两次温服"
  effects: string // 主要功效
  indications: string // 适应症
  contraindications?: string // 禁忌症
  notes?: string // 特别说明
}

// 每日中药方案
export interface DailyHerbalPlan {
  date: string // 日期，如"周一"
  morning: {
    formula: HerbalFormula // 早上服用的方剂
    timing: string // 服用时间，如"早餐前30分钟"
    notes?: string // 特别注意事项
  }
  evening: {
    formula: HerbalFormula // 晚上服用的方剂
    timing: string // 服用时间，如"晚餐后1小时"
    notes?: string // 特别注意事项
  }
  lifestyle?: {
    exercise: string // 配合运动建议
    diet: string // 饮食注意事项
    rest: string // 作息建议
  }
}

// 一周中药方案
export interface WeeklyHerbalPlan {
  day1: DailyHerbalPlan
  day2: DailyHerbalPlan
  day3: DailyHerbalPlan
  day4: DailyHerbalPlan
  day5: DailyHerbalPlan
  day6: DailyHerbalPlan
  day7: DailyHerbalPlan
}

// 中医药减肥方案
export interface HerbalWeightLossPlan {
  id?: number
  user_id: string
  plan_type: 'weight_loss' // 方案类型
  weekly_plan: WeeklyHerbalPlan
  tcm_diagnosis: string // 中医诊断
  constitution_type: string // 体质类型
  treatment_principle: string // 治疗原则
  expected_effects: string // 预期效果
  precautions: string // 注意事项
  basic_analysis: string // 基础分析（来自健康评估）
  herbal_recommendations: string // 中药调理建议（来自健康评估）
  created_at?: Date
  updated_at?: Date
}

// AI生成的中药方案数据
export interface HerbalPlanGeneration {
  planType: 'weight_loss'
  weeklyPlan: WeeklyHerbalPlan
  tcmDiagnosis: string
  constitutionType: string
  treatmentPrinciple: string
  expectedEffects: string
  precautions: string
}

// 中药方案请求
export interface HerbalPlanRequest {
  userId: string
}

// 中药方案响应
export interface HerbalPlanResponse {
  success: boolean
  herbalPlan?: HerbalWeightLossPlan
  error?: string
}

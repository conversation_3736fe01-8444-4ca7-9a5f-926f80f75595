import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!,
    {
        stripeAccount: process.env.STRIPE_ACCOUNT_ID
    });

export async function POST(req: Request) {

    try {

        const { priceId, userId, type, customerEmail } = await req.json();
        
        console.log('=== Stripe API 调试信息 ===');
        console.log('接收到的参数:', { priceId, userId, type, customerEmail });
        
        if (!customerEmail) {
            console.error('customerEmail 参数为空');
            return NextResponse.json({ error: '用户邮箱不存在' }, { status: 400 });
        }
        
        const param: Stripe.Checkout.SessionCreateParams = {
            ui_mode: 'embedded',
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            // redirect_on_completion: 'if_required',
            redirect_on_completion: 'never', 
            automatic_tax: {enabled: true},
            client_reference_id: userId,
            // return_url:`${req.headers.get("origin")}/return?session_id={CHECKOUT_SESSION_ID}`,
            metadata: {
                userId: userId,
                priceId: priceId
            },
            customer_email: customerEmail,
            custom_text: {
                submit: {
                    message: '邮箱已自动填充并锁定，以确保支付与您的账户关联',
                },
            },
        }

        if(type === "1"){
            param.mode = 'payment';
            param.payment_intent_data = { metadata: { userId: userId, priceId: priceId } };
        }else{
            param.mode = 'subscription';
            param.subscription_data = { metadata: { userId: userId, priceId: priceId } };
        }
        
        console.log('创建 Stripe 会话参数:', {
            customer_email: param.customer_email,
            mode: param.mode,
            userId: userId
        });
        
        const session = await stripe.checkout.sessions.create(param);
        
        console.log('Stripe 会话创建成功:', session.id);

        return NextResponse.json({ clientSecret: session.client_secret });
    } catch (error) {
        console.log("payment error", error)
        return NextResponse.json({ error: 'payment error' }, { status: 500 });
    }
}
'use server'

import { getLatestDietPlan, saveDietPlan, deleteDietPlan } from '@/lib/db/diet-plan'
import { DietPlan, DietPlanResponse } from '@/types/diet-plan'

/**
 * 获取用户最新的饮食计划
 */
export async function getUserDietPlan(userId: string): Promise<DietPlan | null> {
  try {
    const dietPlan = await getLatestDietPlan(userId)
    return dietPlan
  } catch (error) {
    console.error('获取饮食计划失败:', error)
    return null
  }
}

/**
 * 生成新的饮食计划
 */
export async function generateDietPlan(
  userId: string, 
  includeHerbalPlan: boolean
): Promise<DietPlanResponse> {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/generate-diet-plan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        includeHerbalPlan
      })
    })

    const result = await response.json()
    
    if (!response.ok) {
      return {
        success: false,
        error: result.error || '生成饮食计划失败'
      }
    }

    return result
  } catch (error) {
    console.error('生成饮食计划失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络错误，请稍后重试'
    }
  }
}

/**
 * 删除用户的饮食计划
 */
export async function removeUserDietPlan(userId: string): Promise<boolean> {
  try {
    const success = await deleteDietPlan(userId)
    return success
  } catch (error) {
    console.error('删除饮食计划失败:', error)
    return false
  }
}

/**
 * 检查用户是否有饮食计划
 */
export async function checkUserHasDietPlan(userId: string): Promise<boolean> {
  try {
    const dietPlan = await getLatestDietPlan(userId)
    return dietPlan !== null
  } catch (error) {
    console.error('检查饮食计划失败:', error)
    return false
  }
} 
import { DietPlan } from '@/types/diet-plan'
import { HerbalWeightLossPlan } from '@/types/herbal-plan'
import { 
  ShoppingList, 
  ShoppingListItem, 
  FoodCategory, 
  HerbalCategory 
} from '@/types/shopping-list'

/**
 * 从饮食计划中提取食材
 */
export function extractIngredientsFromDietPlan(dietPlan: DietPlan): ShoppingListItem[] {
  const ingredients: ShoppingListItem[] = []
  const ingredientMap = new Map<string, ShoppingListItem>()

  // 遍历一周的饮食计划
  Object.entries(dietPlan.weekly_plan).forEach(([dayKey, dayPlan]) => {
    // 遍历每日的餐食
    Object.entries(dayPlan.meals).forEach(([mealType, meal]) => {
      if (!meal) return

      // 遍历每餐的菜品
      meal.dishes.forEach(dish => {
        // 解析食材字符串
        const parsedIngredients = parseIngredientsString(dish.ingredients, dish.name)
        
        parsedIngredients.forEach(ingredient => {
          const key = ingredient.name.toLowerCase()
          
          if (ingredientMap.has(key)) {
            // 如果食材已存在，合并数量和来源菜品
            const existing = ingredientMap.get(key)!
            existing.source_dishes = [...(existing.source_dishes || []), dish.name]
            // 这里可以添加数量合并逻辑，但由于数量格式复杂，暂时保持原有数量
          } else {
            // 新食材
            ingredientMap.set(key, {
              name: ingredient.name,
              quantity: ingredient.quantity,
              unit: ingredient.unit,
              category: categorizeFood(ingredient.name),
              type: 'food',
              source_dishes: [dish.name],
              is_purchased: false
            })
          }
        })
      })
    })
  })

  return Array.from(ingredientMap.values())
}

/**
 * 从中药方案中提取药材
 */
export function extractHerbsFromHerbalPlan(herbalPlan: HerbalWeightLossPlan): ShoppingListItem[] {
  const herbs: ShoppingListItem[] = []
  const herbMap = new Map<string, ShoppingListItem>()

  // 遍历一周的中药方案
  Object.entries(herbalPlan.weekly_plan).forEach(([dayKey, dayPlan]) => {
    // 遍历早晚方剂
    ['morning', 'evening'].forEach(timeSlot => {
      const timeData = dayPlan[timeSlot as keyof typeof dayPlan] as any
      if (!timeData?.formula) return

      const formula = timeData.formula
      
      // 遍历方剂中的药材
      formula.ingredients.forEach((ingredient: any) => {
        const key = ingredient.name.toLowerCase()
        
        if (herbMap.has(key)) {
          // 如果药材已存在，合并数量和来源方剂
          const existing = herbMap.get(key)!
          existing.source_formulas = [...(existing.source_formulas || []), formula.name]
          // 合并数量（简单相加，实际可能需要更复杂的逻辑）
          const existingAmount = parseFloat(existing.quantity) || 0
          const newAmount = parseFloat(ingredient.dosage) || 0
          existing.quantity = `${existingAmount + newAmount}g`
        } else {
          // 新药材
          herbMap.set(key, {
            name: ingredient.name,
            quantity: ingredient.dosage,
            unit: 'g',
            category: categorizeHerb(ingredient.name, ingredient.effects),
            type: 'herbal',
            source_formulas: [formula.name],
            is_purchased: false,
            notes: `${ingredient.properties} | ${ingredient.meridians} | ${ingredient.effects}`
          })
        }
      })
    })
  })

  return Array.from(herbMap.values())
}

/**
 * 生成完整的购物清单
 */
export function generateShoppingList(
  userId: string,
  dietPlan?: DietPlan,
  herbalPlan?: HerbalWeightLossPlan
): ShoppingList {
  const items: ShoppingListItem[] = []

  // 从饮食计划提取食材
  if (dietPlan) {
    const foodItems = extractIngredientsFromDietPlan(dietPlan)
    items.push(...foodItems)
  }

  // 从中药方案提取药材
  if (herbalPlan) {
    const herbalItems = extractHerbsFromHerbalPlan(herbalPlan)
    items.push(...herbalItems)
  }

  return {
    user_id: userId,
    diet_plan_id: dietPlan?.id,
    herbal_plan_id: herbalPlan?.id,
    items,
    total_items: items.length,
    purchased_items: 0,
    estimated_cost: 0
  }
}

/**
 * 解析食材字符串，提取食材名称和数量
 */
function parseIngredientsString(ingredientsStr: string, dishName: string): Array<{name: string, quantity: string, unit: string}> {
  const ingredients: Array<{name: string, quantity: string, unit: string}> = []
  
  // 简单的解析逻辑，可以根据实际情况优化
  const parts = ingredientsStr.split(/[，,、]/)
  
  parts.forEach(part => {
    const trimmed = part.trim()
    if (!trimmed) return

    // 尝试匹配 "食材名 数量单位" 的格式
    const match = trimmed.match(/^(.+?)\s*(\d+(?:\.\d+)?)\s*([^\d\s]+)$/)
    
    if (match) {
      const [, name, amount, unit] = match
      ingredients.push({
        name: name.trim(),
        quantity: `${amount}${unit}`,
        unit: unit
      })
    } else {
      // 如果无法解析数量，使用默认值
      ingredients.push({
        name: trimmed,
        quantity: '适量',
        unit: '份'
      })
    }
  })

  return ingredients
}

/**
 * 食材分类
 */
function categorizeFood(foodName: string): FoodCategory {
  const vegetables = ['白菜', '菠菜', '西红柿', '黄瓜', '胡萝卜', '土豆', '洋葱', '大蒜', '生姜', '韭菜', '芹菜', '豆芽', '茄子', '青椒', '冬瓜', '南瓜', '丝瓜', '苦瓜', '萝卜', '莲藕', '山药', '芋头']
  const fruits = ['苹果', '香蕉', '橙子', '柠檬', '葡萄', '草莓', '蓝莓', '猕猴桃', '梨', '桃子', '李子', '樱桃', '西瓜', '哈密瓜', '火龙果', '芒果', '菠萝', '柚子', '橘子']
  const meat = ['猪肉', '牛肉', '羊肉', '鸡肉', '鸭肉', '鹅肉', '兔肉', '瘦肉', '排骨', '鸡胸肉', '鸡腿', '牛腩', '羊排', '猪蹄', '鸡翅']
  const seafood = ['鱼', '虾', '蟹', '贝类', '鲍鱼', '海参', '带鱼', '黄鱼', '鲤鱼', '草鱼', '鲫鱼', '三文鱼', '金枪鱼', '鳕鱼', '虾仁', '蛤蜊', '扇贝', '海带', '紫菜']
  const grains = ['大米', '小米', '糯米', '黑米', '面粉', '燕麦', '小麦', '玉米', '高粱', '荞麦', '薏米', '红豆', '绿豆', '黑豆', '黄豆', '花生', '芝麻', '核桃', '杏仁']
  const dairy = ['牛奶', '酸奶', '奶酪', '黄油', '奶油', '鸡蛋', '鸭蛋', '鹌鹑蛋']
  const seasonings = ['盐', '糖', '醋', '生抽', '老抽', '料酒', '胡椒粉', '花椒', '八角', '桂皮', '香叶', '孜然', '辣椒粉', '蚝油', '豆瓣酱', '甜面酱', '芝麻酱']
  const oils = ['植物油', '橄榄油', '花生油', '菜籽油', '玉米油', '芝麻油', '香油', '猪油', '牛油']

  if (vegetables.some(v => foodName.includes(v))) return FoodCategory.VEGETABLES
  if (fruits.some(f => foodName.includes(f))) return FoodCategory.FRUITS
  if (meat.some(m => foodName.includes(m))) return FoodCategory.MEAT
  if (seafood.some(s => foodName.includes(s))) return FoodCategory.SEAFOOD
  if (grains.some(g => foodName.includes(g))) return FoodCategory.GRAINS
  if (dairy.some(d => foodName.includes(d))) return FoodCategory.DAIRY
  if (seasonings.some(s => foodName.includes(s))) return FoodCategory.SEASONINGS
  if (oils.some(o => foodName.includes(o))) return FoodCategory.OILS

  return FoodCategory.OTHER
}

/**
 * 药材分类
 */
function categorizeHerb(herbName: string, effects: string): HerbalCategory {
  if (effects.includes('补') || effects.includes('益气') || effects.includes('养血')) {
    return HerbalCategory.TONIFYING
  }
  if (effects.includes('清热') || effects.includes('解毒') || effects.includes('泻火')) {
    return HerbalCategory.CLEARING_HEAT
  }
  if (effects.includes('理气') || effects.includes('行气') || effects.includes('疏肝')) {
    return HerbalCategory.REGULATING_QI
  }
  if (effects.includes('活血') || effects.includes('化瘀') || effects.includes('通络')) {
    return HerbalCategory.PROMOTING_BLOOD
  }
  if (effects.includes('化痰') || effects.includes('祛痰') || effects.includes('燥湿')) {
    return HerbalCategory.TRANSFORMING_PHLEGM
  }
  if (effects.includes('利水') || effects.includes('渗湿') || effects.includes('消肿')) {
    return HerbalCategory.DIURETIC
  }
  if (effects.includes('消食') || effects.includes('健胃') || effects.includes('助消化')) {
    return HerbalCategory.DIGESTIVE
  }
  if (effects.includes('安神') || effects.includes('宁心') || effects.includes('镇静')) {
    return HerbalCategory.CALMING
  }
  if (effects.includes('解表') || effects.includes('发汗') || effects.includes('散寒')) {
    return HerbalCategory.EXTERIOR_RELEASING
  }

  return HerbalCategory.OTHER
}

'use client'

import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs'
import { Download, Share2, Heart, Stethoscope, Leaf, Calendar, RefreshCw, Trash2, Activity, Target, FlaskConical, ArrowLeft } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { HealthAssessmentReport as DatabaseReport } from '@/lib/db/health-assessment'
import { DietPlanGenerator, DietPlanViewer } from '@/components/diet-plan'
import { getUserDietPlan } from '@/actions/diet-plan'
import { DietPlan } from '@/types/diet-plan'
import { HerbalPlanGenerator, HerbalPlanViewer } from '@/components/herbal-plan'
import { getUserHerbalPlan } from '@/actions/herbal-plan'
import { HerbalWeightLossPlan } from '@/types/herbal-plan'
import { ShoppingListGenerator, ShoppingListViewer } from '@/components/shopping-list'
import { getUserShoppingList } from '@/actions/shopping-list'
import { ShoppingList } from '@/types/shopping-list'

interface HealthAssessmentReportProps {
  report: DatabaseReport
  onRegenerate: () => void
  onDelete: () => void
  isDeleting?: boolean
  onSubscribe?: () => void
}

export default function HealthAssessmentReport({ 
  report,
  onRegenerate,
  onDelete,
  isDeleting = false,
  onSubscribe 
}: HealthAssessmentReportProps) {
  
  const [activeTab, setActiveTab] = useState('basic')
  const [showDietPlan, setShowDietPlan] = useState(false)
  const [existingDietPlan, setExistingDietPlan] = useState<DietPlan | null>(null)
  const [isLoadingDietPlan, setIsLoadingDietPlan] = useState(false)
  const [showHerbalPlan, setShowHerbalPlan] = useState(false)
  const [existingHerbalPlan, setExistingHerbalPlan] = useState<HerbalWeightLossPlan | null>(null)
  const [isLoadingHerbalPlan, setIsLoadingHerbalPlan] = useState(false)
  const [showShoppingList, setShowShoppingList] = useState(false)
  const [existingShoppingList, setExistingShoppingList] = useState<ShoppingList | null>(null)
  const [isLoadingShoppingList, setIsLoadingShoppingList] = useState(false)

  // 处理点击个性化餐单
  const handleDietPlanClick = async () => {
    setIsLoadingDietPlan(true)
    try {
      // 检查数据库是否已有饮食计划
      const existingPlan = await getUserDietPlan(report.user_id)
      if (existingPlan) {
        // 如果有现有计划，直接显示
        setExistingDietPlan(existingPlan)
        setShowDietPlan(true)
      } else {
        // 如果没有计划，显示生成界面
        setExistingDietPlan(null)
        setShowDietPlan(true)
      }
    } catch (error) {
      console.error('检查饮食计划失败:', error)
      // 出错时默认显示生成界面
      setExistingDietPlan(null)
      setShowDietPlan(true)
    } finally {
      setIsLoadingDietPlan(false)
    }
  }

  // 处理重新生成饮食计划
  const handleRegeneratePlan = () => {
    setExistingDietPlan(null) // 清除现有计划，显示生成界面
  }

  // 处理新生成的饮食计划
  const handlePlanGenerated = async (newPlan: DietPlan) => {
    setExistingDietPlan(newPlan) // 设置新计划，自动切换到展示模式

    // 自动更新购物清单
    try {
      await fetch('/api/generate-shopping-list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: report.user_id,
          includeDietPlan: true,
          includeHerbalPlan: true
        })
      })
      console.log('购物清单已自动更新（饮食计划）')
    } catch (error) {
      console.error('自动更新购物清单失败:', error)
    }
  }

  // 处理点击中医调理方案
  const handleHerbalPlanClick = async () => {
    setIsLoadingHerbalPlan(true)
    try {
      // 检查数据库是否已有中药方案
      const existingPlan = await getUserHerbalPlan(report.user_id)
      if (existingPlan) {
        // 如果有现有方案，直接显示
        setExistingHerbalPlan(existingPlan)
        setShowHerbalPlan(true)
      } else {
        // 如果没有方案，显示生成界面
        setExistingHerbalPlan(null)
        setShowHerbalPlan(true)
      }
    } catch (error) {
      console.error('检查中药方案失败:', error)
      // 出错时默认显示生成界面
      setExistingHerbalPlan(null)
      setShowHerbalPlan(true)
    } finally {
      setIsLoadingHerbalPlan(false)
    }
  }

  // 处理重新生成中药方案
  const handleRegenerateHerbalPlan = () => {
    setExistingHerbalPlan(null) // 清除现有方案，显示生成界面
  }

  // 处理新生成的中药方案
  const handleHerbalPlanGenerated = async (newPlan: HerbalWeightLossPlan) => {
    setExistingHerbalPlan(newPlan) // 设置新方案，自动切换到展示模式

    // 自动更新购物清单
    try {
      await fetch('/api/generate-shopping-list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: report.user_id,
          includeDietPlan: true,
          includeHerbalPlan: true
        })
      })
      console.log('购物清单已自动更新（中药方案）')
    } catch (error) {
      console.error('自动更新购物清单失败:', error)
    }
  }

  // 处理点击购物清单
  const handleShoppingListClick = async () => {
    setIsLoadingShoppingList(true)
    try {
      // 检查数据库是否已有购物清单
      const existingList = await getUserShoppingList(report.user_id)
      if (existingList) {
        // 如果有现有清单，直接显示
        setExistingShoppingList(existingList)
        setShowShoppingList(true)
      } else {
        // 如果没有清单，显示生成界面
        setExistingShoppingList(null)
        setShowShoppingList(true)
      }
    } catch (error) {
      console.error('检查购物清单失败:', error)
      // 出错时默认显示生成界面
      setExistingShoppingList(null)
      setShowShoppingList(true)
    } finally {
      setIsLoadingShoppingList(false)
    }
  }

  // 处理重新生成购物清单
  const handleRegenerateShoppingList = () => {
    setExistingShoppingList(null) // 清除现有清单，显示生成界面
  }

  // 处理新生成的购物清单
  const handleShoppingListGenerated = (newList: ShoppingList) => {
    setExistingShoppingList(newList) // 设置新清单，自动切换到展示模式
  }
  
  // 获取群体身份显示文本
  const getGroupIdentity = (groupId: string) => {
    switch(groupId) {
      case 'weight-loss':
        return '希望健康瘦身人群'
      case 'fitness':
        return '健身爱好者'
      case 'white-collar':
        return '都市白领'
      default:
        return '希望健康瘦身人群' // 默认值
    }
  }

  // 渲染文本段落
  const renderTextSection = (text?: string) => {
    if (!text) return null
    return (
      <div className="prose prose-gray max-w-none">
        {text.split('\n').map((line: string, index: number) => (
          <p key={index} className="text-gray-700 leading-relaxed mb-4">
            {line}
          </p>
        ))}
      </div>
    )
  }

  // 选项卡配置
  const tabsConfig = [
    {
      id: 'basic',
      label: '基本情况分析',
      icon: Heart,
      color: 'text-rose-600',
      gradient: 'from-rose-500 to-pink-500',
      content: report.basic_analysis,
      description: '全面分析您的身体基本状况'
    },
    {
      id: 'constitution',
      label: '中医体质诊断',
      icon: Activity,
      color: 'text-emerald-600',
      gradient: 'from-emerald-500 to-teal-500',
      content: { constitution: report.tcm_constitution, risks: report.health_risks },
      description: '基于中医理论的个性化体质分析'
    },
    {
      id: 'plan',
      label: '中医减肥方案',
      icon: FlaskConical,
      color: 'text-violet-600',
      gradient: 'from-violet-500 to-purple-500',
      content: { 
        diet: report.diet_recommendations, 
        herbal: report.herbal_recommendations, 
        lifestyle: report.lifestyle_adjustments 
      },
      description: '传统中医调理与现代科学结合'
    },
    {
      id: 'suggestions',
      label: '个性化建议',
      icon: Target,
      color: 'text-blue-600',
      gradient: 'from-blue-500 to-indigo-500',
      content: { shortTerm: report.short_term_goals, longTerm: report.long_term_plan },
      description: '量身定制的健康改善建议'
    }
  ]

  // 如果显示饮食计划，则渲染饮食计划生成器
  if (showDietPlan) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-8">
        <div className="container mx-auto px-4 max-w-6xl">
          {/* 返回按钮 */}
          <div className="mb-6">
            <Button
              onClick={() => setShowDietPlan(false)}
              variant="outline"
              className="flex items-center gap-2 hover:bg-blue-50"
            >
              <ArrowLeft className="w-4 h-4" />
              返回健康评估报告
            </Button>
          </div>

          {/* 饮食计划展示 */}
          {existingDietPlan ? (
            <DietPlanViewer
              dietPlan={existingDietPlan}
              onRegenerate={handleRegeneratePlan}
              isRegenerating={false}
              className="mb-8"
            />
          ) : (
            <DietPlanGenerator
              userId={report.user_id}
              className="mb-8"
              onPlanGenerated={handlePlanGenerated}
            />
          )}
        </div>
      </div>
    )
  }

  // 如果显示中药方案，则渲染中药方案生成器
  if (showHerbalPlan) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-amber-50 py-8">
        <div className="container mx-auto px-4 max-w-6xl">
          {/* 返回按钮 */}
          <div className="mb-6">
            <Button
              onClick={() => setShowHerbalPlan(false)}
              variant="outline"
              className="flex items-center gap-2 hover:bg-green-50"
            >
              <ArrowLeft className="w-4 h-4" />
              返回健康评估报告
            </Button>
          </div>

          {/* 中药方案展示 */}
          {existingHerbalPlan ? (
            <HerbalPlanViewer
              herbalPlan={existingHerbalPlan}
              onRegenerate={handleRegenerateHerbalPlan}
              isRegenerating={false}
              className="mb-8"
            />
          ) : (
            <HerbalPlanGenerator
              userId={report.user_id}
              className="mb-8"
              onPlanGenerated={handleHerbalPlanGenerated}
            />
          )}
        </div>
      </div>
    )
  }

  // 如果显示购物清单，则渲染购物清单生成器
  if (showShoppingList) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 py-8">
        <div className="container mx-auto px-4 max-w-6xl">
          {/* 返回按钮 */}
          <div className="mb-6">
            <Button
              onClick={() => setShowShoppingList(false)}
              variant="outline"
              className="flex items-center gap-2 hover:bg-blue-50"
            >
              <ArrowLeft className="w-4 h-4" />
              返回健康评估报告
            </Button>
          </div>

          {/* 购物清单展示 */}
          {existingShoppingList ? (
            <ShoppingListViewer
              shoppingList={existingShoppingList}
              onRegenerate={handleRegenerateShoppingList}
              isRegenerating={false}
              className="mb-8"
            />
          ) : (
            <ShoppingListGenerator
              userId={report.user_id}
              className="mb-8"
              onListGenerated={handleShoppingListGenerated}
            />
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* 报告头部 */}
        <Card className="mb-8 border-0 shadow-2xl bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 text-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/80 via-purple-600/80 to-blue-600/80"></div>
          <CardHeader className="pb-8 relative z-10">
            <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
              <div className="flex-1">
                <CardTitle className="text-2xl lg:text-4xl font-bold mb-3 flex items-center gap-3 lg:gap-4">
                  <div className="w-10 h-10 lg:w-12 lg:h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <Stethoscope className="w-5 h-5 lg:w-7 lg:h-7" />
                  </div>
                  您的中医健康评估报告
                </CardTitle>
                <p className="text-blue-100 text-lg lg:text-xl">
                  基于传统中医理论的个性化健康分析与指导
                </p>
              </div>
              <div className="text-left lg:text-right">
                <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-sm lg:text-lg px-3 py-1 lg:px-4 lg:py-2">
                  {getGroupIdentity(report.audience_group || 'weight-loss')}
                </Badge>
                <p className="text-sm text-blue-100 mt-2 lg:mt-3">
                  生成时间：{new Date(report.created_at || Date.now()).toLocaleDateString('zh-CN')}
                </p>
              </div>
            </div>
            
            {/* 操作按钮 */}
            <div className="flex flex-col sm:flex-row gap-3 lg:gap-4 mt-6">
              <Button
                onClick={onRegenerate}
                variant="outline"
                className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm transition-all duration-300"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                重新评估
              </Button>
              <Button
                onClick={onDelete}
                variant="outline"
                className="bg-red-500/20 border-red-300/50 text-white hover:bg-red-500/30 backdrop-blur-sm transition-all duration-300"
                disabled={isDeleting}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {isDeleting ? '删除中...' : '删除报告'}
              </Button>
            </div>
          </CardHeader>
        </Card>

        {/* 选项卡内容 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* 选项卡导航 */}
          <div className="mb-8">
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 gap-2 bg-white/80 backdrop-blur-sm p-3 rounded-2xl shadow-lg border h-auto">
              {tabsConfig.map((tab) => {
                const Icon = tab.icon
                return (
                  <TabsTrigger 
                    key={tab.id} 
                    value={tab.id}
                    className="flex flex-col items-center gap-2 p-3 lg:p-4 rounded-xl transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-lg min-h-[80px] lg:min-h-[100px]"
                  >
                    <div className={`w-6 h-6 lg:w-8 lg:h-8 rounded-full bg-gradient-to-r ${tab.gradient} flex items-center justify-center ${activeTab === tab.id ? 'scale-110' : ''} transition-transform duration-300`}>
                      <Icon className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                    </div>
                    <span className={`text-xs lg:text-sm font-medium text-center leading-tight ${activeTab === tab.id ? tab.color : 'text-gray-600'} transition-colors duration-300`}>
                      {tab.label}
                    </span>
                  </TabsTrigger>
                )
              })}
            </TabsList>
          </div>

          {/* 基本情况分析 */}
          <TabsContent value="basic" className="mt-0">
            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-rose-50">
              <CardHeader className="bg-gradient-to-r from-rose-500 to-pink-500 text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <Heart className="w-6 h-6" />
                  基本情况分析
                </CardTitle>
                <p className="text-rose-100">全面分析您的身体基本状况</p>
              </CardHeader>
              <CardContent className="p-8">
                {report.basic_analysis ? (
                  <div className="bg-white rounded-lg p-6 shadow-sm border border-rose-100">
                    {renderTextSection(report.basic_analysis)}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    暂无基本情况分析数据
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 中医体质诊断 */}
          <TabsContent value="constitution" className="mt-0">
            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-emerald-50">
              <CardHeader className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <Activity className="w-6 h-6" />
                  中医体质诊断
                </CardTitle>
                <p className="text-emerald-100">基于中医理论的个性化体质分析</p>
              </CardHeader>
              <CardContent className="p-8 space-y-6">
                {report.tcm_constitution && (
                  <div className="bg-white rounded-lg p-6 shadow-sm border border-emerald-100">
                    <h4 className="font-bold text-lg text-emerald-700 mb-4 flex items-center gap-2">
                      <Leaf className="w-5 h-5" />
                      体质诊断
                    </h4>
                    {renderTextSection(report.tcm_constitution)}
                  </div>
                )}
                
                {report.health_risks && (
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-6 shadow-sm border border-orange-200">
                    <h4 className="font-bold text-lg text-orange-700 mb-4 flex items-center gap-2">
                      <Stethoscope className="w-5 h-5" />
                      潜在健康风险预警
                    </h4>
                    {renderTextSection(report.health_risks)}
                  </div>
                )}

                {!report.tcm_constitution && !report.health_risks && (
                  <div className="text-center text-gray-500 py-8">
                    暂无中医体质诊断数据
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 中医减肥方案 */}
          <TabsContent value="plan" className="mt-0">
            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-violet-50">
              <CardHeader className="bg-gradient-to-r from-violet-500 to-purple-500 text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <FlaskConical className="w-6 h-6" />
                  中医减肥方案
                </CardTitle>
                <p className="text-violet-100">传统中医调理与现代科学结合</p>
              </CardHeader>
              <CardContent className="p-8 space-y-6">
                {report.diet_recommendations && (
                  <div className="bg-white rounded-lg p-6 shadow-sm border border-violet-100">
                    <h4 className="font-bold text-lg text-violet-700 mb-4 flex items-center gap-2">
                      <Heart className="w-5 h-5" />
                      饮食调理建议
                    </h4>
                    {renderTextSection(report.diet_recommendations)}
                  </div>
                )}
                
                {false && report.herbal_recommendations && (
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 shadow-sm border border-green-200">
                    <h4 className="font-bold text-lg text-green-700 mb-4 flex items-center gap-2">
                      <Leaf className="w-5 h-5" />
                      中药调理方案
                    </h4>
                    {renderTextSection(report.herbal_recommendations)}
                  </div>
                )}
                
                {report.lifestyle_adjustments && (
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 shadow-sm border border-blue-200">
                    <h4 className="font-bold text-lg text-blue-700 mb-4 flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      生活方式调整
                    </h4>
                    {renderTextSection(report.lifestyle_adjustments)}
                  </div>
                )}

                {!report.diet_recommendations && !report.herbal_recommendations && !report.lifestyle_adjustments && (
                  <div className="text-center text-gray-500 py-8">
                    暂无中医减肥方案数据
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 个性化建议 */}
          <TabsContent value="suggestions" className="mt-0">
            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-blue-50">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <Target className="w-6 h-6" />
                  个性化建议
                </CardTitle>
                <p className="text-blue-100">量身定制的健康改善建议</p>
              </CardHeader>
              <CardContent className="p-8 space-y-6">
                {report.short_term_goals && (
                  <div className="bg-white rounded-lg p-6 shadow-sm border border-blue-100">
                    <h4 className="font-bold text-lg text-blue-700 mb-4 flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      短期目标（1-3个月）
                    </h4>
                    {renderTextSection(report.short_term_goals)}
                  </div>
                )}
                
                {report.long_term_plan && (
                  <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-6 shadow-sm border border-indigo-200">
                    <h4 className="font-bold text-lg text-indigo-700 mb-4 flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      长期养生计划（6-12个月）
                    </h4>
                    {renderTextSection(report.long_term_plan)}
                  </div>
                )}

                {!report.short_term_goals && !report.long_term_plan && (
                  <div className="text-center text-gray-500 py-8">
                    暂无个性化建议数据
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 订阅提醒卡片 */}
        <Card className="mt-8 shadow-2xl border-0 bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 text-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/90 via-orange-400/90 to-red-400/90"></div>
          <CardContent className="p-8 relative z-10">
            <div className="flex items-start gap-6">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <Leaf className="w-8 h-8 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-bold mb-3">
                  🌟 专业中医指导，开启健康减肥之旅
                </h3>
                <p className="text-yellow-100 mb-4 text-lg">
                  想要获得更详细的饮食计划和中药处方指导吗？我们的专业中医团队为您提供：
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div 
                    className={`bg-white/10 rounded-lg p-4 backdrop-blur-sm cursor-pointer hover:bg-white/20 transition-all duration-300 hover:scale-105 ${isLoadingDietPlan ? 'opacity-50 pointer-events-none' : ''}`}
                    onClick={handleDietPlanClick}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Heart className="w-5 h-5" />
                      <span className="font-semibold">个性化餐单</span>
                    </div>
                    <p className="text-sm text-yellow-100">
                      {isLoadingDietPlan ? '正在检查已有计划...' : '一周七日三餐搭配方案'}
                    </p>
                  </div>
                  <div
                    className={`bg-white/10 rounded-lg p-4 backdrop-blur-sm cursor-pointer hover:bg-white/20 transition-all duration-300 hover:scale-105 ${isLoadingHerbalPlan ? 'opacity-50 pointer-events-none' : ''}`}
                    onClick={handleHerbalPlanClick}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <FlaskConical className="w-5 h-5" />
                      <span className="font-semibold">中医调理方案</span>
                    </div>
                    <p className="text-sm text-yellow-100">
                      {isLoadingHerbalPlan ? '正在检查已有方案...' : '中药调理配方'}
                    </p>
                  </div>
                  <div
                    className={`bg-white/10 rounded-lg p-4 backdrop-blur-sm cursor-pointer hover:bg-white/20 transition-all duration-300 hover:scale-105 ${isLoadingShoppingList ? 'opacity-50 pointer-events-none' : ''}`}
                    onClick={handleShoppingListClick}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Stethoscope className="w-5 h-5" />
                      <span className="font-semibold">购物清单</span>
                    </div>
                    <p className="text-sm text-yellow-100">
                      {isLoadingShoppingList ? '正在检查已有清单...' : '一键生成食材和药材清单'}
                    </p>
                  </div>
                </div>
                {onSubscribe && (
                  <Button 
                    onClick={onSubscribe}
                    className="bg-white text-orange-600 hover:bg-gray-100 font-bold text-lg px-8 py-3 rounded-full shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    立即订阅专业方案 ✨
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 
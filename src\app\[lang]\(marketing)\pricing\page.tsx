import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import {
  Eye,
  Camera,
  Shield,
  MapPin,
  FileText,
  BarChart3,
  CheckCircle2
} from "lucide-react"
import { type Locale, getPathname, generateAlternates } from "@/i18n-config"
import { getDictionary, i18nNamespaces } from '@/i18n'
import { Pricing } from "@/types/locales/pricing"
import { PaymentButton } from './button'
import { host } from '@/config/config'

export async function generateMetadata({ params }: { params: { lang: Locale } }) {
  const { lang } = await params
  const alternates = generateAlternates(lang, '/pricing');
  const i18nPricing = await getDictionary<Pricing>(lang, i18nNamespaces.pricing);
  return {
    title: i18nPricing.meta.title,
    description: i18nPricing.meta.description,
    keywords: i18nPricing.meta.keywords,
    twitter: {
      card: "summary_large_image", title: i18nPricing.meta.title,
      description: i18nPricing.meta.description
    },
    openGraph: {
      type: "website",
      url: `${host}${getPathname(lang, '')}`,
      title: i18nPricing.meta.title,
      description: i18nPricing.meta.description,
      siteName: "Hypertension"
    },
    alternates: {
      canonical: `${host}${getPathname(lang, '')}`,
      languages: alternates
    }
  }
}

export default async function Page({ params }: { params: { lang: Locale } }) {
  const { lang } = await params
  const i18nPricing = await getDictionary<Pricing>(lang, i18nNamespaces.pricing)

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-background to-background/80">
      <main className="flex-1">
        <section id="pricing" className="container px-4 md:px-6 py-16">
          {/* Hero Section */}
          <div className="mx-auto max-w-[58rem] text-center mb-16">
            <h2 className="text-sm font-semibold text-primary tracking-wide uppercase mb-4">
              {i18nPricing.hero.subtitle}
            </h2>
            <h1 className="text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:text-6xl lg:leading-[1.1] bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
              {i18nPricing.hero.title}
            </h1>
            <p className="mt-6 text-lg text-muted-foreground dark:text-muted-foreground/90 md:text-xl max-w-2xl mx-auto">
              {i18nPricing.hero.description}
            </p>
          </div>

          {/* Pricing Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {/* Monthly Subscription Card */}
            <Card className="border-primary relative overflow-hidden transition-all duration-300 hover:shadow-xl dark:bg-background/95 dark:border-primary/30 dark:hover:border-primary/50">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl dark:text-white">{i18nPricing.plan.title}</CardTitle>
                <div className="mt-4 flex items-baseline justify-center gap-2">
                  <span className="text-5xl font-bold dark:text-white">{i18nPricing.plan.price.amount}</span>
                  <span className="text-muted-foreground dark:text-muted-foreground/80">{i18nPricing.plan.price.period}</span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    {i18nPricing.plan.features.map((feature, index) => (
                      <div key={index} className="flex gap-2 items-center">
                        <CheckCircle2 className="h-4 w-4 text-primary" />
                        <span className="text-sm text-muted-foreground dark:text-muted-foreground/90">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <PaymentButton lang={lang} mode={i18nPricing.plan.mode} currency={i18nPricing.plan.currency}
                btnlabel={i18nPricing.plan.cta} paymentTips={i18nPricing.paymentTips} 
                product={i18nPricing.plan.product}/>
              </CardFooter>
            </Card>

            {/* Annual Subscription Card */}
            <Card className="border-primary relative overflow-hidden transition-all duration-300 hover:shadow-xl dark:bg-background/95 dark:border-primary/30 dark:hover:border-primary/50">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl dark:text-white">{i18nPricing.annual.title}</CardTitle>
                <div className="mt-4 flex items-baseline justify-center gap-2">
                  <span className="text-5xl font-bold dark:text-white">{i18nPricing.annual.price.amount}</span>
                  <span className="text-muted-foreground dark:text-muted-foreground/80">{i18nPricing.annual.price.period}</span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    {i18nPricing.annual.features.map((feature, index) => (
                      <div key={index} className="flex gap-2 items-center">
                        <CheckCircle2 className="h-4 w-4 text-primary" />
                        <span className="text-sm text-muted-foreground dark:text-muted-foreground/90">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <PaymentButton lang={lang} mode={i18nPricing.annual.mode} currency={i18nPricing.annual.currency}
                btnlabel={i18nPricing.annual.cta} paymentTips={i18nPricing.paymentTips} 
                product={i18nPricing.annual.product}/>
              </CardFooter>
            </Card>
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 text-center">
            <p className="text-sm text-muted-foreground dark:text-muted-foreground/80">
              {i18nPricing.trust.text}
            </p>
          </div>
        </section>
      </main>
    </div>
  )
}


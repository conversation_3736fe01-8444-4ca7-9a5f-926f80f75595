'use client'

import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ClipboardList, ArrowRight } from "lucide-react";

interface ProfileCompletionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  lang: string;
}

export default function ProfileCompletionDialog({
  isOpen,
  onClose,
  onComplete,
  lang,
}: ProfileCompletionDialogProps) {
  // 添加一些状态来控制动画或其他交互行为
  const [isHovering, setIsHovering] = useState(false);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
              <ClipboardList className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <DialogTitle className="text-center text-xl">完善个人信息，即可获取专属饮食计划</DialogTitle>
          <DialogDescription className="text-center">
            我们需要了解您的基本情况，以便为您提供更精准的饮食建议
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <ul className="space-y-3">
            {[
              "填写基础身体数据",
              "选择饮食偏好",
              "设定健康目标"
            ].map((item, index) => (
              <li key={index} className="flex items-center gap-3">
                <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                  <span className="text-xs text-blue-600 font-medium">{index + 1}</span>
                </div>
                <span className="text-sm">{item}</span>
              </li>
            ))}
          </ul>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row sm:justify-center gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="sm:w-auto"
          >
            稍后再说
          </Button>
          <Button
            onClick={onComplete}
            className={`sm:w-auto ${isHovering ? 'animate-pulse' : ''}`}
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            立即完善
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
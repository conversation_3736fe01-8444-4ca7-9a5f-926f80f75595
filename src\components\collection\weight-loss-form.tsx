'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group'
import { cn } from '@/lib/utils'
import { DatePicker } from '@/components/date-picker'
import { WeightGauge } from '@/components/weight-gauge'

// 表单验证 Schema
const weightLossFormSchema = z.object({
  // 身高
  height: z.string().min(1, { message: '请输入您的身高' }),
  heightUnit: z.enum(['cm', 'inch'], {
    required_error: '请选择身高单位',
  }),
  
  // 体重
  weight: z.string().min(1, { message: '请输入您的体重' }),
  weightUnit: z.enum(['kg', 'lb'], {
    required_error: '请选择体重单位',
  }),
  
  // 体脂率
  bodyFatPercentage: z.string().optional(),
  knowsBodyFat: z.enum(['yes', 'no'], {
    required_error: '请选择是否知道体脂率',
  }),
  
  // 出生日期（替代年龄）
  birthDate: z.date({
    required_error: '请选择您的出生日期',
  }),
  
  // 性别
  gender: z.enum(['male', 'female', 'other'], {
    required_error: '请选择您的性别',
  }),
  
  // 过往体重变化情况
  weightChangeHistory: z.enum(['stable', 'increasing', 'fluctuating'], {
    required_error: '请选择您的体重变化情况',
  }),
  
  // 健康目标 - 期望减重数值
  weightLossGoal: z.string().min(1, { message: '请输入您期望减少的体重' }),
  weightLossGoalUnit: z.enum(['kg', 'lb', 'bodyFat'], {
    required_error: '请选择减重单位',
  }),
  
  // 健康目标 - 期望达成时间
  goalTimeframe: z.string().min(1, { message: '请选择您期望达成目标的时间' }),
  
  // 节食经历
  hasDietHistory: z.enum(['yes', 'no'], {
    required_error: '请选择是否有过盲目节食经历',
  }),
  
  // 节食后反应（多选）
  dietReactions: z.array(z.string()).optional(),
  
  // 饮食偏好
  favoriteFoods: z.array(z.string()).optional(),
  dislikedFoods: z.array(z.string()).optional(),
  religiousDietaryRestrictions: z.enum(['none', 'halal', 'kosher', 'hindu', 'buddhist', 'other']).optional(),
  personalDietaryRestrictions: z.array(z.string()).optional(),
  dietaryRestrictions: z.array(z.string()).optional(),
  lowCalorieAcceptance: z.enum(['high', 'medium', 'low'], {
    required_error: '请选择您对低热量、低脂肪食材的接受度',
  }),
  unhealthyFoodPreference: z.array(z.string()).optional(),
  eatingOutFrequency: z.enum(['0-1', '2-3', '4-5', '6+'], {
    required_error: '请选择您每周在外就餐的次数',
  }),
  medicinalDietPlan: z.enum(['daily', 'threedays', 'weekly', 'monthly', 'none'], {
    required_error: '请选择您是否愿意加入药膳饮食计划',
  }),
  
  // 饮食习惯
  regularMeals: z.enum(['regular', 'irregular'], {
    required_error: '请选择您的三餐是否规律',
  }),
  snackingHabit: z.enum(['yes', 'no'], {
    required_error: '请选择您是否有吃零食的习惯',
  }),
  snackTypes: z.array(z.string()).optional(),
  eatingSpeed: z.enum(['fast', 'medium', 'slow'], {
    required_error: '请选择您的用餐速度',
  }),
  foodAllergies: z.array(z.string()).optional(),
  chronicDiseases: z.array(z.string()).optional(),
  medications: z.array(z.string()).optional(),
  sleepQuality: z.enum(['good', 'fair', 'poor', 'irregular']).optional(),
  bodyHeaviness: z.enum(['yes', 'no', 'sometimes']).optional(),
  temperatureSensitivity: z.enum(['heat', 'cold', 'normal']).optional(),
  oilySkin: z.enum(['yes', 'no', 'sometimes']).optional(),
  sweating: z.enum(['yes', 'no', 'onlyExercise']).optional(),
  backPain: z.enum(['yes', 'no', 'sometimes']).optional(),
  appetite: z.enum(['yes', 'no', 'normal']).optional(),
  hairCondition: z.enum(['yes', 'no', 'mild']).optional(),
  menstrualRegularity: z.enum(['yes', 'no', 'sometimes']).optional(),
})

// 定义表单数据类型
type WeightLossFormValues = z.infer<typeof weightLossFormSchema>

// 组件属性定义
interface WeightLossFormProps {
  lang: string;
  onBack?: () => void; // 返回上一级组件的回调函数
}

// 定义选项类型
interface FormOption {
  value: string;
  label: string;
  icon?: string;
}

// 定义表单字段类型
interface FormField {
  id: string;
  label: string;
  type: string;
  placeholder?: string;
  options?: FormOption[];
  description?: string;
}

// 定义表单部分类型
interface FormSection {
  title: string;
  fields: FormField[];
}

// 定义步骤配置类型
interface FormStep {
  id: string;
  title: string;
  type?: string;
  options?: FormOption[];
  units?: { value: string; label: string }[];
  placeholder?: string;
  description?: string;
  sections?: FormSection[];
  fields?: FormField[];
}

// 步骤配置
const formSteps: FormStep[] = [
  {
    id: 'gender',
    title: '您的性别是？',
    options: [
      { value: 'male', label: '男性', icon: '👨' },
      { value: 'female', label: '女性', icon: '👩' },
      { value: 'other', label: '其他/不便透露', icon: '🧑' },
    ]
  },
  {
    id: 'birthDate',
    title: '您的出生日期是？',
    type: 'birthDate',
    description: '我们将根据您的出生日期计算年龄'
  },
  {
    id: 'height',
    title: '您的身高是？',
    type: 'input',
    placeholder: '请输入身高',
    units: [
      { value: 'cm', label: '厘米 (cm)' },
      { value: 'inch', label: '英寸 (inch)' },
    ]
  },
  {
    id: 'weight',
    title: '您的体重是？',
    type: 'input',
    placeholder: '请输入体重',
    units: [
      { value: 'kg', label: '千克 (kg)' },
      { value: 'lb', label: '磅 (lb)' },
    ]
  },
  {
    id: 'knowsBodyFat',
    title: '您是否知道自己的体脂率？',
    options: [
      { value: 'yes', label: '是的，我知道', icon: '✅' },
      { value: 'no', label: '不，我不清楚', icon: '❓' },
    ]
  },
  {
    id: 'weightChangeHistory',
    title: '您的过往体重变化情况是？',
    options: [
      { value: 'stable', label: '体重一直比较稳定', icon: '⚖️' },
      { value: 'increasing', label: '近期体重有所上升', icon: '📈' },
      { value: 'fluctuating', label: '体重波动较大', icon: '📊' },
    ]
  },
  {
    id: 'weightLossGoal',
    title: '您期望减少多少体重或体脂率？',
    type: 'input',
    placeholder: '请输入目标数值',
    units: [
      { value: 'kg', label: '千克 (kg)' },
      { value: 'lb', label: '磅 (lb)' },
      { value: 'bodyFat', label: '体脂率 (%)' },
    ],
    description: '设定一个合理的目标有助于我们为您制定更科学的减重计划'
  },
  {
    id: 'goalTimeframe',
    title: '您期望在多长时间内达成目标？',
    type: 'timeframe',
    placeholder: '请选择时间范围（1-12个月）',
    description: '根据健康瘦身原则，合理的减重速度对维持效果至关重要'
  },
  {
    id: 'hasDietHistory',
    title: '您是否有过盲目节食经历？',
    options: [
      { value: 'yes', label: '是的，有过', icon: '🍽️' },
      { value: 'no', label: '没有', icon: '🙅‍♂️' },
    ]
  },
  {
    id: 'dietReactions',
    title: '节食后您有哪些身体反应？',
    type: 'multiSelect',
    options: [
      { value: 'rebound', label: '体重反弹', icon: '⬆️' },
      { value: 'fatigue', label: '精神不振', icon: '😴' },
      { value: 'hunger', label: '持续饥饿感', icon: '🍽️' },
      { value: 'mood', label: '情绪波动', icon: '😢' },
      { value: 'metabolism', label: '代谢变慢', icon: '🐢' },
      { value: 'none', label: '没有明显反应', icon: '👌' },
    ],
    description: '了解您的节食反应有助于我们避免类似问题'
  },
  // 饮食偏好 - 喜欢的食材
  {
    id: 'favoriteFoods',
    title: '您喜欢哪些食材？',
    type: 'multiSelect',
    options: [
      { value: 'meat', label: '肉类（牛肉、猪肉等）', icon: '🥩' },
      { value: 'poultry', label: '禽类（鸡肉、鸭肉等）', icon: '🍗' },
      { value: 'fish', label: '鱼类', icon: '🐟' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'dairy', label: '奶制品', icon: '🧀' },
      { value: 'vegetables', label: '蔬菜', icon: '🥦' },
      { value: 'fruits', label: '水果', icon: '🍎' },
      { value: 'grains', label: '谷物（米饭、面食等）', icon: '🍚' },
      { value: 'beans', label: '豆类', icon: '🫘' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
    ],
    description: '了解您喜欢的食材有助于我们为您定制更符合口味的饮食计划'
  },
  // 饮食偏好 - 不喜欢的食材
  {
    id: 'dislikedFoods',
    title: '您不喜欢哪些食材？',
    type: 'multiSelect',
    options: [
      { value: 'meat', label: '肉类（牛肉、猪肉等）', icon: '🥩' },
      { value: 'poultry', label: '禽类（鸡肉、鸭肉等）', icon: '🍗' },
      { value: 'fish', label: '鱼类', icon: '🐟' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'dairy', label: '奶制品', icon: '🧀' },
      { value: 'vegetables', label: '蔬菜', icon: '🥦' },
      { value: 'fruits', label: '水果', icon: '🍎' },
      { value: 'grains', label: '谷物（米饭、面食等）', icon: '🍚' },
      { value: 'beans', label: '豆类', icon: '🫘' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
      { value: 'none', label: '没有特别不喜欢的食材', icon: '✅' },
    ],
    description: '了解您不喜欢的食材有助于我们避免在饮食计划中包含这些食材'
  },
  // 饮食偏好 - 宗教饮食限制
  {
    id: 'religiousDietaryRestrictions',
    title: '您是否有宗教相关的饮食限制？',
    options: [
      { value: 'none', label: '无宗教饮食限制', icon: '✅' },
      { value: 'halal', label: '清真饮食 (伊斯兰教)', icon: '🌙' },
      { value: 'kosher', label: '洁食 (犹太教)', icon: '✡️' },
      { value: 'hindu', label: '印度教饮食限制', icon: '🕉️' },
      { value: 'buddhist', label: '佛教饮食限制', icon: '☸️' },
      { value: 'other', label: '其他宗教饮食限制', icon: '🙏' },
    ],
    description: '了解您的宗教饮食限制有助于我们为您提供符合要求的饮食计划'
  },
  // 饮食偏好 - 个人忌口
  {
    id: 'personalDietaryRestrictions',
    title: '您有哪些个人忌口？',
    type: 'multiSelect',
    options: [
      { value: 'vegetarian', label: '素食', icon: '🥗' },
      { value: 'vegan', label: '纯素', icon: '🌱' },
      { value: 'noBeef', label: '不吃牛肉', icon: '🐄' },
      { value: 'noPork', label: '不吃猪肉', icon: '🐖' },
      { value: 'noSeafood', label: '不吃海鲜', icon: '🦐' },
      { value: 'noSpicy', label: '不吃辛辣食物', icon: '🌶️' },
      { value: 'lowSalt', label: '低盐饮食', icon: '🧂' },
      { value: 'lowSugar', label: '低糖饮食', icon: '🍬' },
      { value: 'other', label: '其他', icon: '❓' },
      { value: 'none', label: '无特殊忌口', icon: '✅' },
    ],
    description: '了解您的个人忌口有助于我们为您提供更符合个人需求的饮食计划'
  },
  // 步骤11：饮食偏好 - 低热量食材接受度
  {
    id: 'lowCalorieAcceptance',
    title: '您对低热量、低脂肪食材的接受度如何？',
    options: [
      { value: 'high', label: '很愿意尝试各种低热量食材', icon: '🥗' },
      { value: 'medium', label: '可以接受部分低热量食材', icon: '🥦' },
      { value: 'low', label: '很难接受大多数低热量食材', icon: '🍔' },
    ],
    description: '了解您的饮食偏好有助于我们为您定制更合适的减重餐单'
  },
  // 步骤12：饮食偏好 - 易发胖食物偏好
  {
    id: 'unhealthyFoodPreference',
    title: '您是否喜欢以下易发胖食物？',
    type: 'multiSelect',
    options: [
      { value: 'sweets', label: '甜食', icon: '🍰' },
      { value: 'fried', label: '油炸食品', icon: '🍟' },
      { value: 'highcarbs', label: '高碳水食物', icon: '🍝' },
      { value: 'alcohol', label: '酒精饮料', icon: '🍷' },
      { value: 'none', label: '都不喜欢', icon: '👌' }
    ],
    description: '了解您的饮食偏好有助于我们制定更针对性的饮食建议'
  },
  // 步骤13：饮食偏好 - 就餐频率
  {
    id: 'eatingOutFrequency',
    title: '您每周在外就餐的次数是？',
    options: [
      { value: '0-1', label: '0-1次', icon: '🏠' },
      { value: '2-3', label: '2-3次', icon: '🍽️' },
      { value: '4-5', label: '4-5次', icon: '🍱' },
      { value: '6+', label: '6次及以上', icon: '🍴' },
    ],
    description: '在外就餐通常会摄入更多热量，了解这一点有助于我们调整您的饮食计划'
  },
  // 步骤14：饮食偏好 - 药膳饮食
  {
    id: 'medicinalDietPlan',
    title: '您是否愿意加入药膳饮食计划？',
    options: [
      { value: 'daily', label: '一日一次', icon: '🌿' },
      { value: 'threedays', label: '三日一次', icon: '🍵' },
      { value: 'weekly', label: '一周一次', icon: '🌱' },
      { value: 'monthly', label: '一月一次', icon: '🍲' },
      { value: 'none', label: '不愿意', icon: '❌' },
    ],
    description: '药膳是将中药材与食材相结合，有助于调理身体机能'
  },
  // 步骤15：饮食习惯 - 三餐规律
  {
    id: 'regularMeals',
    title: '您的三餐是否规律？',
    options: [
      { value: 'regular', label: '规律（每天固定时间进餐）', icon: '🕒' },
      { value: 'irregular', label: '不规律（经常错过或延迟用餐）', icon: '⏱️' },
    ],
    description: '规律的饮食习惯有助于新陈代谢和体重管理'
  },
  // 步骤16：饮食习惯 - 零食习惯
  {
    id: 'snackingHabit',
    title: '您是否有吃零食的习惯？',
    options: [
      { value: 'yes', label: '是', icon: '🍪' },
      { value: 'no', label: '否', icon: '❌' },
    ]
  },
  // 步骤17：饮食习惯 - 零食类型
  {
    id: 'snackTypes',
    title: '您常吃的零食类型是？',
    type: 'multiSelect',
    options: [
      { value: 'chips', label: '薯片/膨化食品', icon: '🍟' },
      { value: 'candy', label: '糖果/巧克力', icon: '🍬' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
      { value: 'fruit', label: '水果', icon: '🍎' },
      { value: 'bakery', label: '面包/糕点', icon: '🥐' },
      { value: 'dairy', label: '奶制品', icon: '🧀' },
      { value: 'other', label: '其他', icon: '🍽️' }
    ],
    description: '仅在有零食习惯时需要回答'
  },
  // 步骤18：饮食习惯 - 用餐速度
  {
    id: 'eatingSpeed',
    title: '您的用餐速度是？',
    options: [
      { value: 'fast', label: '快（10分钟内）', icon: '⚡' },
      { value: 'medium', label: '中等（10-20分钟）', icon: '⏱️' },
      { value: 'slow', label: '慢（20分钟以上）', icon: '🐢' },
    ],
    description: '较慢的用餐速度有助于控制食量和促进消化'
  },
  // 步骤19：健康状况采集 - 食物过敏史
  {
    id: 'foodAllergies',
    title: '您是否有食物过敏史？',
    type: 'multiSelect',
    options: [
      { value: 'dairy', label: '乳制品', icon: '🥛' },
      { value: 'gluten', label: '麸质', icon: '🌾' },
      { value: 'nuts', label: '坚果', icon: '🥜' },
      { value: 'seafood', label: '海鲜', icon: '🦐' },
      { value: 'eggs', label: '鸡蛋', icon: '🥚' },
      { value: 'soy', label: '大豆', icon: '🫘' },
      { value: 'other', label: '其他', icon: '⚠️' },
      { value: 'none', label: '无过敏史', icon: '✅' },
    ],
    description: '了解您的食物过敏情况有助于我们避免在饮食计划中包含可能引起过敏的食材'
  },
  // 步骤20：健康状况采集 - 慢性疾病
  {
    id: 'chronicDiseases',
    title: '您是否患有以下慢性疾病？',
    type: 'multiSelect',
    options: [
      { value: 'hypertension', label: '高血压', icon: '🩸' },
      { value: 'diabetes', label: '糖尿病', icon: '🍬' },
      { value: 'heartDisease', label: '心脏疾病', icon: '❤️' },
      { value: 'thyroid', label: '甲状腺疾病', icon: '🦋' },
      { value: 'liver', label: '肝脏疾病', icon: '🫁' },
      { value: 'kidney', label: '肾脏疾病', icon: '🫘' },
      { value: 'other', label: '其他', icon: '🏥' },
      { value: 'none', label: '无慢性疾病', icon: '✅' },
    ],
    description: '了解您的慢性疾病情况有助于我们制定安全、合适的减重计划'
  },
  // 步骤21：健康状况采集 - 用药情况
  {
    id: 'medications',
    title: '您目前是否正在服用以下药物？',
    type: 'multiSelect',
    options: [
      { value: 'antihypertensive', label: '降压药', icon: '💊' },
      { value: 'antidiabetic', label: '降糖药', icon: '💉' },
      { value: 'steroid', label: '类固醇', icon: '🧪' },
      { value: 'antidepressant', label: '抗抑郁药', icon: '😊' },
      { value: 'thyroid', label: '甲状腺药物', icon: '🦋' },
      { value: 'other', label: '其他长期用药', icon: '💊' },
      { value: 'none', label: '无长期用药', icon: '✅' },
    ],
    description: '某些药物可能会影响体重变化，了解您的用药情况有助于我们调整减重计划'
  },
  // 步骤22：健康状况采集 - 睡眠质量
  {
    id: 'sleepQuality',
    title: '您的睡眠质量如何？',
    options: [
      { value: 'good', label: '良好（睡眠充足，深沉）', icon: '😴' },
      { value: 'fair', label: '一般（睡眠时间不足或质量一般）', icon: '😐' },
      { value: 'poor', label: '较差（经常失眠或睡眠浅）', icon: '😫' },
      { value: 'irregular', label: '不规律（作息时间不固定）', icon: '🔄' },
    ],
    description: '睡眠质量与代谢、饮食习惯和体重管理密切相关'
  },
  // 步骤23：中医体质相关症状 - 身体沉重感
  {
    id: 'bodyHeaviness',
    title: '您是否经常感到身体沉重、容易困倦？',
    options: [
      { value: 'yes', label: '是', icon: '😪' },
      { value: 'no', label: '否', icon: '😊' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '身体沉重、困倦可能与中医湿热体质相关'
  },
  // 步骤24：中医体质相关症状 - 温度敏感性
  {
    id: 'temperatureSensitivity',
    title: '您是否比常人更怕热或怕冷？',
    options: [
      { value: 'heat', label: '怕热明显', icon: '🔥' },
      { value: 'cold', label: '怕冷明显', icon: '❄️' },
      { value: 'normal', label: '无特殊', icon: '🌡️' },
    ],
    description: '温度敏感度可能与中医阴阳平衡状态相关'
  },
  // 步骤25：中医体质相关症状 - 油性皮肤
  {
    id: 'oilySkin',
    title: '您的皮肤是否容易出油、长痘？',
    options: [
      { value: 'yes', label: '是', icon: '💦' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '油性皮肤、易长痘可能与中医湿热体质有关'
  },
  // 步骤26：中医体质相关症状 - 出汗
  {
    id: 'sweating',
    title: '您是否容易出汗，且汗后感觉更累？',
    options: [
      { value: 'yes', label: '是', icon: '💦' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'onlyExercise', label: '仅运动后出汗', icon: '🏃' },
    ],
    description: '容易出汗且汗后乏力可能与中医气虚体质相关'
  },
  // 步骤27：中医体质相关症状 - 腰痛
  {
    id: 'backPain',
    title: '您是否经常感到腰部酸痛、四肢无力？',
    options: [
      { value: 'yes', label: '是', icon: '🤕' },
      { value: 'no', label: '否', icon: '👋' },
      { value: 'sometimes', label: '偶尔', icon: '🤔' },
    ],
    description: '腰部酸痛、四肢无力可能与中医肾虚体质相关'
  },
  // 步骤28：中医体质相关症状 - 食欲
  {
    id: 'appetite',
    title: '您吃饭时是否容易没胃口，或吃一点就饱？',
    options: [
      { value: 'yes', label: '是', icon: '🍽️' },
      { value: 'no', label: '否', icon: '🙅‍♂️' },
      { value: 'normal', label: '正常', icon: '👋' },
    ],
    description: '食欲不振可能与中医脾胃虚弱相关'
  },
  // 步骤29：中医体质相关症状 - 头发状况
  {
    id: 'hairCondition',
    title: '您是否有头发油腻、脱发的情况？',
    options: [
      { value: 'yes', label: '是', icon: '👩‍🦲' },
      { value: 'no', label: '否', icon: '👩‍🦱' },
      { value: 'mild', label: '轻微', icon: '👩‍🦳' },
    ],
    description: '头发油腻、脱发可能与中医肝肾功能失调相关'
  },
  // 步骤30：中医体质相关症状 - 月经规律性（仅女性）
  {
    id: 'menstrualRegularity',
    title: '月经周期是否规律？',
    options: [
      { value: 'yes', label: '是', icon: '👩‍🦰' },
      { value: 'no', label: '否', icon: '👩‍🦰' },
      { value: 'sometimes', label: '偶尔', icon: '👩‍🦰' },
    ],
    description: '月经不规律可能与中医气血失调相关（此问题仅女性需要回答）'
  }
];

// 定义采集阶段
const COLLECTION_PHASES = {
  BODY_DATA: 'bodyData',
  HEALTH_GOALS: 'healthGoals', 
  DIET_PREFERENCES: 'dietPreferences',
  HEALTH_STATUS: 'healthStatus',
  TCM_SYMPTOMS: 'tcmSymptoms'
} as const

type CollectionPhase = typeof COLLECTION_PHASES[keyof typeof COLLECTION_PHASES]

// 身体数据采集步骤（前6步 + 总结步骤）
const bodyDataSteps = formSteps.slice(0, 6)
// 添加引导步骤和总结步骤
const bodyDataStepsWithSummary = [
  {
    id: 'bodyDataIntro',
    title: '身体数据采集',
    type: 'intro'
  },
  ...bodyDataSteps,
  {
    id: 'bodyDataSummary',
    title: '基于您的回答的个人总结',
    type: 'summary'
  }
]

// 健康目标采集步骤（第7-10步）
const healthGoalsStepsOriginal = formSteps.slice(6, 10)
// 添加引导步骤和总结步骤到健康目标采集
const healthGoalsSteps = [
  {
    id: 'healthGoalsIntro',
    title: '健康目标设定',
    type: 'intro'
  },
  ...healthGoalsStepsOriginal,
  {
    id: 'healthGoalsSummary',
    title: '您的健康目标总结',
    type: 'summary'
  }
]

// 饮食偏好采集步骤（第11-22步）
const dietPreferencesStepsOriginal = formSteps.slice(10, 22)
// 添加引导步骤和总结步骤到饮食偏好采集
const dietPreferencesSteps = [
  {
    id: 'dietPreferencesIntro',
    title: '饮食偏好了解',
    type: 'intro'
  },
  ...dietPreferencesStepsOriginal,
  {
    id: 'dietPreferencesSummary',
    title: '您的饮食偏好总结',
    type: 'summary'
  }
]

// 健康状况采集步骤（食物过敏史、慢性疾病、用药情况、睡眠质量）
const healthStatusStepsOriginal = formSteps.filter(step => 
  step.id === 'foodAllergies' || 
  step.id === 'chronicDiseases' || 
  step.id === 'medications' || 
  step.id === 'sleepQuality'
)
// 添加引导步骤和总结步骤到健康状况采集
const healthStatusSteps = [
  {
    id: 'healthStatusIntro',
    title: '健康状况了解',
    type: 'intro'
  },
  ...healthStatusStepsOriginal,
  {
    id: 'healthStatusSummary',
    title: '您的健康状况总结',
    type: 'summary'
  }
]

// 中医体质相关症状采集步骤（步骤23-30：8个中医体质症状）
const tcmSymptomsStepsOriginal = formSteps.filter(step => 
  step.id === 'bodyHeaviness' || 
  step.id === 'temperatureSensitivity' || 
  step.id === 'oilySkin' ||
  step.id === 'sweating' ||
  step.id === 'backPain' ||
  step.id === 'appetite' ||
  step.id === 'hairCondition' ||
  step.id === 'menstrualRegularity'
)
// 添加引导步骤和总结步骤到中医体质相关症状采集
const tcmSymptomsSteps = [
  {
    id: 'tcmSymptomsIntro',
    title: '中医体质相关症状了解',
    type: 'intro'
  },
  ...tcmSymptomsStepsOriginal,
  {
    id: 'tcmSymptomsSummary',
    title: '您的中医体质相关症状总结',
    type: 'summary'
  }
]

export default function WeightLossForm({ lang, onBack }: WeightLossFormProps) {
  const [currentPhase, setCurrentPhase] = useState<CollectionPhase>(COLLECTION_PHASES.BODY_DATA)
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  const router = useRouter()

  // 生成用户数据摘要
  const generateUserDataSummary = (formData: any) => {
    // 计算BMI
    const bmi = formData.height && formData.weight 
      ? (parseFloat(formData.weight) / Math.pow(parseFloat(formData.height) / 100, 2)).toFixed(1)
      : '未知'

    // 计算年龄
    const calculateAge = (birthDate: string) => {
      if (!birthDate) return '未提供'
      const birth = new Date(birthDate)
      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age.toString()
    }

    // 获取用户选择的群体并映射到中文名称
    const selectedGroup = typeof window !== 'undefined' ? localStorage.getItem('selected_audience_group') : null
    const getGroupIdentity = (groupId: string | null) => {
      switch(groupId) {
        case 'weight-loss':
          return '希望健康瘦身人群'
        case 'fitness':
          return '健身爱好者'
        case 'white-collar':
          return '都市白领'
        default:
          return '希望健康瘦身人群' // 默认值
      }
    }

    // 格式化体脂率信息
    const formatBodyFat = () => {
      if (formData.knowsBodyFat === 'yes' && formData.bodyFatPercentage) {
        return `${formData.bodyFatPercentage}%`
      }
      return '不清楚'
    }

    // 格式化过往体重变化
    const formatWeightHistory = (history: string) => {
      const historyMap: {[key: string]: string} = {
        'stable': '体重保持稳定',
        'increasing': '体重逐渐增加',
        'decreasing': '体重逐渐减少',
        'fluctuating': '体重经常波动'
      }
      return historyMap[history] || '未提供'
    }

    // 格式化节食后身体反应
    const formatDietReactions = (reactions: string[]) => {
      if (!reactions || reactions.length === 0) return '未提供'
      const reactionMap: {[key: string]: string} = {
        'none': '没有明显反应',
        'fatigue': '容易疲劳',
        'dizziness': '头晕',
        'mood': '情绪波动',
        'sleep': '睡眠质量下降',
        'constipation': '便秘',
        'hairLoss': '脱发',
        'skinProblems': '皮肤问题'
      }
      return reactions.map(r => reactionMap[r] || r).join('、')
    }

    // 格式化零食类型
    const formatSnackTypes = (snacks: string[]) => {
      if (!snacks || snacks.length === 0) return '无特殊偏好'
      const snackMap: {[key: string]: string} = {
        'chips': '薯片类',
        'sweets': '甜食类',
        'nuts': '坚果类',
        'fruits': '水果类',
        'none': '不吃零食'
      }
      return snacks.map(s => snackMap[s] || s).join('、')
    }

    // 格式化用餐速度
    const formatEatingSpeed = (speed: string) => {
      const speedMap: {[key: string]: string} = {
        'slow': '用餐速度较慢',
        'normal': '用餐速度正常',
        'fast': '用餐速度较快'
      }
      return speedMap[speed] || '未提供'
    }

    // 格式化宗教饮食限制
    const formatReligiousDietaryRestrictions = (restrictions: string) => {
      const restrictionMap: {[key: string]: string} = {
        'none': '无宗教饮食限制',
        'halal': '清真饮食',
        'kosher': '犹太洁食',
        'hindu': '印度教饮食',
        'buddhist': '佛教素食'
      }
      return restrictionMap[restrictions] || restrictions || '未提供'
    }

    // 格式化个人饮食限制
    const formatPersonalDietaryRestrictions = (restrictions: string[]) => {
      if (!restrictions || restrictions.length === 0) return '无特殊限制'
      const restrictionMap: {[key: string]: string} = {
        'vegetarian': '素食主义',
        'vegan': '严格素食主义',
        'lowCarb': '低碳水饮食',
        'glutenFree': '无麸质饮食',
        'lowSodium': '低盐饮食'
      }
      return restrictions.map(r => restrictionMap[r] || r).join('、')
    }

    // 格式化接受度
    const formatAcceptance = (acceptance: string) => {
      const acceptanceMap: {[key: string]: string} = {
        'high': '高接受度',
        'medium': '中等接受度',
        'low': '低接受度'
      }
      return acceptanceMap[acceptance] || '未提供'
    }

    // 格式化药膳饮食计划
    const formatMedicinalDietPlan = (plan: string) => {
      const planMap: {[key: string]: string} = {
        'daily': '愿意每日加入药膳',
        'weekly': '愿意每周加入药膳',
        'monthly': '愿意每月加入药膳',
        'none': '不愿意加入药膳'
      }
      return planMap[plan] || '未提供'
    }

    // 数据清理函数 - 移除字段中的错误选项
    const cleanHealthData = () => {
      // 定义各字段的有效选项
      const validFoodAllergies = ['dairy', 'gluten', 'nuts', 'seafood', 'eggs', 'soy', 'other', 'none']
      const validChronicDiseases = ['hypertension', 'diabetes', 'heartDisease', 'thyroid', 'liver', 'kidney', 'other', 'none']
      const validMedications = ['antihypertensive', 'antidiabetic', 'steroid', 'antidepressant', 'thyroid', 'other', 'none']

      // 清理数据
      const cleanedFoodAllergies = formData.foodAllergies?.filter((item: string) => validFoodAllergies.includes(item)) || []
      const cleanedChronicDiseases = formData.chronicDiseases?.filter((item: string) => validChronicDiseases.includes(item)) || []
      const cleanedMedications = formData.medications?.filter((item: string) => validMedications.includes(item)) || []

      return {
        foodAllergies: cleanedFoodAllergies,
        chronicDiseases: cleanedChronicDiseases,
        medications: cleanedMedications
      }
    }

    // 格式化健康状况字段
    const formatHealthConditions = () => {
      const cleaned = cleanHealthData()
      
      const allergyMap: {[key: string]: string} = {
        'dairy': '乳制品',
        'gluten': '麸质', 
        'nuts': '坚果',
        'seafood': '海鲜',
        'eggs': '鸡蛋',
        'soy': '大豆',
        'other': '其他',
        'none': '无过敏史'
      }

      const diseaseMap: {[key: string]: string} = {
        'hypertension': '高血压',
        'diabetes': '糖尿病',
        'heartDisease': '心脏疾病',
        'thyroid': '甲状腺疾病',
        'liver': '肝脏疾病',
        'kidney': '肾脏疾病',
        'other': '其他',
        'none': '无慢性疾病'
      }

      const medicationMap: {[key: string]: string} = {
        'antihypertensive': '降压药',
        'antidiabetic': '降糖药',
        'steroid': '类固醇',
        'antidepressant': '抗抑郁药',
        'thyroid': '甲状腺药物',
        'other': '其他长期用药',
        'none': '无长期用药'
      }

      const formatArray = (arr: string[], map: {[key: string]: string}) => {
        return arr.length === 0 ? '无' : arr.map(item => map[item] || item).join('、')
      }

      return {
        allergies: formatArray(cleaned.foodAllergies, allergyMap),
        diseases: formatArray(cleaned.chronicDiseases, diseaseMap),
        medications: formatArray(cleaned.medications, medicationMap)
      }
    }

    return `
基础数据信息：
- 身份：${getGroupIdentity(selectedGroup)}
- 性别：${formData.gender === 'male' ? '男' : '女'}
- 年龄：${calculateAge(formData.birthDate)}岁
- 身高：${formData.height || '未提供'}${formData.heightUnit || 'cm'}
- 体重：${formData.weight || '未提供'}${formData.weightUnit || 'kg'}
- BMI：${bmi}
- 体脂率：${formatBodyFat()}

体重管理信息：
- 过往体重变化情况：${formatWeightHistory(formData.weightChangeHistory)}
- 节食后身体反应：${formatDietReactions(formData.dietReactions)}
- 减重目标：${formData.weightLossGoal || '未设定'}${formData.weightLossGoalUnit || 'kg'}
- 期望时间：${formData.goalTimeframe || '未设定'}个月

饮食习惯：
- 常吃零食类型：${formatSnackTypes(formData.snackTypes)}
- 用餐速度：${formatEatingSpeed(formData.eatingSpeed)}
- 喜欢的食材：${formData.favoriteFoods?.join('、') || '未提供'}
- 不喜欢的食材：${formData.dislikedFoods?.join('、') || '无特别忌口'}
- 宗教饮食限制：${formatReligiousDietaryRestrictions(formData.religiousDietaryRestrictions)}
- 个人饮食限制（忌口）：${formatPersonalDietaryRestrictions(formData.personalDietaryRestrictions)}
- 对低热量、低脂肪食材的接受度：${formatAcceptance(formData.lowCalorieAcceptance)}
- 易发胖食物偏好：${formData.unhealthyFoodPreference?.join('、') || '无特殊偏好'}
- 是否加入药膳饮食计划：${formatMedicinalDietPlan(formData.medicinalDietPlan)}

健康状况：
- 食物过敏史：${(() => {
  const healthData = formatHealthConditions()
  return healthData.allergies
})()}
- 患有的慢性疾病：${(() => {
  const healthData = formatHealthConditions()
  return healthData.diseases
})()}
- 正在服用的药物：${(() => {
  const healthData = formatHealthConditions()
  return healthData.medications
})()}
- 睡眠质量：${formData.sleepQuality === 'good' ? '良好' : formData.sleepQuality === 'fair' ? '一般' : formData.sleepQuality === 'poor' ? '较差' : formData.sleepQuality === 'irregular' ? '不规律' : '未提供'}

中医体质相关症状：
- 身体沉重感：${formData.bodyHeaviness === 'yes' ? '是，经常感到身体沉重困倦' : formData.bodyHeaviness === 'sometimes' ? '偶尔感到身体沉重' : '否，身体感觉正常'}
- 温度敏感性：${formData.temperatureSensitivity === 'heat' ? '怕热明显' : formData.temperatureSensitivity === 'cold' ? '怕冷明显' : '无特殊温度敏感'}
- 皮肤状况：${formData.oilySkin === 'yes' ? '是，皮肤容易出油长痘' : formData.oilySkin === 'sometimes' ? '偶尔出油长痘' : '否，皮肤状况正常'}
- 出汗情况：${formData.sweating === 'yes' ? '是，容易出汗且汗后感觉疲惫' : formData.sweating === 'onlyExercise' ? '仅运动时出汗' : '否，出汗情况正常'}
- 腰部症状：${formData.backPain === 'yes' ? '是，经常腰酸背痛' : formData.backPain === 'sometimes' ? '偶尔腰酸背痛' : '否，腰部无明显症状'}
- 食欲状况：${formData.appetite === 'yes' ? '是，容易没胃口或吃一点就饱' : formData.appetite === 'normal' ? '食欲正常' : '否，食欲良好'}
- 头发状况：${formData.hairCondition === 'yes' ? '是，头发油腻容易脱发' : formData.hairCondition === 'mild' ? '轻微油腻或脱发' : '否，头发状况正常'}
${formData.gender === 'female' ? `- 月经规律性：${formData.menstrualRegularity === 'yes' ? '是，月经周期规律' : '否，月经周期不规律'}` : ''}
    `.trim()
  }

  // 生成健康评估报告
  const generateHealthReport = async () => {
    setIsGeneratingReport(true)

    try {
      const formData = form.getValues()
      const userDataSummary = generateUserDataSummary(formData)
      
      // 获取选择的群体信息
      const selectedGroup = typeof window !== 'undefined' ? localStorage.getItem('selected_audience_group') : null
      
      const response = await fetch('/api/generate-health-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userData: userDataSummary,
          formData: {
            ...formData,
            selectedGroup: selectedGroup
          }
        }),
      })

      if (!response.ok) {
        throw new Error('生成报告失败，请稍后重试')
      }

      const data = await response.json()
      
      // 健康评估完成后，标记用户已完成信息填写，避免再次弹出对话框
      if (typeof window !== 'undefined') {
        localStorage.setItem('profile_completed', 'true')
      }

      // 重定向到主页，添加时间戳参数强制刷新
      router.push(`/?refresh=${Date.now()}`)
      
    } catch (error) {
      console.error('生成健康报告时出错:', error)
      alert(error instanceof Error ? error.message : '生成报告时发生未知错误')
    } finally {
      setIsGeneratingReport(false)
    }
  }

  const [currentStep, setCurrentStep] = useState(0)
  
  // 获取当前阶段的步骤数
  const getCurrentPhaseSteps = () => {
    switch(currentPhase) {
      case COLLECTION_PHASES.BODY_DATA:
        return bodyDataStepsWithSummary
      case COLLECTION_PHASES.HEALTH_GOALS:
        return healthGoalsSteps
      case COLLECTION_PHASES.DIET_PREFERENCES:
        return dietPreferencesSteps
      case COLLECTION_PHASES.HEALTH_STATUS:
        return healthStatusSteps
      case COLLECTION_PHASES.TCM_SYMPTOMS:
        return tcmSymptomsSteps
      default:
        return formSteps
    }
  }
  
  const currentPhaseSteps = getCurrentPhaseSteps()
  const totalSteps = currentPhaseSteps.length
  const [calculatedBMI, setCalculatedBMI] = useState<number | null>(null)
  const [bmiCategory, setBMICategory] = useState<string>('')
  const [heightUnit, setHeightUnit] = useState('cm')
  const [weightUnit, setWeightUnit] = useState('kg')
  const [selectedGender, setSelectedGender] = useState<string | null>(null)
  const [knowsBodyFatSelection, setKnowsBodyFatSelection] = useState<string | null>(null)
  const [selectedWeightLossUnit, setSelectedWeightLossUnit] = useState<string | null>(null)
  const [selectedDietReactions, setSelectedDietReactions] = useState<string[]>(['none']) // 默认为"没有明显反应"
  const [selectedFavoriteFoods, setSelectedFavoriteFoods] = useState<string[]>([])
  const [selectedDislikedFoods, setSelectedDislikedFoods] = useState<string[]>([])
  const [selectedReligiousDietaryRestriction, setSelectedReligiousDietaryRestriction] = useState<string | null>(null)
  const [selectedPersonalDietaryRestrictions, setSelectedPersonalDietaryRestrictions] = useState<string[]>([])
  
  // 添加本地状态来存储输入值，避免跨字段影响
  const [inputValues, setInputValues] = useState({
    height: '170',
    weight: '65',
    weightLossGoal: '5',
    goalTimeframe: '3',
    bodyFatPercentage: ''
  });
  
  // 初始化表单
  const form = useForm<WeightLossFormValues>({
    resolver: zodResolver(weightLossFormSchema),
    defaultValues: {
      heightUnit: 'cm',
      weightUnit: 'kg',
      knowsBodyFat: 'no',
      gender: 'male',
      birthDate: new Date(new Date().setFullYear(new Date().getFullYear() - 30)),
      height: inputValues.height,
      weight: inputValues.weight,
      weightChangeHistory: 'stable',
      weightLossGoal: inputValues.weightLossGoal,
      weightLossGoalUnit: 'kg',
      goalTimeframe: inputValues.goalTimeframe,
      hasDietHistory: 'no',
      // 健康状况字段明确初始化为空数组，防止数据污染
      foodAllergies: [],
      chronicDiseases: [],
      medications: [],
      dietReactions: ['none'], // 默认设置为"没有明显反应"
      favoriteFoods: [],
      dislikedFoods: [],
      religiousDietaryRestrictions: 'none',
      personalDietaryRestrictions: [],
      dietaryRestrictions: [],
      lowCalorieAcceptance: 'medium',
      unhealthyFoodPreference: [],
      eatingOutFrequency: '2-3',
      medicinalDietPlan: 'none',
      regularMeals: 'regular',
      snackingHabit: 'no',
      snackTypes: [],
      eatingSpeed: 'medium',
      sleepQuality: 'fair',
      bodyHeaviness: 'no',
      temperatureSensitivity: 'normal',
      oilySkin: 'no',
      sweating: 'no',
      backPain: 'no',
      appetite: 'normal',
      hairCondition: 'no',
      menstrualRegularity: 'yes',
    },
  })
  
  // 监听单位变化
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.heightUnit) setHeightUnit(value.heightUnit);
      if (value.weightUnit) setWeightUnit(value.weightUnit);
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);
  
  // 监听表单值变化，计算BMI
  useEffect(() => {
    // 确保有身高和体重值时计算BMI
    if (inputValues.height && inputValues.weight) {
      const heightUnit = form.getValues('heightUnit');
      const weightUnit = form.getValues('weightUnit');
      if (heightUnit && weightUnit) {
        calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
      }
    }
  }, [inputValues.height, inputValues.weight]);
  
  // 监听表单单位变化
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if ((name === 'heightUnit' || name === 'weightUnit') 
          && inputValues.height && inputValues.weight) {
        const heightUnit = name === 'heightUnit' ? value.heightUnit : form.getValues('heightUnit');
        const weightUnit = name === 'weightUnit' ? value.weightUnit : form.getValues('weightUnit');
        if (heightUnit && weightUnit) {
          calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, inputValues.height, inputValues.weight]);
  
  // 监听体脂率输入值变化，自动清除错误
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      // 当体脂率字段变化且有值时
      if (name === 'bodyFatPercentage' && value.bodyFatPercentage) {
        const bodyFatValue = value.bodyFatPercentage;
        // 如果输入了有效的体脂率数值，清除错误
        const numericValue = parseFloat(bodyFatValue);
        if (!isNaN(numericValue) && numericValue > 0 && numericValue <= 100) {
          if (form.formState.errors.bodyFatPercentage) {
            form.clearErrors('bodyFatPercentage');
          }
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);
  
  // 设置步骤10（节食反应）的默认值
  useEffect(() => {
    // 在组件加载后立即设置默认值
    if (currentStep === 9) { // 节食反应是步骤10，索引为9
      // 确保默认选中"没有明显反应"
      form.setValue('dietReactions', ['none'], {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true
      });
      setSelectedDietReactions(['none']);
    }
  }, [currentStep, form]);
  
  // 计算BMI
  const calculateBMI = (height: string, heightUnit: string, weight: string, weightUnit: string) => {
    try {
      let heightInMeters: number
      let weightInKg: number
      
      // 转换身高为米
      if (heightUnit === 'cm') {
        heightInMeters = parseFloat(height) / 100
      } else {
        heightInMeters = parseFloat(height) * 0.0254 // 英寸转米
      }
      
      // 转换体重为千克
      if (weightUnit === 'kg') {
        weightInKg = parseFloat(weight)
      } else {
        weightInKg = parseFloat(weight) * 0.453592 // 磅转千克
      }
      
      if (heightInMeters <= 0 || weightInKg <= 0) {
        setCalculatedBMI(null)
        setBMICategory('')
        return
      }
      
      // 计算BMI: 体重(kg) / 身高(m)的平方
      const bmi = weightInKg / (heightInMeters * heightInMeters)
      setCalculatedBMI(parseFloat(bmi.toFixed(1)))
      
      // 确定BMI类别
      if (bmi < 18.5) {
        setBMICategory('体重过轻')
      } else if (bmi >= 18.5 && bmi < 24) {
        setBMICategory('正常范围')
      } else if (bmi >= 24 && bmi < 28) {
        setBMICategory('超重')
      } else {
        setBMICategory('肥胖')
      }
    } catch (error) {
      setCalculatedBMI(null)
      setBMICategory('')
    }
  }
  
  // 处理下一步
  const handleNext = () => {
    const currentStepId = currentPhaseSteps[currentStep].id
    
    // 对于体脂率特殊处理
    if (currentStepId === 'knowsBodyFat' && form.getValues('knowsBodyFat') === 'yes') {
      // 如果用户知道体脂率，需要先填写体脂率后才能进入下一步
      const bodyFatValue = form.getValues('bodyFatPercentage');
      // 检查输入值是否存在且不为空字符串
      if (!bodyFatValue || bodyFatValue.trim() === '') {
        form.setError('bodyFatPercentage', {
          type: 'manual',
          message: '请输入您的体脂率'
        });
        return;
      }
      
      // 检查输入值是否为有效数字
      const numericValue = parseFloat(bodyFatValue);
      if (isNaN(numericValue) || numericValue <= 0 || numericValue > 100) {
        form.setError('bodyFatPercentage', {
          type: 'manual',
          message: '请输入有效的体脂率数值（1-100之间）'
        });
        return;
      }
    }
    
    // 对于减重目标特殊处理
    if (currentStepId === 'weightLossGoal') {
      if (!form.getValues('weightLossGoal')) {
        form.setError('weightLossGoal', {
          type: 'manual',
          message: '请输入您的减重目标数值'
        });
        return;
      }
    }
    
    // 对于节食反应特殊处理
    if (currentStepId === 'dietReactions' && form.getValues('hasDietHistory') === 'yes') {
      const dietReactions = form.getValues('dietReactions');
      if (!dietReactions || dietReactions.length === 0) {
        form.setError('dietReactions', {
          type: 'manual',
          message: '请至少选择一项节食后的身体反应'
        });
        return;
      }
    }
    
    // 如果用户没有节食经历，直接跳过节食反应步骤
    if (currentStepId === 'hasDietHistory' && form.getValues('hasDietHistory') === 'no') {
      // 如果当前是节食经历步骤，且用户选择没有节食经历，则设置默认值并跳过下一步
      form.setValue('dietReactions', ['none']);
      if (currentStep < totalSteps - 2) {
        setCurrentStep(prev => prev + 2); // 跳过节食反应步骤
        return;
      }
    }
    
    // 对于零食习惯特殊处理
    if (currentStepId === 'snackingHabit' && form.getValues('snackingHabit') === 'no') {
      // 如果用户没有零食习惯，直接跳过零食类型步骤
      form.setValue('snackTypes', []);
      if (currentStep < totalSteps - 2) {
        setCurrentStep(prev => prev + 2); // 跳过零食类型步骤
        return;
      }
    }
    
    // 对于零食类型特殊处理
    if (currentStepId === 'snackTypes' && form.getValues('snackingHabit') === 'yes') {
      const snackTypes = form.getValues('snackTypes');
      if (!snackTypes || snackTypes.length === 0) {
        form.setError('snackTypes', {
          type: 'manual',
          message: '请至少选择一种零食类型'
        });
        return;
      }
    }
    
    // 如果当前步骤是hairCondition，且用户性别不是女性，则跳过menstrualRegularity步骤
    if (currentStepId === 'hairCondition' && form.getValues('gender') !== 'female') {
      // 设置默认值并跳过月经规律性问题
      form.setValue('menstrualRegularity', 'yes');
      if (currentStep < totalSteps - 2) {
        setCurrentStep(prev => prev + 2); // 跳过月经规律性步骤
        return;
      }
    }
    
    // 特殊处理引导步骤
    if (currentStepId === 'bodyDataIntro' || currentStepId === 'healthGoalsIntro' || currentStepId === 'dietPreferencesIntro' || currentStepId === 'healthStatusIntro' || currentStepId === 'tcmSymptomsIntro') {
      setCurrentStep(prev => prev + 1);
      return;
    }
    
    // 特殊处理总结步骤
    if (currentStepId === 'bodyDataSummary') {
      // 直接进入健康目标采集阶段
      setCurrentPhase(COLLECTION_PHASES.HEALTH_GOALS);
      setCurrentStep(0);
      return;
    }
    
    if (currentStepId === 'healthGoalsSummary') {
      // 健康目标采集完成，直接进入饮食偏好采集阶段
      setCurrentPhase(COLLECTION_PHASES.DIET_PREFERENCES);
      setCurrentStep(0);
      return;
    }
    
    if (currentStepId === 'dietPreferencesSummary') {
      // 饮食偏好采集完成，直接进入健康状况采集阶段
      setCurrentPhase(COLLECTION_PHASES.HEALTH_STATUS);
      setCurrentStep(0);
      return;
    }
    
    if (currentStepId === 'healthStatusSummary') {
      // 健康状况采集完成，直接进入中医体质相关症状采集阶段
      setCurrentPhase(COLLECTION_PHASES.TCM_SYMPTOMS);
      setCurrentStep(0);
      return;
    }
    
    if (currentStepId === 'tcmSymptomsSummary') {
      // 中医体质相关症状采集完成，生成健康评估报告
      console.log('中医体质相关症状采集完成，生成健康评估报告');
      generateHealthReport();
      return;
    }
    
    // 处理其他步骤
    const stepValue = form.getValues(currentStepId as keyof WeightLossFormValues);
    if (stepValue !== undefined) {
      if (currentStep < totalSteps - 1) {
        setCurrentStep(prev => prev + 1);
      } else {
                // 当前阶段完成
        // 提交表单
        onSubmit(form.getValues());
      }
    } else {
      // 显示必填项提示
      form.trigger(currentStepId as any);
    }
  }
  
  // 处理上一步
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    } else {
            // 当前是各阶段的第一步，需要跨阶段返回
      if (currentPhase === COLLECTION_PHASES.HEALTH_GOALS) {
        // 从健康目标采集阶段返回到身体数据采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.BODY_DATA);
        setCurrentStep(bodyDataStepsWithSummary.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        // 从饮食偏好采集阶段返回到健康目标采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.HEALTH_GOALS);
        setCurrentStep(healthGoalsSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        // 从健康状况采集阶段返回到饮食偏好采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.DIET_PREFERENCES);
        setCurrentStep(dietPreferencesSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        // 从中医体质相关症状采集阶段返回到健康状况采集阶段的总结页面
        setCurrentPhase(COLLECTION_PHASES.HEALTH_STATUS);
        setCurrentStep(healthStatusSteps.length - 1); // 返回到总结页面
      } else if (currentPhase === COLLECTION_PHASES.BODY_DATA && onBack) {
        // 从身体数据采集阶段返回到群体选择组件
      onBack()
      }
    }
  }
  
  // 提交表单
  function onSubmit(data: WeightLossFormValues) {
    console.log('表单数据:', data)
    // 这里添加提交表单的逻辑
    // 例如保存到后端、本地存储或进入下一步
  }

  // 如果正在生成报告，显示加载状态
  if (isGeneratingReport) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
        <Card className="w-full max-w-md">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              正在生成您的健康评估报告
            </h3>
            <p className="text-gray-600 mb-4">
              我们的中医专家正在根据您的信息制定个性化健康方案...
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
            <p className="text-sm text-gray-500 mt-2">预计需要 30-60 秒</p>
          </div>
        </Card>
      </div>
    )
  }
  
  // 渲染当前步骤
  const renderCurrentStep = () => {
    const currentStepConfig = currentPhaseSteps[currentStep]
    
    // 如果是引导步骤，渲染引导内容
    if (currentStepConfig.type === 'intro') {
      // 根据当前阶段显示不同的引导内容
      if (currentPhase === COLLECTION_PHASES.BODY_DATA) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-blue-100 to-green-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-green-500 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-blue-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">📏</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-orange-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                让我们开始收集您的身体数据！
              </h2>
              
              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                为了制定个性化的健康瘦身计划，我们需要了解您的基本身体信息。这包括您的身高、体重、年龄等基础数据。
              </p>
              
              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  💡 提示：请如实填写，这些信息将帮助我们为您计算BMI并制定科学的减重目标。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_GOALS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-yellow-100 to-orange-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-red-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-red-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🎯</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-yellow-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                设定您的健康目标！
              </h2>
              
              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                现在让我们设定合理的减重目标和时间规划。科学的目标设定是成功减重的关键。
              </p>
              
              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  🎯 提示：合理的减重速度是每周0.5-1公斤，这样有助于长期维持效果。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-green-400 to-blue-500 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-green-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🍽️</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-blue-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                现在了解您的饮食偏好！
              </h2>
              
              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                接下来我们将了解您的饮食偏好和习惯，为您定制专属的健康餐单。
              </p>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-red-100 to-pink-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-red-400 to-pink-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-pink-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🏥</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-red-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                了解您的健康状况！
              </h2>
              
              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                现在我们需要了解您的健康状况，包括食物过敏史、慢性疾病、用药情况和睡眠质量，这将帮助我们制定更安全、有效的减重计划。
              </p>
              
              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  🏥 提示：了解您的健康状况有助于我们制定安全、个性化的减重方案。
                </p>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-8 min-h-[500px] flex flex-col justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full mx-auto mb-8 flex items-center justify-center relative overflow-hidden">
                <div className="w-24 h-24 bg-purple-400 rounded-full absolute top-4 left-4 flex items-center justify-center">
                  <span className="text-4xl">🌿</span>
                </div>
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-8 bg-pink-500 rounded-sm transform rotate-12"></div>
                </div>
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                了解您的中医体质相关症状！
              </h2>
              
              <p className="text-gray-700 text-lg leading-relaxed mb-8 px-4">
                接下来我们将了解您的中医体质相关症状，包括身体感受、皮肤状况、温度敏感性等，以便为您提供更全面的健康评估和中医体质分析。
              </p>
              
              <div className="bg-white bg-opacity-70 rounded-lg p-4 mx-4 mb-6">
                <p className="text-sm text-gray-600">
                  🌿 提示：中医体质症状有助于我们了解您的身体状态，制定更个性化的健康建议。
                </p>
              </div>
            </div>
          </div>
        );
      }
    }
    
    // 如果是总结步骤，渲染总结内容
    if (currentStepConfig.type === 'summary') {
      // 根据当前阶段显示不同的总结内容
      if (currentPhase === COLLECTION_PHASES.BODY_DATA) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">📊</span>
              </div>
            </div>
          
                     <div className="space-y-6 text-left">
             <div className="bg-white rounded-lg p-6 shadow-sm border">
               <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center gap-2">
                 <span>📏</span> 身体质量指数 (BMI)
               </h3>
               <div className="flex items-center justify-between">
                 <span className="text-3xl font-bold text-orange-500">
                   {calculatedBMI || '计算中...'}
                 </span>
                 <span className="text-orange-500 font-medium">
                   {bmiCategory}
                 </span>
               </div>
               <p className="text-sm text-gray-600 mt-2">
                 BMI升高会增加慢性病的风险。
               </p>
             </div>
             
             <div className="flex gap-6">
               {/* 左侧数据列表 */}
               <div className="flex-1 space-y-4">
                 <div className="flex items-center gap-3">
                   <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold">👤</span>
                   </div>
                   <div>
                      <span className="font-medium">性别</span>
                     <p className="text-sm text-gray-600">
                       {(() => {
                         const gender = form.getValues('gender');
                         switch(gender) {
                           case 'male': return '男性';
                           case 'female': return '女性';
                           case 'other': return '其他/不便透露';
                           default: return '未选择';
                         }
                       })()}
                     </p>
                   </div>
                 </div>
                 
                 <div className="flex items-center gap-3">
                   <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold">📅</span>
                   </div>
                   <div>
                      <span className="font-medium">年龄</span>
                     <p className="text-sm text-gray-600">
                       {(() => {
                         const birthDate = form.getValues('birthDate');
                         if (birthDate) {
                           const age = new Date().getFullYear() - birthDate.getFullYear();
                            return `${age} 岁`;
                         }
                          return '未填写';
                       })()}
                     </p>
                   </div>
                  </div>
                 
                 <div className="flex items-center gap-3">
                   <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold">📏</span>
                   </div>
                   <div>
                      <span className="font-medium">身高</span>
                     <p className="text-sm text-gray-600">
                       {(() => {
                          const height = form.getValues('height');
                          const heightUnit = form.getValues('heightUnit');
                          return `${height} ${heightUnit === 'cm' ? '厘米' : '英寸'}`;
                        })()}
                      </p>
                    </div>
                  </div>
                 
                 <div className="flex items-center gap-3">
                   <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold">⚖️</span>
                   </div>
                   <div>
                      <span className="font-medium">体重</span>
                     <p className="text-sm text-gray-600">
                       {(() => {
                          const weight = form.getValues('weight');
                          const weightUnit = form.getValues('weightUnit');
                          return `${weight} ${weightUnit === 'kg' ? '公斤' : '磅'}`;
                        })()}
                      </p>
                    </div>
                  </div>
                 
                 <div className="flex items-center gap-3">
                   <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold">📊</span>
                   </div>
                   <div>
                      <span className="font-medium">体脂率</span>
                     <p className="text-sm text-gray-600">
                       {(() => {
                         const knowsBodyFat = form.getValues('knowsBodyFat');
                         const bodyFatPercentage = form.getValues('bodyFatPercentage');
                         
                         if (knowsBodyFat === 'yes' && bodyFatPercentage) {
                           return `${bodyFatPercentage}%`;
                         } else if (knowsBodyFat === 'no') {
                            return '不清楚';
                          }
                          return '未填写';
                       })()}
                      </p>
                    </div>
                  </div>
                 
                 <div className="flex items-center gap-3">
                   <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold">📈</span>
                   </div>
                   <div>
                      <span className="font-medium">体重变化情况</span>
                     <p className="text-sm text-gray-600">
                       {(() => {
                          const weightHistory = form.getValues('weightChangeHistory');
                          switch(weightHistory) {
                            case 'stable': return '体重一直比较稳定';
                            case 'increasing': return '近期体重有所上升';
                            case 'fluctuating': return '体重波动较大';
                            default: return '未选择';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
               </div>
               
               {/* 右侧体型示意图 */}
               <div className="w-40 flex-shrink-0">
                  <div className="w-full h-64 rounded-lg bg-gradient-to-b from-blue-100 to-blue-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-6xl mb-2">
                        {(() => {
                          const gender = form.getValues('gender');
                          const bmi = calculatedBMI;
                          
                          if (gender === 'male') {
                            if (bmi && bmi >= 28) return '🤰'; // 肥胖
                            if (bmi && bmi >= 24) return '🧔'; // 超重
                            return '🧑'; // 正常
                          } else {
                            if (bmi && bmi >= 28) return '🤱'; // 肥胖  
                            if (bmi && bmi >= 24) return '👩'; // 超重
                            return '👩‍🦱'; // 正常
                          }
                        })()}
                      </div>
                      <p className="text-xs text-gray-600">
                        {(() => {
                          if (calculatedBMI) {
                            if (calculatedBMI >= 28) return '目标：健康减重';
                            if (calculatedBMI >= 24) return '目标：适度减重';
                            if (calculatedBMI < 18.5) return '目标：健康增重';
                            return '目标：维持体重';
                          }
                          return '健康体型示意';
                        })()}
                      </p>
                    </div>
                  </div>
                 </div>
             </div>
           </div>
        </div>
      );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_GOALS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-red-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🎯</span>
              </div>
            </div>
            
            <div className="space-y-6 text-left">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center gap-2">
                  <span>🎯</span> 您的健康目标
                </h3>
                <div className="flex items-center justify-between">
                  <span className="text-3xl font-bold text-orange-500">
                    {form.getValues('weightLossGoal')} {(() => {
                      const unit = form.getValues('weightLossGoalUnit');
                      switch(unit) {
                        case 'kg': return '公斤';
                        case 'lb': return '磅';
                        case 'bodyFat': return '% 体脂率';
                        default: return '公斤';
                      }
                    })()}
                  </span>
                  <span className="text-orange-500 font-medium">
                    {form.getValues('goalTimeframe')} 个月内达成
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  合理的减重目标有助于长期维持效果。
                </p>
              </div>
              
              <div className="flex gap-6">
                {/* 左侧数据列表 */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                       <span className="text-orange-600 font-bold">⚖️</span>
                    </div>
                    <div>
                       <span className="font-medium">减重目标</span>
                      <p className="text-sm text-gray-600">
                        {form.getValues('weightLossGoal')} {(() => {
                          const unit = form.getValues('weightLossGoalUnit');
                          switch(unit) {
                            case 'kg': return '公斤';
                            case 'lb': return '磅';
                            case 'bodyFat': return '% 体脂率';
                            default: return '公斤';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                       <span className="text-orange-600 font-bold">⏰</span>
                    </div>
                    <div>
                       <span className="font-medium">达成时间</span>
                      <p className="text-sm text-gray-600">
                        {form.getValues('goalTimeframe')} 个月
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                       <span className="text-orange-600 font-bold">🍽️</span>
                    </div>
                    <div>
                       <span className="font-medium">节食经历</span>
                      <p className="text-sm text-gray-600">
                        {form.getValues('hasDietHistory') === 'yes' ? '有过节食经历' : '无节食经历'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                       <span className="text-orange-600 font-bold">📊</span>
                    </div>
                    <div>
                       <span className="font-medium">减重计划</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const goalValue = parseFloat(form.getValues('weightLossGoal') || '0');
                          const timeframe = parseInt(form.getValues('goalTimeframe') || '1');
                          const weeklyTarget = (goalValue / (timeframe * 4)).toFixed(1);
                          return `每周建议减重 ${weeklyTarget} ${form.getValues('weightLossGoalUnit') === 'kg' ? '公斤' : form.getValues('weightLossGoalUnit') === 'lb' ? '磅' : '% 体脂'}`;
                        })()}
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* 右侧目标示意图 */}
                <div className="w-40 flex-shrink-0">
                   <div className="w-full h-64 rounded-lg bg-gradient-to-b from-orange-100 to-red-100 flex items-center justify-center">
                     <div className="text-center">
                       <div className="text-6xl mb-2">🎯</div>
                       <p className="text-xs text-gray-600">
                         健康减重目标
                       </p>
                     </div>
                   </div>
                  </div>
              </div>
            </div>
          </div>
        );

      } else if (currentPhase === COLLECTION_PHASES.DIET_PREFERENCES) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-blue-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🍽️</span>
              </div>
            </div>
            
            <div className="space-y-6 text-left">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center gap-2">
                  <span>🍽️</span> 您的饮食偏好
                </h3>
                <div className="flex items-center justify-between">
                  <span className="text-3xl font-bold text-green-500">
                    个性化餐单
                  </span>
                  <span className="text-green-500 font-medium">
                    已为您定制
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  基于您的饮食偏好和习惯，我们将为您制定专属餐单。
                </p>
              </div>
              
              <div className="flex gap-6">
                {/* 左侧数据列表 */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">❤️</span>
                    </div>
                    <div>
                       <span className="font-medium">喜欢的食材</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const favorites = form.getValues('favoriteFoods') || [];
                          return favorites.length > 0 ? `${favorites.length} 种食材` : '暂无特别偏好';
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">🚫</span>
                    </div>
                    <div>
                       <span className="font-medium">不喜欢的食材</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const dislikes = form.getValues('dislikedFoods') || [];
                          if (dislikes.includes('none')) return '无特别不喜欢';
                          return dislikes.length > 0 ? `${dislikes.length} 种食材` : '暂无忌口';
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">🙏</span>
                    </div>
                    <div>
                       <span className="font-medium">饮食限制</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const religious = form.getValues('religiousDietaryRestrictions');
                          switch(religious) {
                            case 'halal': return '清真饮食';
                            case 'kosher': return '洁食';
                            case 'hindu': return '印度教饮食';
                            case 'buddhist': return '佛教饮食';
                            case 'other': return '其他宗教限制';
                            default: return '无宗教限制';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">🍽️</span>
                    </div>
                    <div>
                       <span className="font-medium">用餐习惯</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const regular = form.getValues('regularMeals') === 'regular' ? '三餐规律' : '三餐不规律';
                          const eating = form.getValues('eatingOutFrequency');
                          const frequency = eating === '0-1' ? '很少外食' : eating === '2-3' ? '偶尔外食' : eating === '4-5' ? '经常外食' : '频繁外食';
                          return `${regular}，${frequency}`;
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">⚠️</span>
                    </div>
                    <div>
                       <span className="font-medium">个人忌口</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const restrictions = form.getValues('personalDietaryRestrictions') || [];
                          if (restrictions.includes('none')) return '无特殊忌口';
                          if (restrictions.length === 0) return '暂无记录';
                          const restrictionMap: { [key: string]: string } = {
                            'vegetarian': '素食',
                            'vegan': '纯素',
                            'noBeef': '不吃牛肉',
                            'noPork': '不吃猪肉',
                            'noSeafood': '不吃海鲜',
                            'noSpicy': '不吃辛辣',
                            'lowSalt': '低盐饮食',
                            'lowSugar': '低糖饮食',
                            'other': '其他'
                          };
                          return restrictions.map(r => restrictionMap[r] || r).join('、');
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">🍰</span>
                    </div>
                    <div>
                       <span className="font-medium">易发胖食物偏好</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const preferences = form.getValues('unhealthyFoodPreference') || [];
                          if (preferences.includes('none')) return '都不喜欢';
                          if (preferences.length === 0) return '暂无记录';
                          const preferenceMap: { [key: string]: string } = {
                            'sweets': '甜食',
                            'fried': '油炸食品',
                            'highcarbs': '高碳水食物',
                            'alcohol': '酒精饮料'
                          };
                          return preferences.map(p => preferenceMap[p] || p).join('、');
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">🌿</span>
                    </div>
                    <div>
                       <span className="font-medium">药膳计划</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const plan = form.getValues('medicinalDietPlan');
                          switch(plan) {
                            case 'daily': return '一日一次';
                            case 'threedays': return '三日一次';
                            case 'weekly': return '一周一次';
                            case 'monthly': return '一月一次';
                            case 'none': return '不愿意加入';
                            default: return '暂无选择';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">🍪</span>
                    </div>
                    <div>
                       <span className="font-medium">零食习惯</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const hasSnackHabit = form.getValues('snackingHabit') === 'yes';
                          if (!hasSnackHabit) return '无零食习惯';
                          
                          const snackTypes = form.getValues('snackTypes') || [];
                          if (snackTypes.length === 0) return '有零食习惯';
                          
                          const typeMap: { [key: string]: string } = {
                            'chips': '薯片/膨化食品',
                            'candy': '糖果/巧克力',
                            'nuts': '坚果',
                            'fruit': '水果',
                            'bakery': '面包/糕点',
                            'dairy': '奶制品',
                            'other': '其他'
                          };
                          return snackTypes.map(t => typeMap[t] || t).join('、');
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">⏱️</span>
                    </div>
                    <div>
                       <span className="font-medium">用餐速度</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const speed = form.getValues('eatingSpeed');
                          switch(speed) {
                            case 'fast': return '快（10分钟内）';
                            case 'medium': return '中等（10-20分钟）';
                            case 'slow': return '慢（20分钟以上）';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                       <span className="text-green-600 font-bold">🥗</span>
                    </div>
                    <div>
                       <span className="font-medium">健康接受度</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const acceptance = form.getValues('lowCalorieAcceptance');
                          switch(acceptance) {
                            case 'high': return '很愿意尝试低热量食材';
                            case 'medium': return '适度接受低热量食材';
                            case 'low': return '较难接受低热量食材';
                            default: return '适度接受';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* 右侧餐单示意图 */}
                <div className="w-40 flex-shrink-0">
                   <div className="w-full h-64 rounded-lg bg-gradient-to-b from-green-100 to-blue-100 flex items-center justify-center">
                     <div className="text-center">
                       <div className="text-6xl mb-2">🍽️</div>
                       <p className="text-xs text-gray-600">
                         专属健康餐单
                       </p>
                     </div>
                   </div>
                  </div>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.HEALTH_STATUS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-red-400 to-pink-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🏥</span>
              </div>
            </div>
            
            <div className="space-y-6 text-left">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center gap-2">
                  <span>🏥</span> 您的健康状况
                </h3>
                <div className="flex items-center justify-between">
                  <span className="text-3xl font-bold text-red-500">
                    健康档案
                  </span>
                  <span className="text-red-500 font-medium">
                    已建立完成
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  基于您的健康状况信息，我们将为您制定安全的减重方案。
                </p>
              </div>
              
              <div className="flex gap-6">
                {/* 左侧数据列表 */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                       <span className="text-red-600 font-bold">⚠️</span>
                    </div>
                    <div>
                       <span className="font-medium">食物过敏史</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const allergies = form.getValues('foodAllergies') || [];
                          if (allergies.includes('none')) return '无过敏史';
                          if (allergies.length === 0) return '暂无记录';
                          const allergyMap: { [key: string]: string } = {
                            'dairy': '乳制品',
                            'gluten': '麸质',
                            'nuts': '坚果',
                            'seafood': '海鲜',
                            'eggs': '鸡蛋',
                            'soy': '大豆',
                            'other': '其他'
                          };
                          return allergies.map(a => allergyMap[a] || a).join('、');
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                       <span className="text-red-600 font-bold">🫀</span>
                    </div>
                    <div>
                       <span className="font-medium">慢性疾病</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const diseases = form.getValues('chronicDiseases') || [];
                          if (diseases.includes('none')) return '无慢性疾病';
                          if (diseases.length === 0) return '暂无记录';
                          const diseaseMap: { [key: string]: string } = {
                            'hypertension': '高血压',
                            'diabetes': '糖尿病',
                            'heartDisease': '心脏疾病',
                            'thyroid': '甲状腺疾病',
                            'liver': '肝脏疾病',
                            'kidney': '肾脏疾病',
                            'other': '其他'
                          };
                          return diseases.map(d => diseaseMap[d] || d).join('、');
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                       <span className="text-red-600 font-bold">💊</span>
                    </div>
                    <div>
                       <span className="font-medium">用药情况</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const medications = form.getValues('medications') || [];
                          if (medications.includes('none')) return '无长期用药';
                          if (medications.length === 0) return '暂无记录';
                          const medicationMap: { [key: string]: string } = {
                            'antihypertensive': '降压药',
                            'antidiabetic': '降糖药',
                            'steroid': '类固醇',
                            'antidepressant': '抗抑郁药',
                            'thyroid': '甲状腺药物',
                            'other': '其他长期用药'
                          };
                          return medications.map(m => medicationMap[m] || m).join('、');
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                       <span className="text-red-600 font-bold">😴</span>
                    </div>
                    <div>
                       <span className="font-medium">睡眠质量</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const sleep = form.getValues('sleepQuality');
                          switch(sleep) {
                            case 'good': return '良好（睡眠充足，深沉）';
                            case 'fair': return '一般（睡眠时间不足或质量一般）';
                            case 'poor': return '较差（经常失眠或睡眠浅）';
                            case 'irregular': return '不规律（作息时间不固定）';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* 右侧健康档案示意图 */}
                <div className="w-40 flex-shrink-0">
                   <div className="w-full h-64 rounded-lg bg-gradient-to-b from-red-100 to-pink-100 flex items-center justify-center">
                     <div className="text-center">
                       <div className="text-6xl mb-2">🏥</div>
                       <p className="text-xs text-gray-600">
                         健康档案已建立
                       </p>
                     </div>
                   </div>
                  </div>
              </div>
            </div>
          </div>
        );
      } else if (currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS) {
        return (
          <div className="space-y-6 text-center">
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl">🌿</span>
              </div>
            </div>
            
            <div className="space-y-6 text-left">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center gap-2">
                  <span>🌿</span> 您的中医体质相关症状
                </h3>
                <div className="flex items-center justify-between">
                  <span className="text-3xl font-bold text-purple-500">
                    体质分析
                  </span>
                  <span className="text-purple-500 font-medium">
                    已完成评估
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  基于您的症状反馈，我们将为您提供中医体质分析和调理建议。
                </p>
              </div>
              
              <div className="flex gap-6">
                {/* 左侧数据列表 */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                       <span className="text-purple-600 font-bold">😪</span>
                    </div>
                    <div>
                       <span className="font-medium">身体沉重感</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const heaviness = form.getValues('bodyHeaviness');
                          switch(heaviness) {
                            case 'yes': return '是，经常感到身体沉重困倦';
                            case 'no': return '否，身体感觉正常';
                            case 'sometimes': return '偶尔感到身体沉重';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                       <span className="text-purple-600 font-bold">🌡️</span>
                    </div>
                    <div>
                       <span className="font-medium">温度敏感性</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const temp = form.getValues('temperatureSensitivity');
                          switch(temp) {
                            case 'heat': return '怕热明显';
                            case 'cold': return '怕冷明显';
                            case 'normal': return '无特殊温度敏感';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                       <span className="text-purple-600 font-bold">💦</span>
                    </div>
                    <div>
                       <span className="font-medium">皮肤状况</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const oily = form.getValues('oilySkin');
                          switch(oily) {
                            case 'yes': return '是，皮肤容易出油长痘';
                            case 'no': return '否，皮肤状况正常';
                            case 'sometimes': return '偶尔出现皮肤问题';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                       <span className="text-purple-600 font-bold">💧</span>
                    </div>
                    <div>
                       <span className="font-medium">出汗情况</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const sweating = form.getValues('sweating');
                          switch(sweating) {
                            case 'yes': return '是，容易出汗且汗后感觉疲惫';
                            case 'no': return '否，出汗情况正常';
                            case 'onlyExercise': return '仅运动后出汗';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                       <span className="text-purple-600 font-bold">🤕</span>
                    </div>
                    <div>
                       <span className="font-medium">腰部症状</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const back = form.getValues('backPain');
                          switch(back) {
                            case 'yes': return '是，经常腰部酸痛四肢无力';
                            case 'no': return '否，腰部感觉正常';
                            case 'sometimes': return '偶尔腰部不适';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                       <span className="text-purple-600 font-bold">🍽️</span>
                    </div>
                    <div>
                       <span className="font-medium">食欲状况</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const appetite = form.getValues('appetite');
                          switch(appetite) {
                            case 'yes': return '是，容易没胃口或吃一点就饱';
                            case 'no': return '否，食欲正常';
                            case 'normal': return '食欲正常';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                       <span className="text-purple-600 font-bold">👩‍🦱</span>
                    </div>
                    <div>
                       <span className="font-medium">头发状况</span>
                      <p className="text-sm text-gray-600">
                        {(() => {
                          const hair = form.getValues('hairCondition');
                          switch(hair) {
                            case 'yes': return '是，头发油腻容易脱发';
                            case 'no': return '否，头发状况正常';
                            case 'mild': return '轻微头发问题';
                            default: return '暂无记录';
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                  
                  {form.getValues('gender') === 'female' && (
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                         <span className="text-purple-600 font-bold">👩‍🦰</span>
                      </div>
                      <div>
                         <span className="font-medium">月经规律性</span>
                        <p className="text-sm text-gray-600">
                          {(() => {
                            const menstrual = form.getValues('menstrualRegularity');
                            switch(menstrual) {
                              case 'yes': return '是，月经周期规律';
                              case 'no': return '否，月经周期不规律';
                              case 'sometimes': return '偶尔不规律';
                              default: return '暂无记录';
                            }
                          })()}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* 右侧中医体质示意图 */}
                <div className="w-40 flex-shrink-0">
                   <div className="w-full h-64 rounded-lg bg-gradient-to-b from-purple-100 to-pink-100 flex items-center justify-center">
                     <div className="text-center">
                       <div className="text-6xl mb-2">🌿</div>
                       <p className="text-xs text-gray-600">
                         中医体质分析
                       </p>
                     </div>
                   </div>
                  </div>
              </div>
            </div>
          </div>
        );
      }
      return null;
    }
    
    // 显示健康目标相关建议
    if (currentStepConfig.id === 'weightLossGoal' && inputValues.weightLossGoal) {
      const goalValue = parseFloat(inputValues.weightLossGoal);
      const unit = form.getValues('weightLossGoalUnit');
      let recommendedTime = '';
      
      if (!isNaN(goalValue)) {
        if (unit === 'kg') {
          if (goalValue <= 5) {
            recommendedTime = '3-4个月';
          } else if (goalValue <= 10) {
            recommendedTime = '6-8个月';
          } else {
            recommendedTime = '8-12个月';
          }
        } else if (unit === 'lb') {
          const kgEquivalent = goalValue * 0.453592;
          if (kgEquivalent <= 5) {
            recommendedTime = '3-4个月';
          } else if (kgEquivalent <= 10) {
            recommendedTime = '6-8个月';
          } else {
            recommendedTime = '8-12个月';
          }
        } else if (unit === 'bodyFat') {
          if (goalValue <= 3) {
            recommendedTime = '3-4个月';
          } else if (goalValue <= 6) {
            recommendedTime = '6-8个月';
          } else {
            recommendedTime = '8-12个月';
          }
        }
      }
      
      if (recommendedTime) {
        setTimeout(() => {
          const recommendationElement = document.getElementById('goal-recommendation');
          if (recommendationElement) {
            recommendationElement.classList.remove('opacity-0');
          }
        }, 500);
      }
    }
    
    // 月经规律性问题只对女性显示
    if (currentStepConfig.id === 'menstrualRegularity' && form.getValues('gender') !== 'female') {
      return (
        <div className="text-center text-gray-500 italic">
          <p>此问题仅适用于女性用户，系统已自动跳过。</p>
        </div>
      );
    }
    
    // 出生日期选择
    if (currentStepConfig.type === 'birthDate') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col items-center">
                    <DatePicker
                      value={field.value}
                      onChange={(date) => {
                        field.onChange(date);
                      }}
                      minYear={1940}
                      maxYear={new Date().getFullYear()}
                    />
                    <div className="mt-4 text-center text-gray-500">
                      <p>您的年龄: {new Date().getFullYear() - field.value.getFullYear()} 岁</p>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="text-center mt-4">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 体重选择
    if (currentStepConfig.id === 'weight') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="weight"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col items-center">
                    <WeightGauge
                      value={parseFloat(inputValues.weight) || 65}
                      onChange={(value) => {
                        const newValue = value.toString();
                        // 更新本地状态
                        setInputValues(prev => ({
                          ...prev,
                          weight: newValue
                        }));
                        
                        // 同时更新表单值
                        field.onChange(newValue);
                        
                        // 重新计算BMI
                        if (inputValues.height) {
                          calculateBMI(inputValues.height, form.getValues('heightUnit'), newValue, form.getValues('weightUnit'));
                        }
                      }}
                      minWeight={form.getValues('weightUnit') === 'kg' ? 30 : 66}
                      maxWeight={form.getValues('weightUnit') === 'kg' ? 200 : 440}
                      label={form.getValues('weightUnit') === 'kg' ? '千克' : '磅'}
                      rangeFormat={`范围: ${form.getValues('weightUnit') === 'kg' ? '30 - 200' : '66 - 440'} ${form.getValues('weightUnit')}`}
                    />
                    
                    {/* 单位选择 */}
                    <div className="flex gap-2 mt-6">
                      {currentStepConfig.units?.map((unit) => (
                        <Button
                          key={unit.value}
                          type="button"
                          variant={form.getValues('weightUnit') === unit.value ? "default" : "outline"}
                          onClick={() => {
                            const currentUnit = form.getValues('weightUnit');
                            const newUnit = unit.value;
                            const currentValue = inputValues.weight;
                            
                            // 只有当单位发生变化且有当前值时才进行转换
                            if (currentUnit !== newUnit && currentValue) {
                              let newValue = '';
                              
                                                          // 体重单位转换
                            if (currentUnit === 'kg' && newUnit === 'lb') {
                              // 千克转磅 (1千克 ≈ 2.2046磅)
                              const lbValue = parseFloat(currentValue) * 2.2046;
                              // 直接使用转换值，不强制最小值
                              newValue = lbValue.toFixed(1);
                            } else if (currentUnit === 'lb' && newUnit === 'kg') {
                              // 磅转千克 (1磅 ≈ 0.4536千克)
                              const kgValue = parseFloat(currentValue) * 0.4536;
                              // 确保转换后的值在新的范围内
                              const minKg = 30;
                              const maxKg = 200;
                              const adjustedValue = Math.max(minKg, Math.min(maxKg, kgValue));
                              newValue = adjustedValue.toFixed(1);
                              }
                              
                              if (newValue) {
                                                              // 更新本地状态
                              setInputValues(prev => ({
                                ...prev,
                                weight: newValue
                              }));
                              
                              // 更新表单值
                              form.setValue('weight', newValue);
                              
                              // 强制刷新组件以更新仪表盘
                              setTimeout(() => {
                                form.trigger('weight');
                              }, 0);
                            }
                          }
                          
                          // 更新单位
                          form.setValue('weightUnit', unit.value as any);
                          
                          // 如果有身高值，立即重新计算BMI
                          if (inputValues.height) {
                            calculateBMI(inputValues.height, form.getValues('heightUnit'), inputValues.weight, unit.value);
                          }
                          }}
                          className="min-w-[100px]"
                        >
                          {unit.label}
                        </Button>
                      ))}
                    </div>
                    
                    {/* BMI显示 */}
                    {calculatedBMI !== null && (
                      <div className="bg-muted p-4 rounded-md mt-6 w-full max-w-md">
                        <h4 className="font-medium mb-1">您的BMI指数</h4>
                        <div className="flex items-center justify-between">
                          <p className="text-2xl font-bold">{calculatedBMI}</p>
                          <p className={`text-sm font-medium ${
                            bmiCategory === '正常范围' ? 'text-green-600' : 
                            bmiCategory === '体重过轻' ? 'text-yellow-600' : 
                            'text-red-600'
                          }`}>
                            {bmiCategory}
                          </p>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          BMI是体重指数(Body Mass Index)的缩写，根据身高和体重来计算
                        </p>
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      );
    }
    
    // 时间范围选择
    if (currentStepConfig.type === 'timeframe') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name={currentStepConfig.id as any}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <div className="grid grid-cols-4 gap-2">
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                      <Button
                        key={month}
                        type="button"
                        variant={inputValues.goalTimeframe === month.toString() ? "default" : "outline"}
                        onClick={() => {
                          setInputValues(prev => ({
                            ...prev,
                            goalTimeframe: month.toString()
                          }));
                          form.setValue(currentStepConfig.id as any, month.toString());
                        }}
                        className="h-12"
                      >
                        {month} 个月
                      </Button>
                    ))}
                  </div>
                </FormControl>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription>{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
          
          {/* 健康目标时间建议 */}
          {currentStepConfig.id === 'goalTimeframe' && inputValues.weightLossGoal && (
            <div id="goal-recommendation" className="bg-amber-50 border border-amber-200 p-3 rounded-md text-sm text-amber-700 transition-opacity duration-500 opacity-0">
              <p>
                <span className="font-medium">健康建议：</span>
                {(() => {
                  const goalValue = parseFloat(inputValues.weightLossGoal);
                  const unit = form.getValues('weightLossGoalUnit');
                  
                  if (unit === 'kg') {
                    return `若期望减重 ${goalValue} 千克，建议设置 ${goalValue <= 5 ? '3-4' : goalValue <= 10 ? '6-8' : '8-12'} 个月的达成时间`;
                  } else if (unit === 'lb') {
                    const kgEquivalent = (goalValue * 0.453592).toFixed(1);
                    return `若期望减重 ${goalValue} 磅（约 ${kgEquivalent} 千克），建议设置 ${goalValue * 0.453592 <= 5 ? '3-4' : goalValue * 0.453592 <= 10 ? '6-8' : '8-12'} 个月的达成时间`;
                  } else {
                    return `若期望降低 ${goalValue}% 的体脂率，建议设置 ${goalValue <= 3 ? '3-4' : goalValue <= 6 ? '6-8' : '8-12'} 个月的达成时间`;
                  }
                })()}
              </p>
            </div>
          )}
        </div>
      )
    }
    
    // 多选功能
    if (currentStepConfig.type === 'multiSelect') {
      return (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name={currentStepConfig.id as any}
            render={({ field }) => (
              <FormItem>
                <div className="grid grid-cols-2 gap-3">
                  {currentStepConfig.options?.map((option) => {
                    // 对于需要使用本地状态的字段
                    let isSelected = false;
                    switch(currentStepConfig.id) {
                      case 'dietReactions':
                        isSelected = selectedDietReactions.includes(option.value);
                        break;
                      case 'favoriteFoods':
                        isSelected = selectedFavoriteFoods.includes(option.value);
                        break;
                      case 'dislikedFoods':
                        isSelected = selectedDislikedFoods.includes(option.value);
                        break;
                      case 'personalDietaryRestrictions':
                        isSelected = selectedPersonalDietaryRestrictions.includes(option.value);
                        break;
                      default:
                        isSelected = field.value?.includes(option.value);
                        break;
                    }
                    
                    return (
                      <Card
                        key={option.value}
                        className={cn(
                          "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                          isSelected
                            ? "ring-2 ring-blue-500 shadow-md"
                            : "hover:border-blue-200"
                        )}
                        onClick={() => {
                          const currentValues = Array.isArray(field.value) ? [...field.value] : [];
                          let newValues: string[] = [];
                          
                          if (option.value === 'none') {
                            // 如果选择"没有明显反应"，清除其他选项
                            newValues = isSelected ? [] : ['none'];
                          } else {
                            // 如果选择其他选项，移除"没有明显反应"
                            newValues = isSelected
                              ? currentValues.filter(val => val !== option.value)
                              : [...currentValues.filter(val => val !== 'none'), option.value];
                          }
                          
                          // 更新表单值
                          form.setValue(currentStepConfig.id as any, newValues, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });
                          
                          // 更新相应的本地状态
                          switch(currentStepConfig.id) {
                            case 'dietReactions':
                              setSelectedDietReactions(newValues);
                              break;
                            case 'favoriteFoods':
                              setSelectedFavoriteFoods(newValues);
                              break;
                            case 'dislikedFoods':
                              setSelectedDislikedFoods(newValues);
                              break;
                            case 'personalDietaryRestrictions':
                              setSelectedPersonalDietaryRestrictions(newValues);
                              break;
                          }
                        }}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div className="text-xl">{option.icon}</div>
                          <span className="text-sm">{option.label}</span>
                        </div>
                      </Card>
                    );
                  })}
                </div>
                <FormMessage />
                {currentStepConfig.description && (
                  <FormDescription className="mt-3">{currentStepConfig.description}</FormDescription>
                )}
              </FormItem>
            )}
          />
          
          {/* 只有在用户选择有节食经历时才显示节食反应选择 */}
          {currentStepConfig.id === 'dietReactions' && form.getValues('hasDietHistory') === 'no' && (
            <div className="text-center text-gray-500 italic">
              <p>您选择了没有盲目节食经历，可直接点击下一步继续</p>
            </div>
          )}
          
          {/* 只有在用户选择有零食习惯时才显示零食类型选择 */}
          {currentStepConfig.id === 'snackTypes' && form.getValues('snackingHabit') === 'no' && (
            <div className="text-center text-gray-500 italic">
              <p>您选择了没有零食习惯，可直接点击下一步继续</p>
            </div>
          )}
        </div>
      );
    }
    
    if (currentStepConfig.type === 'input') {
      return (
        <FormField
          control={form.control}
          name={currentStepConfig.id as any}
          render={({ field }) => (
            <FormItem className="w-full">
              <FormControl>
                <div className="flex gap-2">
                  <Input 
                    type="number" 
                    placeholder={currentStepConfig.placeholder}
                    className="text-lg p-6"
                    value={inputValues[currentStepConfig.id as keyof typeof inputValues] || ''}
                    onChange={(e) => {
                      // 更新本地状态
                      setInputValues(prev => ({
                        ...prev,
                        [currentStepConfig.id]: e.target.value
                      }));
                      
                      // 同时更新表单值
                      form.setValue(currentStepConfig.id as any, e.target.value);
                    }}
                    onBlur={field.onBlur}
                    name={field.name}
                    ref={field.ref}
                  />
                  {currentStepConfig.units && (
                    <div className="flex gap-2">
                      {currentStepConfig.units.map((unit) => (
                        <Button
                          key={unit.value}
                          type="button"
                          variant={
                            // 对于减重目标单位，优先使用本地状态
                            (currentStepConfig.id === 'weightLossGoal' && selectedWeightLossUnit === unit.value) ||
                            form.getValues(`${currentStepConfig.id}Unit` as any) === unit.value 
                              ? "default" 
                              : "outline"
                          }
                          onClick={() => {
                            const currentUnit = form.getValues(`${currentStepConfig.id}Unit` as any);
                            const newUnit = unit.value;
                            const currentValue = inputValues[currentStepConfig.id as keyof typeof inputValues];
                            
                            // 如果是减重目标单位，更新选择状态
                            if (currentStepConfig.id === 'weightLossGoal') {
                              setSelectedWeightLossUnit(newUnit);
                              console.log("选择了减重目标单位:", newUnit);
                            }
                            
                            // 只有当单位发生变化且有当前值时才进行转换
                            if (currentUnit !== newUnit && currentValue) {
                              let newValue = '';
                              
                              // 身高单位转换
                              if (currentStepConfig.id === 'height') {
                                if (currentUnit === 'cm' && newUnit === 'inch') {
                                  // 厘米转英寸 (1厘米 ≈ 0.3937英寸)
                                  newValue = (parseFloat(currentValue) * 0.3937).toFixed(1);
                                } else if (currentUnit === 'inch' && newUnit === 'cm') {
                                  // 英寸转厘米 (1英寸 ≈ 2.54厘米)
                                  newValue = (parseFloat(currentValue) * 2.54).toFixed(1);
                                }
                              }
                              // 体重单位转换
                              else if (currentStepConfig.id === 'weight' || currentStepConfig.id === 'weightLossGoal') {
                                if (currentUnit === 'kg' && newUnit === 'lb') {
                                  // 千克转磅 (1千克 ≈ 2.2046磅)
                                  newValue = (parseFloat(currentValue) * 2.2046).toFixed(1);
                                } else if (currentUnit === 'lb' && newUnit === 'kg') {
                                  // 磅转千克 (1磅 ≈ 0.4536千克)
                                  newValue = (parseFloat(currentValue) * 0.4536).toFixed(1);
                                } else if ((currentUnit === 'kg' || currentUnit === 'lb') && newUnit === 'bodyFat') {
                                  // 切换到体脂率时，设置默认值为5%
                                  newValue = '5';
                                } else if (newUnit === 'kg' && currentUnit === 'bodyFat') {
                                  // 从体脂率切换到千克，设置默认值为5千克
                                  newValue = '5';
                                } else if (newUnit === 'lb' && currentUnit === 'bodyFat') {
                                  // 从体脂率切换到磅，设置默认值为10磅
                                  newValue = '11';
                                }
                              }
                              
                              if (newValue) {
                                // 更新本地状态
                                setInputValues(prev => ({
                                  ...prev,
                                  [currentStepConfig.id]: newValue
                                }));
                                
                                // 更新表单值
                                form.setValue(currentStepConfig.id as any, newValue, {
                                  shouldValidate: true,
                                  shouldDirty: true,
                                  shouldTouch: true
                                });
                              }
                            }
                            
                            // 更新单位
                            form.setValue(`${currentStepConfig.id}Unit` as any, unit.value as any, {
                              shouldValidate: true,
                              shouldDirty: true,
                              shouldTouch: true
                            });
                            
                            // 如果是身高或体重单位改变，立即重新计算BMI
                            if ((currentStepConfig.id === 'height' || currentStepConfig.id === 'weight') && 
                                inputValues.height && inputValues.weight) {
                              const heightUnit = currentStepConfig.id === 'height' ? newUnit : form.getValues('heightUnit');
                              const weightUnit = currentStepConfig.id === 'weight' ? newUnit : form.getValues('weightUnit');
                              calculateBMI(inputValues.height, heightUnit, inputValues.weight, weightUnit);
                            }
                          }}
                        >
                          {unit.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )
    }
    
    // 渲染体脂率输入（当用户选择"知道体脂率"时）
    if (currentStepConfig.id === 'knowsBodyFat' && (knowsBodyFatSelection === 'yes' || form.getValues('knowsBodyFat') === 'yes')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsBodyFatSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsBodyFatSelection(option.value);
                  
                  // 因为知道体脂率选择"是"后不需要立即前进，所以这里不添加自动跳转
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <FormField
            control={form.control}
            name="bodyFatPercentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>体脂率 (%)</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入您的体脂率" 
                    value={inputValues.bodyFatPercentage}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      setInputValues(prev => ({
                        ...prev,
                        bodyFatPercentage: newValue
                      }));
                      // 使用正确的参数设置表单值，确保触发验证
                      form.setValue('bodyFatPercentage', newValue, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true
                      });
                      
                      // 如果有错误，清除错误
                      if (form.formState.errors.bodyFatPercentage) {
                        form.clearErrors('bodyFatPercentage');
                      }
                    }}
                    onKeyDown={(e) => {
                      // 当用户按下回车键时，如果输入有效，自动进入下一步
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const value = inputValues.bodyFatPercentage;
                        if (value && parseFloat(value) > 0 && parseFloat(value) <= 100) {
                          handleNext();
                        }
                      }
                    }}
                    onBlur={(e) => {
                      field.onBlur();
                      // 在失去焦点时也验证一次
                      const value = e.target.value;
                      if (!value || value.trim() === '') {
                        form.setError('bodyFatPercentage', {
                          type: 'manual',
                          message: '请输入您的体脂率'
                        });
                      } else {
                        const numericValue = parseFloat(value);
                        if (isNaN(numericValue) || numericValue <= 0 || numericValue > 100) {
                          form.setError('bodyFatPercentage', {
                            type: 'manual',
                            message: '请输入有效的体脂率数值（1-100之间）'
                          });
                        } else {
                          form.clearErrors('bodyFatPercentage');
                        }
                      }
                    }}
                    name={field.name}
                    ref={field.ref}
                  />
                </FormControl>
                <FormDescription>
                  体脂率是指体内脂肪重量在总体重中所占的百分比
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )
    }
    
    // 为"不知道体脂率"选项提供链接
    if (currentStepConfig.id === 'knowsBodyFat' && (knowsBodyFatSelection === 'no' || form.getValues('knowsBodyFat') === 'no')) {
      return (
        <div className="space-y-6">
          <div className="w-full flex flex-col gap-3">
            {currentStepConfig.options?.map((option) => (
              <Card
                key={option.value}
                className={cn(
                  "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
                  (knowsBodyFatSelection === option.value) || form.getValues(currentStepConfig.id as any) === option.value
                    ? "ring-2 ring-blue-500 shadow-md"
                    : "hover:border-blue-200"
                )}
                onClick={() => {
                  form.setValue(currentStepConfig.id as any, option.value as any, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true
                  });
                  setKnowsBodyFatSelection(option.value);
                  
                  // 为了更好的用户体验，选择后自动前进到下一步
                  setTimeout(() => {
                    handleNext();
                  }, 300);
                }}
              >
                <div className="flex items-center gap-4 w-full justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{option.icon}</div>
                    <span className="text-base">{option.label}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <div className="text-sm text-blue-600">
            <Link href="#" className="underline">
              了解如何测量体脂率
            </Link>
          </div>
        </div>
      )
    }
    
    return (
      <div className="w-full flex flex-col gap-3">
        {currentStepConfig.options?.map((option) => (
          <Card
            key={option.value}
            className={cn(
              "p-4 cursor-pointer transition-all hover:shadow-md flex items-center",
              // 对于需要使用本地状态的字段，使用本地状态来确定是否选中，否则使用表单值
              (currentStepConfig.id === 'gender' && selectedGender === option.value) || 
              (currentStepConfig.id === 'religiousDietaryRestrictions' && selectedReligiousDietaryRestriction === option.value) || 
              form.getValues(currentStepConfig.id as any) === option.value
                ? "ring-2 ring-blue-500 shadow-md"
                : "hover:border-blue-200"
            )}
            onClick={() => {
              // 更新表单值
              form.setValue(currentStepConfig.id as any, option.value as any, {
                shouldValidate: true, // 立即触发验证
                shouldDirty: true,    // 标记为已修改
                shouldTouch: true     // 标记为已交互
              });
              
              // 添加视觉反馈并触发表单验证
              const selectedValue = form.getValues(currentStepConfig.id as any);
              console.log("选择了:", option.value, "当前值:", selectedValue);
              
              // 更新相应的本地状态以确保UI响应
              switch(currentStepConfig.id) {
                case 'gender':
                  setSelectedGender(option.value);
                  break;
                case 'religiousDietaryRestrictions':
                  setSelectedReligiousDietaryRestriction(option.value);
                  break;
              }
              
              // 为了解决点击不响应问题，强制更新表单状态
              setTimeout(() => {
                // 对于某些步骤，可以自动前进到下一步
                if (currentStepConfig.id === 'gender' || 
                    currentStepConfig.id === 'religiousDietaryRestrictions') {
                  handleNext();
                }
              }, 200);
            }}
          >
            <div className="flex items-center gap-4 w-full justify-between">
              <div className="flex items-center gap-4">
                <div className="text-2xl">{option.icon}</div>
                <span className="text-base">{option.label}</span>
              </div>
            </div>
          </Card>
        ))}
      </div>
    )
  }
  
  
  

  

  

  
  // 计算进度百分比
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100
  
  return (
    <div className="w-full max-w-xl mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <span>健康瘦身信息采集</span>
        </h2>
        <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-md inline-block mt-1 mb-2 text-sm font-medium">
          {currentPhase === COLLECTION_PHASES.BODY_DATA ? '身体数据采集' : 
           currentPhase === COLLECTION_PHASES.HEALTH_GOALS ? '健康目标采集' : 
           currentPhase === COLLECTION_PHASES.DIET_PREFERENCES ? '饮食偏好采集' :
           currentPhase === COLLECTION_PHASES.HEALTH_STATUS ? '健康状况采集' :
           currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS ? '中医体质相关症状采集' :
           '信息采集完成'}
        </div>
        <p className="text-gray-600 mt-2">
          {currentPhase === COLLECTION_PHASES.BODY_DATA
            ? '请提供以下身体数据，以便我们为您制定个性化的健康瘦身计划。'
            : currentPhase === COLLECTION_PHASES.HEALTH_GOALS
                ? '请设置您的健康目标，以便我们为您制定科学合理的减重计划。' 
            : currentPhase === COLLECTION_PHASES.DIET_PREFERENCES
            ? '请提供您的饮食偏好信息，以便我们为您定制更合适的减重餐单。'
            : currentPhase === COLLECTION_PHASES.HEALTH_STATUS
            ? '请提供您的健康状况信息，以便我们为您制定更安全、更有效的减重计划。'
            : currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS
            ? '请提供您的中医体质相关症状信息，以便我们为您提供更全面的健康评估和中医体质分析。'
            : '信息采集已完成，感谢您的配合。'
          }
        </p>
      </div>
      
      {/* 进度指示器 */}
      <div className="mb-12">
        <h3 className="text-base font-medium text-gray-700 mb-2">
          {currentPhase === COLLECTION_PHASES.BODY_DATA ? '身体数据采集进度' : 
           currentPhase === COLLECTION_PHASES.HEALTH_GOALS ? '健康目标采集进度' : 
           currentPhase === COLLECTION_PHASES.DIET_PREFERENCES ? '饮食偏好采集进度' : 
           currentPhase === COLLECTION_PHASES.HEALTH_STATUS ? '健康状况采集进度' :
           currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS ? '中医体质相关症状采集进度' :
           '信息采集进度'}
        </h3>
        <div className="flex justify-between items-center mb-1 text-sm">
          <span>步骤 {currentStep + 1} / {totalSteps}</span>
          <span>{progressPercentage.toFixed(0)}%</span>
        </div>
        <div className="mb-1 text-xs text-gray-500">
          <span>点击下方进度条可快速跳转到任意步骤</span>
        </div>
        <div className="relative w-full h-8 py-3">
          <div className="relative w-full bg-gray-200 h-2 rounded-full overflow-hidden cursor-pointer hover:bg-gray-300 transition-colors"
            title="点击跳转到相应步骤"
            onClick={(e) => {
              // 获取点击位置相对于进度条的百分比
              const rect = e.currentTarget.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const percentPosition = x / rect.width;
              
              // 将百分比位置转换为步骤索引
              const stepIndex = Math.min(
                Math.max(Math.floor(percentPosition * totalSteps), 0), 
                totalSteps - 1
              );
              
              // 允许跳转到任意步骤
              setCurrentStep(stepIndex);
            }}
          >
          <div 
            className="h-full bg-gradient-to-r from-blue-500 to-green-400"
            style={{ width: `${progressPercentage}%` }}
          ></div>
            
            {/* 添加步骤标记 */}
            <div className="absolute top-0 left-0 w-full h-full pointer-events-none flex items-center">
              {/* 显示所有步骤的点 */}
              {Array.from({ length: totalSteps }).map((_, index) => {
                const isCompleted = index < currentStep;
                const isCurrent = index === currentStep;
                // 主要步骤位置
                const isMainStep = [0, 6, 10, 22, 25, totalSteps-1].includes(index);
                
                return (
                  <div 
                    key={index}
                    className={`absolute rounded-full transform -translate-y-1/2 -translate-x-1/2 ${
                      isCurrent 
                        ? 'bg-blue-600 ring-2 ring-blue-300' 
                        : isCompleted 
                          ? 'bg-green-500 hover:ring-1 hover:ring-green-200' 
                          : 'bg-gray-400 hover:ring-1 hover:ring-gray-200'
                    } transition-all`}
                    style={{ 
                      left: `${(index / (totalSteps - 1)) * 100}%`, 
                      top: '50%',
                      width: isMainStep ? '12px' : '8px',
                      height: isMainStep ? '12px' : '8px'
                    }}
                    title={`步骤 ${index + 1}`}
                  ></div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      
      <Form {...form}>
        <form className="space-y-8">
          {/* 问题标题 */}
          <h3 className="text-xl font-medium text-center">
            {currentPhaseSteps[currentStep].title}
          </h3>
          
          {/* 当前步骤内容 */}
          {renderCurrentStep()}
          
          {/* 导航按钮 */}
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              className="flex items-center gap-1"
            >
              <ChevronLeft size={16} /> 上一步
            </Button>
            
            <Button
              type="button"
              onClick={handleNext}
              className="flex items-center gap-1"
            >
              {currentPhaseSteps[currentStep]?.type === 'intro'
                ? '开始'
                : currentStep === totalSteps - 1 && currentPhaseSteps[currentStep]?.type === 'summary' && currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS
                  ? '开始评估'
                  : currentStep === totalSteps - 1 && currentPhaseSteps[currentStep]?.type === 'summary' 
                    ? '继续' 
                    : currentStep === totalSteps - 1 
                      ? '完成' 
                      : '下一步'
              } <ChevronRight size={16} />
            </Button>
          </div>
          
          {currentPhaseSteps[currentStep]?.type === 'intro' && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
              <p>
                                    {currentPhase === COLLECTION_PHASES.BODY_DATA
                      ? '点击"开始"按钮开始填写您的身体数据。'
                      : currentPhase === COLLECTION_PHASES.HEALTH_GOALS
                      ? '点击"开始"按钮开始设定您的健康目标。'
                      : currentPhase === COLLECTION_PHASES.DIET_PREFERENCES
                      ? '点击"开始"按钮开始了解您的饮食偏好。'
                      : currentPhase === COLLECTION_PHASES.HEALTH_STATUS
                      ? '点击"开始"按钮开始了解您的健康状况。'
                      : '点击"开始"按钮开始了解您的中医体质相关症状。'
                    }
              </p>
            </div>
          )}
          
          {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.BODY_DATA && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
              <p>这是您的身体数据总结，点击"继续"进入下一阶段。</p>
            </div>
          )}

          {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.HEALTH_GOALS && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
              <p>这是您的健康目标总结，点击"继续"进入下一阶段。</p>
            </div>
          )}
          
                      {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.DIET_PREFERENCES && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
                <p>这是您的饮食偏好总结，点击"继续"进入下一阶段。</p>
            </div>
          )}
            {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.HEALTH_STATUS && (
            <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
                <p>这是您的健康状况总结，点击"继续"进入下一阶段。</p>
              </div>
            )}
            {currentStep === totalSteps - 1 && currentPhase === COLLECTION_PHASES.TCM_SYMPTOMS && (
              <div className="mt-4 text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
                <p>这是您的中医体质相关症状总结，点击"开始评估"完成信息采集并提交表单。</p>
            </div>
          )}
        </form>
      </Form>
    </div>
  )
} 
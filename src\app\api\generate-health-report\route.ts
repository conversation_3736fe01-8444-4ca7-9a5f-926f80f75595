import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { TCM_HEALTH_ASSESSMENT_PROMPT } from '@/actions/constants'
import { saveHealthAssessmentReport } from '@/lib/db/health-assessment'

export async function POST(request: NextRequest) {
  try {
    const { userData, formData } = await request.json()

    if (!userData) {
      return NextResponse.json(
        { error: '缺少用户数据' },
        { status: 400 }
      )
    }

    // 构建完整的提示词
    const fullPrompt = TCM_HEALTH_ASSESSMENT_PROMPT.replace('{userDataPlaceholder}', userData)

    // 调用通义千问API
    const apiKey = process.env.DASHSCOPE_API_KEY
    if (!apiKey) {
      throw new Error('未配置DASHSCOPE API密钥')
    }

    const startTime = Date.now()

    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: process.env.VISION_MODEL || 'qwen-plus-latest',
        input: {
          messages: [
            {
              role: 'user',
              content: fullPrompt
            }
          ]
        },
        parameters: {
          max_tokens: 4000,
          temperature: 0.7,
          top_p: 0.8,
        }
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('通义千问API错误:', errorData)
      throw new Error(`通义千问API调用失败: ${response.status}`)
    }

    const data = await response.json()
    
    // 打印AI返回的JSON结果和消耗时间
    const endTime = Date.now()
    const duration = endTime - startTime
    console.log('=== AI返回的JSON结果 ===')
    console.log(JSON.stringify(data, null, 2))
    console.log('=== 消耗时间 ===')
    console.log(`${duration}ms`)
    
    if (!data.output?.text) {
      throw new Error('通义千问API返回数据格式错误')
    }

    const reportText = data.output.text

    // 打印原始报告文本用于调试
    console.log('=== 原始报告文本 ===')
    console.log(reportText)
    console.log('=== 文本长度 ===')
    console.log(`${reportText.length} 字符`)

    // 解析AI返回的报告内容
    const parsedReport = parseAIReport(reportText)

    console.log('=== 解析后的报告结构 ===')
    console.log(JSON.stringify(parsedReport, null, 2))

    // 获取当前用户信息
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    // 只允许登录用户生成报告
    if (!token?.userId) {
      return NextResponse.json(
        { error: '请先登录再生成健康评估报告' },
        { status: 401 }
      )
    }

    const userId = token.userId as string
    console.log('=== 使用登录用户ID ===', userId)

    // 获取客户端IP（用于记录）
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    request.headers.get('x-client-ip') || 
                    'unknown'

    // 获取用户选择的群体
    const selectedGroup = formData.selectedGroup || 'weight-loss'

    try {
      // 保存到数据库
      await saveHealthAssessmentReport({
        user_id: userId,
        audience_group: selectedGroup,
        form_data: formData,
        user_data_summary: userData,
        report_data: parsedReport,
        basic_analysis: parsedReport.basicAnalysis,
        tcm_constitution: parsedReport.tcmConstitution,
        health_risks: parsedReport.healthRisks,
        diet_recommendations: parsedReport.tcmWeightLossPlan?.dietRecommendations,
        herbal_recommendations: parsedReport.tcmWeightLossPlan?.herbalRecommendations,
        lifestyle_adjustments: parsedReport.tcmWeightLossPlan?.lifestyleAdjustments,
        short_term_goals: parsedReport.personalizedAdvice?.shortTermGoals,
        long_term_plan: parsedReport.personalizedAdvice?.longTermPlan,
        ip_address: clientIP
      })
      
      console.log('=== 报告已保存到数据库 ===')
    } catch (dbError) {
      console.error('保存到数据库时出错:', dbError)
      // 不影响返回结果，只记录错误
    }

    return NextResponse.json({
      success: true,
      report: parsedReport,
      userId: userId // 返回用户ID供前端使用
    })

  } catch (error) {
    console.error('生成健康报告时出错:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '生成报告时发生未知错误',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 解析AI生成的报告内容
function parseAIReport(reportText: string) {
  // 使用更智能的方式解析结构化内容
  const extractSection = (text: string, startPattern: RegExp, endPattern?: RegExp) => {
    const startMatch = text.match(startPattern)
    if (!startMatch) return ''
    
    const startIndex = startMatch.index! + startMatch[0].length
    let endIndex = text.length
    
    if (endPattern) {
      const endMatch = text.slice(startIndex).match(endPattern)
      if (endMatch) {
        endIndex = startIndex + endMatch.index!
      }
    }
    
    return text.slice(startIndex, endIndex).trim()
  }

  // 提取各个章节
  const basicAnalysis = extractSection(
    reportText, 
    /##\s*一、基本情况分析/,
    /---\s*##/
  )

  // 提取中医体质诊断，包含风险预警
  const tcmConstitutionSection = extractSection(
    reportText,
    /##\s*二、中医体质诊断/,
    /---\s*##\s*三、健康状况评估|---\s*##\s*四、中医减肥方案/
  )
  
  const tcmConstitution = extractSection(
    tcmConstitutionSection,
    /^/,
    /###\s*潜在健康风险预警|此体质如不调理/
  )
  
  const healthRisks = extractSection(
    reportText,
    /###\s*潜在健康风险预警/,
    /---\s*##/
  )

  // 提取减肥方案的子章节
  const weightLossSection = extractSection(
    reportText,
    /##\s*四、中医减肥方案/,
    /---\s*##\s*五、个性化建议/
  )

  console.log('=== 减肥方案章节内容 ===')
  console.log('weightLossSection长度:', weightLossSection.length)
  console.log('weightLossSection内容:', weightLossSection.substring(0, 500) + '...')

  const dietRecommendations = extractSection(
    reportText,
    /###\s*饮食调理建议/,
    /###\s*中药调理方案|###\s*生活方式调整/
  )
  
  const herbalRecommendations = extractSection(
    reportText,
    /###\s*中药调理方案/,
    /###\s*生活方式调整|---\s*##\s*五、个性化建议/
  )

  const lifestyleAdjustments = extractSection(
    reportText,
    /###\s*生活方式调整/,
    /---\s*##\s*五、个性化建议/
  )

  // 提取个性化建议的子章节
  const shortTermGoals = extractSection(
    reportText,
    /###\s*短期目标[^#]*?（[^）]*?）/,
    /---\s*###|###\s*长期养生计划/
  )
  
  const longTermPlan = extractSection(
    reportText,
    /###\s*长期养生计划[^#]*?（[^）]*?）/,
    /---\s*##/
  )

  // 清理提取的内容，移除多余的markdown格式
  const cleanContent = (content: string) => {
    return content
      .replace(/\*\*/g, '')  // 移除粗体标记
      .replace(/\*/g, '')    // 移除斜体标记
      .replace(/^---+$/gm, '') // 移除分割线
      .replace(/^\s*>\s*/gm, '') // 移除引用标记
      .trim()
  }

  console.log('=== 解析结果调试 ===')
  console.log('基本情况分析:', basicAnalysis ? '已提取' : '未提取')
  console.log('中医体质诊断:', tcmConstitution ? '已提取' : '未提取')
  console.log('健康风险预警:', healthRisks ? '已提取' : '未提取')
  console.log('饮食调理建议:', dietRecommendations ? '已提取' : '未提取')
  console.log('中药调理方案:', herbalRecommendations ? '已提取' : '未提取')
  console.log('生活方式调整:', lifestyleAdjustments ? '已提取' : '未提取')
  console.log('短期目标:', shortTermGoals ? '已提取' : '未提取')
  console.log('长期养生计划:', longTermPlan ? '已提取' : '未提取')

  // 添加详细的调试信息
  if (!dietRecommendations) {
    console.log('=== 饮食调理建议提取失败调试 ===')
    console.log('原始文本中是否包含"饮食调理建议":', reportText.includes('饮食调理建议'))
    console.log('原始文本中是否包含"### 饮食调理建议":', reportText.includes('### 饮食调理建议'))
    console.log('原始文本中是否包含"###饮食调理建议":', reportText.includes('###饮食调理建议'))

    // 查找所有可能的饮食相关标题
    const dietMatches = reportText.match(/###[^#]*?饮食[^#]*?/g)
    console.log('找到的饮食相关标题:', dietMatches)
  }

  return {
    basicAnalysis: cleanContent(basicAnalysis) || '基本情况分析生成中...',
    tcmConstitution: cleanContent(tcmConstitution) || '中医体质诊断生成中...',
    healthRisks: cleanContent(healthRisks) || '健康风险分析生成中...',
    tcmWeightLossPlan: {
      dietRecommendations: cleanContent(dietRecommendations) || '饮食建议生成中...',
      herbalRecommendations: cleanContent(herbalRecommendations) || '中药方案生成中...',
      lifestyleAdjustments: cleanContent(lifestyleAdjustments) || '生活调整建议生成中...'
    },
    personalizedAdvice: {
      shortTermGoals: cleanContent(shortTermGoals) || '短期目标制定中...',
      longTermPlan: cleanContent(longTermPlan) || '长期计划制定中...'
    }
  }
} 
export interface Nutrition {
  calories: number
  protein: number
  carbs: number
  fat: number
}

export interface Dish {
  name: string
  ingredients: string
  cookingMethod: string
  cookingSteps?: string[] // 详细制作步骤
  nutrition: Nutrition
  tcmProperties?: string // 中医属性，仅膳食计划有
}

export interface Meal {
  dishes: Dish[]
  totalNutrition: Nutrition
}

export interface DayPlan {
  date: string
  meals: {
    breakfast: Meal
    lunch: Meal
    dinner: Meal
    snack?: Meal
    herbalTea?: Meal // 中医膳食计划可能包含的药茶
  }
  dailyTotal: Nutrition
}

export interface WeeklyDietPlan {
  day1: DayPlan
  day2: DayPlan
  day3: DayPlan
  day4: DayPlan
  day5: DayPlan
  day6: DayPlan
  day7: DayPlan
}

export interface DietPlan {
  id?: number
  user_id: string
  plan_type: 'normal' | 'herbal'
  weekly_plan: WeeklyDietPlan
  special_notes: string
  tcm_principles?: string // 中医膳食原理，仅膳食计划有
  basic_analysis: string
  diet_recommendations?: string
  herbal_recommendations?: string // 中药调理建议，仅膳食计划有
  created_at?: Date
  updated_at?: Date
}

export interface DietPlanGeneration {
  planType: 'normal' | 'herbal'
  weeklyPlan: WeeklyDietPlan
  specialNotes: string
  tcmPrinciples?: string
}

export interface DietPlanRequest {
  userId: string
  includeHerbalPlan: boolean // 是否包含中医膳食计划
}

export interface DietPlanResponse {
  success: boolean
  dietPlan?: DietPlan
  error?: string
} 
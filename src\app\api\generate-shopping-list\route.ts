import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { generateShoppingList } from '@/lib/shopping-list-generator'
import { saveShoppingList } from '@/lib/db/shopping-list'
import { getLatestDietPlan } from '@/lib/db/diet-plan'
import { getLatestHerbalPlan } from '@/lib/db/herbal-plan'
import { ShoppingListGenerateRequest } from '@/types/shopping-list'

export async function POST(request: NextRequest) {
  try {
    const { userId, includeDietPlan = true, includeHerbalPlan = true }: ShoppingListGenerateRequest = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: '缺少用户ID' },
        { status: 400 }
      )
    }

    let dietPlan = null
    let herbalPlan = null

    // 获取饮食计划
    if (includeDietPlan) {
      dietPlan = await getLatestDietPlan(userId)
      if (!dietPlan) {
        console.warn('用户没有饮食计划，跳过食材提取')
      }
    }

    // 获取中药方案
    if (includeHerbalPlan) {
      herbalPlan = await getLatestHerbalPlan(userId)
      if (!herbalPlan) {
        console.warn('用户没有中药方案，跳过药材提取')
      }
    }

    // 检查是否有任何计划
    if (!dietPlan && !herbalPlan) {
      return NextResponse.json(
        { error: '用户没有饮食计划或中药方案，无法生成购物清单' },
        { status: 404 }
      )
    }

    // 生成购物清单
    const shoppingList = generateShoppingList(userId, dietPlan || undefined, herbalPlan || undefined)

    // 保存到数据库
    const shoppingListId = await saveShoppingList(shoppingList)
    shoppingList.id = shoppingListId

    console.log('=== 购物清单生成完成 ===')
    console.log('用户ID:', userId)
    console.log('包含饮食计划:', !!dietPlan)
    console.log('包含中药方案:', !!herbalPlan)
    console.log('总项目数:', shoppingList.total_items)
    console.log('数据库ID:', shoppingListId)

    return NextResponse.json({
      success: true,
      shoppingList: shoppingList
    })

  } catch (error) {
    console.error('购物清单生成错误:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '购物清单生成失败'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId, items } = await request.json()

    if (!userId || !items) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 更新购物清单项目
    const { updateShoppingListItems } = await import('@/lib/db/shopping-list')
    const success = await updateShoppingListItems(userId, items)

    if (!success) {
      return NextResponse.json(
        { error: '更新购物清单失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: '购物清单更新成功'
    })

  } catch (error) {
    console.error('购物清单更新错误:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '购物清单更新失败'
      },
      { status: 500 }
    )
  }
}

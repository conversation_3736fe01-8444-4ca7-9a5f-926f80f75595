export interface Order {
    orderId: string;
    fullName: string;
    email: string;
    product: string;
    amount: number;
    type: string;
    status: string;
    date: string;
}


export interface UserOrder {
    orderId: string;
    credits: string;
    price: string;
    date: string;
    type: string;
    status: string;
}

export interface UserSubscription {
    orderId: string;
    credits: string;
    price: string;
    date: string;
    renewalDate: string;
}
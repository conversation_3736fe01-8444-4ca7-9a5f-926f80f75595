# 移除临时用户重构

## 重构目标

根据用户要求，移除系统中的临时用户功能，确保所有健康评估都与真实登录用户关联：

> "把临时用户给我去掉，不需要临时用户。当用户选择群体后点继续，如果没登录则直接弹出登录框让其登录。"

## 重构内容

### 1. 前端组件修改

#### `audience-group-selector.tsx`

**添加功能**：
- 集成 `useSession` 钩子检查登录状态
- 添加登录状态检查逻辑
- 添加简化的登录提示对话框

**修改逻辑**：
```typescript
const handleContinue = async () => {
  if (!selectedGroup) {
    toast.error('请选择一个受众群体');
    return;
  }

  // 检查用户是否已登录
  if (!session?.user) {
    // 用户未登录，显示登录对话框
    setShowLoginDialog(true);
    return;
  }

  // 用户已登录，保存选择并继续
  localStorage.setItem('selected_audience_group', selectedGroup);
  setShowForm(true);
};
```

**登录提示对话框**：
```jsx
{showLoginDialog && (
  <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
      <h3 className="text-lg font-semibold mb-4">需要登录</h3>
      <p className="text-gray-600 mb-6">
        请先登录您的账户，以便为您保存个性化的健康评估报告。
      </p>
      <div className="flex gap-3">
        <Button variant="outline" onClick={() => setShowLoginDialog(false)}>取消</Button>
        <Button onClick={() => window.location.href = '/login'}>去登录</Button>
      </div>
    </div>
  </div>
)}
```

### 2. API端点重构

#### `/api/generate-health-report`

**之前**：支持登录和未登录用户
```typescript
if (token?.userId) {
  userId = token.userId as string
} else {
  userId = `guest_${Buffer.from(clientIP).toString('base64')}_${Date.now()}`
}
```

**现在**：只支持登录用户
```typescript
if (!token?.userId) {
  return NextResponse.json(
    { error: '请先登录再生成健康评估报告' },
    { status: 401 }
  )
}

const userId = token.userId as string
```

#### `/api/check-health-report`

**之前**：根据登录状态选择查询策略
```typescript
if (token?.userId) {
  // 查询登录用户的报告
  hasReport = await hasHealthAssessmentReport(userId)
} else {
  // 查询临时用户的报告
  hasReport = await hasHealthAssessmentReportByIPPrefix(tempUserIdPrefix)
}
```

**现在**：只查询登录用户的报告
```typescript
if (!token?.userId) {
  return NextResponse.json({
    success: true,
    hasReport: false,
    report: null
  })
}

const userId = token.userId as string
const hasReport = await hasHealthAssessmentReport(userId)
```

#### `/api/delete-health-report`

**之前**：根据登录状态选择删除策略

**现在**：只允许登录用户删除
```typescript
if (!token?.userId) {
  return NextResponse.json(
    { error: '请先登录' },
    { status: 401 }
  )
}

const userId = token.userId as string
const deleted = await deleteHealthAssessmentReport(userId)
```

### 3. 数据库影响

#### 清理方案
- **保留现有数据**：已存在的临时用户数据保留在数据库中
- **停止生成**：不再创建新的临时用户记录
- **函数保留**：临时用户相关的数据库函数暂时保留，便于后续清理

#### 数据库记录格式
**之前**：
- 登录用户：`user_id = "user123"`
- 临时用户：`user_id = "guest_Ojox_1753335401566"`

**现在**：
- 只有登录用户：`user_id = "user123"`

## 用户体验流程

### 新的用户流程
```
1. 用户访问健康评估页面
2. 选择用户群体
3. 点击"继续"
4. 系统检查登录状态：
   - 如果已登录：直接进入表单填写
   - 如果未登录：显示登录提示对话框
5. 用户选择登录或取消
6. 登录成功后自动进入表单填写
7. 完成表单后生成并保存报告（关联到真实用户ID）
```

### 登录流程集成
- 使用系统现有的登录组件和流程
- 登录成功后自动返回健康评估流程
- 保持用户选择的群体信息

## 安全性提升

### 数据关联安全
- ✅ **消除数据混乱**：不再有临时用户数据与真实用户的混淆
- ✅ **明确数据归属**：所有健康报告都明确归属于真实用户
- ✅ **防止数据泄露**：不会因IP地址变化导致的数据访问问题

### 用户身份验证
- ✅ **强制身份验证**：所有健康评估都需要真实用户身份
- ✅ **一致的权限控制**：统一的用户权限验证逻辑
- ✅ **审计友好**：所有操作都有明确的用户身份记录

## 代码简化

### 移除的复杂逻辑
- ❌ IP地址获取和处理逻辑
- ❌ 临时用户ID生成逻辑
- ❌ 双重查询策略（登录用户 vs 临时用户）
- ❌ 条件分支处理

### 保留的核心功能
- ✅ 健康评估报告生成
- ✅ 报告查看和管理
- ✅ 数据持久化存储
- ✅ 用户群体选择

## 后续清理任务

### 数据库清理（可选）
1. **查询临时用户数据**：
   ```sql
   SELECT * FROM nf_health_assessment_reports WHERE user_id LIKE 'guest_%'
   ```

2. **删除临时用户数据**（谨慎操作）：
   ```sql
   DELETE FROM nf_health_assessment_reports WHERE user_id LIKE 'guest_%'
   ```

### 代码清理（可选）
- 移除 `hasHealthAssessmentReportByIPPrefix` 函数
- 移除 `getLatestHealthAssessmentReportByIPPrefix` 函数  
- 移除 `deleteHealthAssessmentReportByIPPrefix` 函数

## 测试验证

### 功能测试
1. **未登录用户**：
   - 选择群体 → 点击继续 → 显示登录提示
   - 取消登录 → 返回群体选择
   - 去登录 → 跳转到登录页面

2. **登录用户**：
   - 选择群体 → 点击继续 → 直接进入表单
   - 填写表单 → 生成报告 → 保存到真实用户ID
   - 查看报告 → 只显示自己的报告

### 安全测试
1. **API访问控制**：
   - 未登录状态调用生成API → 返回401错误
   - 未登录状态调用删除API → 返回401错误
   - 未登录状态调用查询API → 返回空结果

## 总结

这次重构完全移除了临时用户功能，确保：

1. **数据安全**：所有健康报告都明确归属于真实用户
2. **用户体验**：强制登录确保数据不会丢失
3. **代码简化**：移除了复杂的双重用户处理逻辑
4. **一致性**：统一的用户身份验证和数据访问策略

用户现在必须登录才能使用健康评估功能，这确保了数据的正确归属和长期可访问性。 
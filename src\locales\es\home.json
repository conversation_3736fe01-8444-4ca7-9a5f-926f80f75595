{"meta": {"title": "Evaluación de salud de hipertensión - Menú dietético de hipertensión | Guía de ejercicios para hipertensión & Plan de dieta para presión arterial", "description": "A través de cuestionarios de salud detallados, use tecnología de IA para la evaluación de riesgo de hipertensión para obtener planes personalizados de control de presión arterial, recomendaciones dietéticas, orientación de ejercicios y sugerencias de ajuste de estilo de vida.", "alt": "Evaluación de salud de hipertensión - Sistema inteligente de análisis de riesgo de presión arterial", "keywords": "dieta DASH,alimentos para hipertensión,dieta saludable para hipertensión"}, "hero": {"aiLabel": "Evaluación de Salud con IA", "title": "Evaluación Hipertensiva", "subtitle": "Profesional · Inteligente · Personalizado", "description": "Basado en estándares médicos internacionales y análisis IA\nPlan de control de presión arterial personalizado para su salud cardiovascular", "ctaPrimary": "Iniciar evaluación", "ctaSecondary": "Detalles", "features": [{"title": "Evaluación precisa", "description": "Clasificación de riesgo según estándares clínicos"}, {"title": "Plan personalizado", "description": "Estrategias no farmacológicas adaptadas"}, {"title": "Monitoreo continuo", "description": "Seguimiento prolongado con ajustes dinámicos"}]}, "pyramid": {"title": "Pirámide de gestión de presión arterial", "subtitle": "Estructura científica de niveles para protección cardiovascular integral", "managementLevels": "Niveles de gestión", "fromAcuteToPrevention": "De agudo a prevención", "healthScore": "85 puntos", "healthScoreLabel": "Puntuación de salud", "levels": [{"level": 1, "label": "Fase aguda", "percentage": "80%"}, {"level": 2, "label": "Medicación", "percentage": "60%"}, {"level": 3, "label": "Estilo de vida", "percentage": "40%"}, {"level": 4, "label": "Monitoreo", "percentage": "20%"}, {"level": 5, "label": "Prevención", "percentage": "5%"}], "pyramidLevels": [{"title": "<PERSON><PERSON><PERSON>", "subtitle": "Control urgente y evaluación de riesgo", "level": "Nivel 1", "category": "Intervención crítica", "systolic": "≥180", "diastolic": "≥110", "intervention": "Inmediata", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Intervención médica"}, {"title": "Tratamiento farmacológico", "subtitle": "Protocolo estandarizado de antihipertensivos", "level": "Nivel 2", "category": "Intervención médica", "systolic": "160-179", "diastolic": "100-109", "intervention": "4-6 semanas", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Periodo de efecto"}, {"title": "Intervención de hábitos", "subtitle": "Ajustes dietéticos y conductuales", "level": "Nivel 3", "category": "Mejora conductual", "systolic": "140-159", "diastolic": "90-99", "intervention": "3-6 meses", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Periodo de mejora"}, {"title": "Monitoreo de salud", "subtitle": "Chequeos regulares y seguimiento", "level": "Nivel 4", "category": "Prevención y monitoreo", "systolic": "130-139", "diastolic": "85-89", "intervention": "<PERSON><PERSON><PERSON>", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Frecuencia de monitoreo"}, {"title": "Prevención de salud", "subtitle": "Educación y prevención de riesgos", "level": "Nivel 5", "category": "Prevención básica", "systolic": "<130", "diastolic": "<85", "intervention": "<PERSON><PERSON>", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Frecuencia de chequeos"}], "description": "Nuestro sistema IA elaborará un plan científico multinivel basado en su presión arterial y estado de salud", "stats": [{"number": "5 niveles", "label": "Niveles de gestión"}, {"number": "360°", "label": "Cobertura completa"}, {"number": "Personalizado", "label": "Plan adaptado"}, {"number": "IA inteligente", "label": "<PERSON><PERSON><PERSON><PERSON>í<PERSON>"}]}, "statistics": {"stats": [{"number": "50k+", "label": "Usuarios"}, {"number": "98%", "label": "Precisión"}, {"number": "30 días", "label": "Plan personalizado"}, {"number": "Profesional", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "assessment": {"title": "Comience su evaluación", "subtitle": "Obtenga en minutos un informe profesional de riesgo hipertensivo y plan de salud personalizado"}, "process": {"title": "Proceso de evaluación inteligente", "subtitle": "4 pasos simples para evaluación médica profesional de hipertensión", "steps": [{"title": "Datos básicos", "desc": "Complete información básica (edad, sexo, altura, peso)", "step": "01"}, {"title": "Presión arterial", "desc": "Registre sus mediciones actuales y recientes", "step": "02"}, {"title": "Historial médico", "desc": "Detalle antecedentes y tratamientos", "step": "03"}, {"title": "Análisis IA", "desc": "Generación de evaluación de riesgo y consejos personalizados", "step": "04"}]}, "advantages": {"title": "Por qué elegirnos", "subtitle": "Basado en últimas investigaciones médicas y guías clínicas para evaluación confiable y científica", "features": [{"title": "Análisis IA inteligente", "subtitle": "Modelo basado en medicina basada en evidencia", "description": "Uso de modelo internacional de evaluación de riesgo hipertensivo combinando algoritmo de aprendizaje automático para análisis multifactorial (edad, sexo, presión, antecedentes...) y clasificación precisa de riesgos.", "tags": ["Medicina basada en evidencia", "Análisis multifactorial", "Estándares internacionales"]}, {"title": "Plan personalizado", "subtitle": "Estrategia de gestión de salud adaptada", "description": "Elaboración de plan de control de presión personalizado según su estado de salud, hábitos y nivel de riesgo, incluyendo ajustes dietéticos, consejos de ejercicio y mejora de estilo de vida (medidas no farmacológicas).", "tags": ["Consejos nutricionales", "Recomendaciones de ejercicio", "Estilo de vida"]}], "highlights": [{"title": "Científicamente preciso", "description": "Basado en estándares médicos internacionales y datos clínicos"}, {"title": "Rápido y conveniente", "description": "Evaluación en 3-5 minutos con informe inmediato"}, {"title": "Profesional y confiable", "description": "Validado por equipo de expertos médicos garantizando calidad"}]}, "faq": {"title": "Preguntas frecuentes", "subtitle": "Respuestas a dudas comunes sobre evaluación hipertensiva", "items": [{"question": "¿Qué tan precisa es esta evaluación?", "answer": "Nuestra evaluación se basa en guías internacionales de evaluación de riesgo hipertensivo combinadas con análisis IA, ofreciendo alto valor indicativo. Con precisión superior al 98%, no reemplaza diagnóstico médico pero es referencia útil para discutir con su doctor."}, {"question": "¿Mis datos personales están seguros?", "answer": "Protegemos estrictamente su privacidad. Todos los datos de salud se cifran bajo estándares internacionales de protección, usados solo para su informe de evaluación y nunca compartidos con terceros o fines comerciales."}, {"question": "¿Qué incluye el informe de evaluación?", "answer": "El informe detallado incluye: (1) Evaluación e interpretación de nivel de riesgo (2) Análisis completo de su salud (3) Plan personalizado de control (4) Consejos nutricionales y de ejercicio científicos (5) Recomendaciones para mejorar estilo de vida (6) Plan de seguimiento."}, {"question": "¿Cuándo reevaluar?", "answer": "Recomendamos reevaluaciones periódicas según su control de presión. Durante ajustes activos de estilo de vida, evalúe mensualmente para seguir progresos. Si su presión es estable, evaluación trimestral o semestral basta para verificar eficacia del plan."}]}, "form": {"mainTitle": "Plan de Dieta Personalizado para Hipertensión", "subtitle": "¡Necesitamos el apoyo de sus datos de salud! Simplemente complete el cuestionario (aproximadamente 2 minutos), y podremos proporcionarle recomendaciones precisas de recetas bajas en sodio y altas en potasio para ayudarle a controlar la presión arterial de manera científica.", "step": "Paso", "ageLabel": "Edad", "years": "años", "prevButton": "Anterior", "nextButton": "Siguient<PERSON>", "submitButton": "Comenzar Personalización", "submittingButton": "Evaluando...", "loadExistingTitle": "Evaluación anterior encontrada", "loadExistingDesc": "Hemos encontrado un registro reciente de evaluación de salud, ¿desea cargarlo?", "loadExistingAction": "Cargar registro", "loadExistingCancel": "<PERSON><PERSON><PERSON> de nuevo", "submitSuccessTitle": "Evaluación exitosa", "submitSuccessDesc": "Su informe de salud ha sido generado.", "submitErrorAuthTitle": "Por favor inicie sesión primero", "submitErrorAuthDesc": "Por favor inicie sesión antes de realizar su evaluación de salud.", "submitErrorNetworkTitle": "Evaluación fallida", "submitErrorNetworkDesc": "El servidor tiene problemas, por favor intente de nuevo más tarde.", "questions": {"basicInfoTitle": "Información básica", "birthDate": {"title": "¿Cuál es su fecha de nacimiento?", "monthNames": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Abril", "Mayo", "<PERSON><PERSON>", "<PERSON>", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"], "daySuffix": ""}, "gender": {"title": "¿Cuál es su género?", "male": "Hombre", "female": "<PERSON><PERSON>", "non_binary": "No binario"}, "height": {"title": "¿Cuál es su altura?", "unit": "cm"}, "weight": {"title": "¿Cuál es su peso?", "unit": "kg", "label": "Peso", "range": "Rango: {min} - {max} kg"}, "bloodPressure": {"title": "¿Cuál es su presión arterial actual?", "description": "Deslice para ajustar la presión sistólica y diastólica", "systolicLabel": "Presión sistólica", "diastolicLabel": "Presión diastólica", "unknownBloodPressure": "¿No conoce su presión arterial?", "normalBloodPressure": "Presión Arterial Normal", "highBloodPressure": "Presión Arterial Alta", "lowBloodPressure": "Presión Arterial Baja"}, "medicalHistory": {"title": "¿Tiene algún problema de salud?", "familyHistory": "Antecedentes familiares de hipertensión", "diabetes": "Diabetes", "heartDisease": "Enfermedad <PERSON>", "kidneyDisease": "Enfermedad renal", "stroke": "<PERSON><PERSON><PERSON> cerebral", "cholesterol": "Colesterol alto"}, "exercise": {"title": "¿Cuántas horas hace ejercicio por semana en promedio?", "none": "Sin ejercicio", "light": "1-2 horas/semana", "moderate": "3-4 horas/semana", "intense": "5+ horas/semana"}, "salt": {"title": "¿Cómo es su consumo diario de sal?", "low": "<PERSON>co salado", "normal": "Moderadamente salado", "high": "<PERSON><PERSON>o"}, "stress": {"title": "¿Cómo es su nivel de estrés emocional reciente?", "low": "<PERSON><PERSON>", "moderate": "Moderado", "high": "Alto"}, "sleep": {"title": "¿Cuántas horas duerme al día?", "unit": "horas", "short": "No duermo suficiente 😴", "good": "Duermo bien 😊", "long": "Duermo mucho 😌", "range_4": "4 horas", "range_8": "8 horas", "range_12": "12 horas"}, "additionalInfo": {"title": "¿Tiene alguna información de salud adicional que agregar?", "description": "por ejemplo: otras enfermedades, alergias, medicamentos a largo plazo, etc.", "placeholder": "Ingrese aquí..."}, "badHabits": {"title": "Todos tenemos algunos malos hábitos. ¿Cuáles son los tuyos?", "smoking": "Fumar frecuentemente", "drinking": "Beber frecuentemente", "stayingUpLate": "Trasnochar frecuentemente", "overeating": "Comer en exceso"}}}, "result": {"title": "Informe de Evaluación de Riesgo de Hipertensión", "description": "Evaluación personalizada basada en su información de salud", "reassess": "<PERSON><PERSON><PERSON><PERSON>", "riskAssessment": "I. Evaluación del Nivel de Riesgo", "mainRiskFactors": "Principales Factores de Riesgo", "bmi": "Índice de IMC", "currentBloodPressure": "Presión Arterial Actual", "systolic": "Sistólica", "diastolic": "Diastólica", "healthAnalysis": "II. <PERSON><PERSON><PERSON><PERSON> del Estado de Salud", "currentSymptoms": "(1) Síntomas actuales inferidos", "managementAdvice": "Consejos de manejo", "noSymptoms": "No se detectaron s<PERSON>as notables", "organDamageRisk": "Riesgo de daño orgánico", "lowRisk": "<PERSON><PERSON><PERSON> bajo", "moderateRisk": "<PERSON><PERSON><PERSON> moderado", "highRisk": "Riesgo alto", "possibleOrgans": "Órganos potencialmente afectados", "possibleComplications": "Posibles complicaciones", "nonDrugPlan": "3. Plan de control no farmacológico", "dietAdjustment": "(1) <PERSON><PERSON><PERSON>s <PERSON>", "dietPlanLoading": "Personalizando según su región...", "customDietPlan": "Plan nutricional personalizado", "dietAdvice": "Consejos alimenticios", "dietRestriction": "Restricciones dietéticas", "dietPlan": "Plan dietético detallado", "exerciseIntervention": "(2) Programa de ejercicio", "exercisePlanLoading": "Generando...", "customExercisePlan": "Plan de ejercicio personalizado", "recommendedExercise": "Tipos de ejercicio recomendados", "frequency": "Frecuencia", "duration": "Duración", "precautions": "Precauciones", "lifestyleAdjustment": "(3) Ajustes de estilo de vida", "sleepManagement": "Manejo <PERSON>ño", "stressManagement": "Manejo del estrés", "habitAdjustment": "Modificación de hábitos", "followUpAdvice": "Consejos de seguimiento", "monitoringIndicators": "Indicadores de monitoreo", "checkupAdvice": "Recomendaciones de chequeo", "emergencyIndicators": "Señales de emergencia que requieren consulta", "reassessConfirm": "⚠️ ¿Confirmar reevaluación? Esto borrará sus planes personalizados (dieta y ejercicio).", "loading": "Cargando su evaluación..."}, "dietPlan": {"title": "Plan Dietético Personalizado para Hipertensión", "subtitle": "Programa alimenticio individualizado basado en evaluación de riesgos", "planTitle": "Plan Dietético para Hipertensión", "planDescription": "Programa alimenticio individualizado basado en evaluación de riesgos", "dailyMealPlan": "Planificación de comidas", "calendarTitle": "Calendario Dietético", "calendarDescription": "Consulte el plan completo de 30 días y seleccione una fecha", "noDietPlanData": "Sin datos para el día {day}", "planStartsFromDay": "Plan inicia {day}, duración {total} días", "noMonthPlan": "Sin plan este mes", "generatingProgress": "⏳ Generado {current}/30 días", "weekDays": ["Dom", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vie", "<PERSON><PERSON><PERSON>"], "today": "Hoy", "close": "<PERSON><PERSON><PERSON>", "nutritionTips": {"saltReduction": "Reducir sal disminuye presión", "balancedNutrition": "Dieta balanceada ayuda a controlar presión", "regularMeals": "Comidas regulares estabilizan presión"}, "foodCategories": {"staples": "Almidones", "protein": "<PERSON><PERSON><PERSON><PERSON>", "vegetables": "Vegetales", "fruits": "<PERSON><PERSON><PERSON>", "dairy": "Lácteos", "nuts": "<PERSON><PERSON><PERSON>"}, "dailyOverview": {"title": "Resumen nutricional diario", "nutritionMatch": "Balance nutricional", "totalCalories": "Calorías totales"}, "mealTimes": {"breakfast": "<PERSON><PERSON><PERSON>", "lunch": "<PERSON><PERSON><PERSON><PERSON>", "dinner": "<PERSON><PERSON>", "snack": "<PERSON><PERSON><PERSON>"}, "foodItems": {"oatmeal": "Avena", "boiledEgg": "<PERSON><PERSON> duro", "banana": "<PERSON><PERSON><PERSON><PERSON>", "apple": "Man<PERSON><PERSON>", "rice": "Arroz", "chicken": "Pollo", "fish": "Pescado", "vegetables": "Vegetales verdes", "tofu": "Tofu", "milk": "Leche", "yogurt": "<PERSON><PERSON><PERSON>"}, "nutritionInfo": {"calories": "Calorías", "kcal": "kcal", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carbohidratos", "fat": "<PERSON><PERSON><PERSON>", "fiber": "Fibra", "sodium": "Sodio", "potassium": "Potasio"}, "actions": {"deleteAndRegenerate": "Borrar y regenerar", "backToResults": "Volver a resultados", "downloadPlan": "<PERSON><PERSON><PERSON>", "sharePlan": "Compartir", "viewMore": "<PERSON>er más", "refresh": "Actualizar"}, "timeFormats": {"morning": "7:30-8:00", "noon": "12:00-13:00", "evening": "18:30-19:30"}, "portions": {"times1": "×1", "times2": "×2", "times3": "×3", "serving": "porción", "cup": "taza", "piece": "unidad", "bowl": "tazón"}, "dayNumber": "<PERSON><PERSON> {day}", "monthDay": "{day}/{month}", "combinedWith": ", adaptado a {country}", "regionalSpecialty": "Sugerencia regional", "clickTip": "📖 Click en platillos para ver preparación | 🍚Almidones 🥩Proteínas 🥬Vegetales 🍎Frutas 🥜Otros", "deleteConfirm": "¿Confirmar borrado del plan dietético? Podrá generarse uno nuevo.", "deleteSuccess": "Plan borrado - listo para nueva generación", "deleteFailed": "Error al borrar:", "unknownError": "Error descon<PERSON>", "networkError": "Error de red al borrar"}, "exercisePlan": {"refresh": "Actualizar", "calendarTitle": "Calendario de Ejercicio", "calendarDescription": "Consulte el programa completo de 30 días y seleccione una fecha", "planStartsFromDay": "Programa inicia {day}, duración {total} días", "noMonthPlan": "Sin programa este mes - consulte otros meses", "generatingProgress": "⏳ Generando: {current}/30 días (actualice para ver progreso)", "weekDays": ["Dom", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vie", "<PERSON><PERSON><PERSON>"], "today": "Hoy", "legend": {"today": "Hoy", "hasExercisePlan": "Con programa", "currentViewing": "Consultando"}, "noMonthExercise": "Sin programa este mes", "noMonthExerciseDescription": "Use flechas para consultar otros meses con programa", "exerciseArrangement": "Programa de ejercicio", "totalDuration": "Duración total", "intensity": {"low": "Baja", "medium": "Moderada", "high": "Alta"}, "heartRateControl": "Control de ritmo cardíaco", "targetHeartRate": "Objetivo:", "calculationMethod": "Mé<PERSON><PERSON> de cálculo:", "specificActions": "Ejercicios:", "duration": "Duración:", "sets": "Series:", "reps": "Repeticiones:", "precautions": "Precauciones", "requiredEquipment": "Equipo necesario:", "dailyTips": "Consejo del día", "exerciseGuidelines": "Recomendaciones", "exerciseContraindications": "Contraindicaciones", "progressiveAdvice": "Progresión recomendada", "generatedTime": "Generado el:", "clickTip": "🏃‍♂️ Click en ejercicios para instrucciones detalladas", "monthPlanTitle": "Programa de Ejercicio", "deleteConfirm": "¿Confirmar borrado del programa? Podrá generarse uno nuevo.", "deleteSuccess": "Programa borrado - listo para nueva generación", "deleteFailed": "Error al borrar:", "unknownError": "Error descon<PERSON>", "networkError": "Error de red al borrar", "deleteAndRegenerate": "🗑️ Borrar y regenerar", "backToResults": "← Volver a resultados", "close": "<PERSON><PERSON><PERSON>", "exerciseActionDialog": {"intensity": {"low": "Baja", "medium": "Moderada", "high": "Alta", "unknown": "Desconocida"}, "actionDescription": "Descripción", "detailedSteps": "Pasos detallados", "sets": "Series", "reps": "Repeticiones", "duration": "Duración", "heartRateControl": "Control de ritmo cardíaco", "targetHeartRate": "Objetivo:", "calculationMethod": "Mé<PERSON><PERSON> de cálculo:", "monitoringMethod": "Mé<PERSON>do de monitoreo:", "tips": "Consejos", "precautions": "Precauciones", "requiredEquipment": "Equipo necesario"}}, "toast": {"unlockFullDietPlan": "💡 Desbloquee plan completo (30 días)", "unlockFullDietPlanDesc": "Solo ve día 1. Suscríbase para acceder a 30 días personalizados y alcanzar sus metas de salud!", "unlockFullExercisePlan": "💡 Desbloquee programa completo (30 días)", "unlockFullExercisePlanDesc": "Solo ve día 1. Suscríbase para acceder a 30 días de consejos de ejercicio científicos!", "subscribe": "Suscribirse", "dietPlanUpdated": "✅ ¡Plan dietético actualizado!", "dietPlanUpdatedDesc": "{count} días añadidos - total {total}/30 días.", "exercisePlanUpdated": "✅ ¡Programa de ejercicio actualizado!", "exercisePlanUpdatedDesc": "{count} días añadidos - total {total}/30 días.", "stillGenerating": "⏳ Generando...", "dietPlanGeneratingDesc": "Su plan dietético se está generando - {completed}/30 días listos - vuelva más tarde.", "exercisePlanGeneratingDesc": "Su programa de ejercicio se está generando - {completed}/30 días listos - vuelva más tarde.", "planIsLatest": "👍 Plan actualizado", "dietPlanLatestDesc": "Su plan dietético está completo ({total}/30 días).", "exercisePlanLatestDesc": "Su programa de ejercicio está completo ({total}/30 días).", "noPlanToRefresh": "ℹ️ Nada que actualizar", "noDietPlanDesc": "Genere primero un plan dietético.", "noExercisePlanDesc": "Genere primero un programa de ejercicio.", "generateDietPlanFailed": "Error al generar", "generateExercisePlanFailed": "Error al generar", "networkError": "Error de red - reintente", "refreshFailed": "<PERSON><PERSON>r al actualizar - reintente", "dietPlanGenerating": "📋 Generando", "dietPlanGeneratingInProgress": "Su plan dietético se está generando ({completed}/30 días) - espere o actualice luego!", "exercisePlanGenerating": "🏃‍♂️ Generando", "exercisePlanGeneratingInProgress": "Su programa de ejercicio se está generando ({completed}/30 días) - espere o actualice luego!", "assessmentFailed": "Error en evaluación", "networkErrorRetry": "Error de red - reintente"}, "riskLevels": {"low": "<PERSON><PERSON>", "moderate": "Moderado", "high": "Alto", "very_high": "<PERSON><PERSON> alto", "unknown": "Desconocido"}, "foodCategories": {"staples": "Almidones", "protein": "<PERSON><PERSON><PERSON><PERSON>", "vegetables": "Vegetales", "fruits": "<PERSON><PERSON><PERSON>", "others": "<PERSON><PERSON><PERSON>", "nutritionMatch": "🍽️ Balance nutricional", "viewRecipe": "Ver receta"}, "foodRecipe": {"basicInfo": "Información básica", "category": "Categoría:", "quantity": "Cantidad:", "calories": "Calorías:", "ingredients": "Ingredientes", "steps": "Preparación", "tips": "Consejos clave", "nutritionValue": "Valor nutricional", "difficulty": {"easy": "F<PERSON><PERSON>l", "medium": "Medio", "hard": "Dif<PERSON><PERSON>l"}, "kcal": "kcal"}}
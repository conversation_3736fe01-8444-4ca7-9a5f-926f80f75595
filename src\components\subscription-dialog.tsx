'use client';
import { useState, useEffect } from 'react';
import { findSubscriptionByUserId } from '@/actions/user-order';
import { SubscriptionLocal, ToastLocal } from "@/types/locales/billing";
import { X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast"

interface Subscription {
    orderId: string;
    credits: string;
    price: string;
    date: string;
    renewalDate: string;
}

interface ManageSubscriptionDialogProps {
    i18n?: SubscriptionLocal;
    toastLocal: ToastLocal;
    onClose: () => void;
    open: boolean;
}

export function ManageSubscriptionDialog({ i18n, toastLocal, onClose, open }: ManageSubscriptionDialogProps) {
    const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
    const [loading, setLoading] = useState(true);
    const [confirmDialog, setConfirmDialog] = useState<{ show: boolean; orderId: string }>({
        show: false,
        orderId: ''
    });
    const [cancellingOrderId, setCancellingOrderId] = useState<string>('');
    const [isConfirming, setIsConfirming] = useState(false);
    const { toast } = useToast()

    useEffect(() => {
        const fetchSubscriptions = async () => {
            try {
                const data = await findSubscriptionByUserId();
                setSubscriptions(data || []);
            } catch (error) {
                console.error('Failed to fetch subscription data:', error);
                toast({
                    title: toastLocal.error?.title,
                    description: toastLocal?.error?.cancelSubscription,
                    variant: "destructive",
                    duration: 3000,
                    position: "top-center",
                  })
            } finally {
                setLoading(false);
            }
        };

        if (open) {
            fetchSubscriptions();
        }
    }, [open]);

    const handleCancelClick = (orderId: string) => {
        setCancellingOrderId(orderId);
        setConfirmDialog({ show: true, orderId });
    };

    const handleConfirmCancel = async () => {
        setIsConfirming(true);
        try {
            const subscriptionId = confirmDialog.orderId;
            const response = await fetch('/api/subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ subscriptionId }),
            });

            if (response.status === 200) {
                setSubscriptions(subscriptions.filter(sub => sub.orderId !== confirmDialog.orderId));
                toast({
                    title: toastLocal.success?.title,
                    description: toastLocal.success?.cancelSubscription,
                    duration: 3000,
                    position: "top-center",
                  })
            } else {
                const errorData = await response.json();
                toast({
                    title: toastLocal.error?.title,
                    description: toastLocal.error?.cancelSubscription,
                    variant: "destructive",
                    duration: 3000,
                    position: "top-center",
                  })
            }
        } catch (error) {
            console.error('Failed to cancel subscription:', error);
            toast({
                title: toastLocal.error?.title,
                description: toastLocal.error?.cancelSubscription,
                variant: "destructive",
                duration: 3000,
                position: "top-center",
              })
        } finally {
            setIsConfirming(false);
            setCancellingOrderId('');
            setConfirmDialog({ show: false, orderId: '' });
        }
    };

    const cancelHandle = () => {
        setIsConfirming(false);
        setCancellingOrderId('');
        setConfirmDialog({ show: false, orderId: '' });
    }
    return (
        <>
            <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                            {i18n?.description}
                        </DialogTitle>
                    </DialogHeader>
                    
                    <div className="border border-primary-gold/20 dark:border-primary-gold/30 rounded-lg p-6 max-h-[80vh] overflow-y-auto">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                            {i18n?.title}
                        </h2>
                        <Separator className="my-4 bg-primary-gold/20 dark:bg-primary-gold/30" />
                        
                        <div className="grid grid-rows-1 md:grid-cols-1 gap-4">
                            {loading ? (
                                <div className="flex flex-col items-center justify-center w-full py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-4 border-primary-gold border-t-transparent"></div>
                                    <p className="mt-4 text-gray-600 dark:text-gray-300">Loading...</p>
                                </div>
                            ) : subscriptions.length > 0 ? (
                                subscriptions.map((subs, index) => (
                                    <div key={index} className="space-y-4 py-4 border-b border-primary-gold/20 dark:border-primary-gold/30 last:border-b-0">
                                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {i18n?.currentPlan?.planName.replace('[0]', subs.credits)}
                                        </h4>
                                        <dl className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <dt className="text-sm text-gray-500 dark:text-gray-400">{i18n?.currentPlan?.billingCycle}</dt>
                                                <dd className="text-base font-medium text-gray-900 dark:text-white">
                                                    ${subs.price} / {Number(subs.price) === 7.99 ? i18n?.currentPlan?.monthlyUnit : i18n?.currentPlan?.annualUnit}
                                                </dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm text-gray-500 dark:text-gray-400">{i18n?.currentPlan?.subscriptionStart}</dt>
                                                <dd className="text-base font-medium text-gray-900 dark:text-white">
                                                    {new Date(subs.date).toLocaleString()}
                                                </dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm text-gray-500 dark:text-gray-400">{i18n?.currentPlan?.nextRenewal}</dt>
                                                <dd className="text-base font-medium text-gray-900 dark:text-white">
                                                    {new Date(subs.renewalDate).toLocaleDateString()}
                                                </dd>
                                            </div>
                                        </dl>
                                        <div className="pt-2">
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => handleCancelClick(subs.orderId)}
                                                disabled={cancellingOrderId === subs.orderId}
                                                className="flex items-center"
                                            >
                                                {cancellingOrderId === subs.orderId ? (
                                                    <>
                                                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                                                        {i18n?.currentPlan?.cancelButton}
                                                    </>
                                                ) : (
                                                    <>
                                                        <X className="h-4 w-4 mr-2" />
                                                        {i18n?.currentPlan?.cancelButton}
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="flex justify-center items-center py-8">
                                    <p className="text-gray-700 dark:text-gray-400">{i18n?.currentPlan?.noSubscriptions}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
    
            {confirmDialog.show && (
                <Dialog open={confirmDialog.show} onOpenChange={(open) => !open && cancelHandle()}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                                {i18n?.cancelDialog?.title}
                            </DialogTitle>
                        </DialogHeader>
                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                            {i18n?.cancelDialog?.description}
                        </p>
                        <div className="flex justify-end space-x-4">
                            <Button
                                variant="outline"
                                onClick={cancelHandle}
                                disabled={isConfirming}
                                className="border-primary-gold/20 dark:border-primary-gold/30"
                            >
                                {i18n?.cancelDialog?.cancelButton}
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={handleConfirmCancel}
                                disabled={isConfirming}
                                className="flex items-center"
                            >
                                {isConfirming && (
                                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                                )}
                                {i18n?.cancelDialog?.confirmButton}
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            )}
        </>
    );
}
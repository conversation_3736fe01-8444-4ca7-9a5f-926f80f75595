import { Users, Globe2, Mail} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { type Locale, getPathname, generateAlternates  } from "@/i18n-config"
import { getDictionary, i18nNamespaces } from '@/i18n'
import { Contact } from "@/types/locales/contact"
import { ContactForm } from "./contact-form"
import { host } from '@/config/config'

export async function generateMetadata({ params }: { params: { lang: Locale } }) {
  const { lang } = await params
  const alternates = generateAlternates(lang, '/contact');
  const i18nContact = await getDictionary<Contact>(lang, i18nNamespaces.contact);
  return {
    title: i18nContact.meta.title,
    description: i18nContact.meta.description,
    keywords: i18nContact.meta.keywords,
    twitter: {
      card: "summary_large_image", title: i18nContact.meta.title,
      description: i18nContact.meta.description
    },
    openGraph: {
      type: "website",
      url: `${host}${getPathname(lang, '')}`,
      title: i18nContact.meta.title,
      description: i18nContact.meta.description,
      siteName: "Hypertension"
    },
    alternates: {
      canonical: `${host}${getPathname(lang, '')}`,
      languages: alternates
    }
  }
}

export default async function Page({ params }: { params: { lang: Locale } }) {

  const { lang } = await params
  const i18nContact = await getDictionary<Contact>(lang, i18nNamespaces.contact)

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-background to-background/80">
      <main className="flex-1">
        <div className="container py-12 md:py-16">
          <div className="mx-auto max-w-[58rem] space-y-4 text-center">
            <h1 className="font-heading text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:text-6xl lg:leading-[1.1] bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
              {i18nContact.hero.title||"联系AI动物识别团队"}
            </h1>
            <p className="mx-auto max-w-[46rem] text-muted-foreground sm:text-lg">
              {i18nContact.hero.description||"需要动物识别技术支持？联系我们的专家团队获取识别咨询、技术帮助和动物保护建议。"}
            </p>
          </div>

          <div className="mx-auto mt-12 grid max-w-5xl gap-8 md:grid-cols-2">
            <Card className="border-2 border-primary/20 transition-all duration-300 hover:border-primary/40 hover:shadow-xl dark:bg-background/95">
              <CardHeader>
                <CardTitle className="text-2xl">{i18nContact.info.title||"联系信息"}</CardTitle>
                <CardDescription className="text-base">{i18nContact.info.description||"我们的联系方式和地址，方便您随时联系我们。"}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {i18nContact.info.contacts.map((contact, index) => (
                  <div key={index} className="flex items-center gap-3 text-muted-foreground">
                    {index === 0 && <Mail className="h-5 w-5 text-primary" />}
                    {index === 1 && <Globe2 className="h-5 w-5 text-primary" />}
                    {index === 2 && <Users className="h-5 w-5 text-primary" />}
                    <span>{contact.text||"联系我们"}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card className="md:order-2 border-2 border-primary/20 transition-all duration-300 hover:border-primary/40 hover:shadow-xl dark:bg-background/95">
              <CardHeader>
                <CardTitle className="text-2xl">{i18nContact.form.title||"联系我们"}</CardTitle>
                <CardDescription className="text-base">
                  {i18nContact.form.description||"请填写以下信息，我们将尽快回复您。"}
                </CardDescription>
              </CardHeader>
              <CardContent>
              <ContactForm formLabels={i18nContact.form}/>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
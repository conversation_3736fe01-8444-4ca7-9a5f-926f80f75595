import { sql } from '@/lib/postgres-client'

export interface HealthAssessmentReport {
  id?: number
  user_id: string
  audience_group: string
  form_data: any
  user_data_summary: string
  report_data: any
  basic_analysis?: string
  tcm_constitution?: string
  health_risks?: string
  diet_recommendations?: string
  herbal_recommendations?: string
  lifestyle_adjustments?: string
  short_term_goals?: string
  long_term_plan?: string
  ip_address?: string
  created_at?: Date
  updated_at?: Date
}

/**
 * 保存健康评估报告到数据库（UPSERT：如果用户已有报告则更新，否则插入新记录）
 */
export async function saveHealthAssessmentReport(data: HealthAssessmentReport): Promise<number> {
  // 首先检查用户是否已有报告
  const existingReport = await getLatestHealthAssessmentReport(data.user_id)
  
  if (existingReport) {
    // 用户已有报告，更新现有记录
    const { rows } = await sql`
      UPDATE nf_health_assessment_reports 
      SET 
        audience_group = ${data.audience_group},
        form_data = ${JSON.stringify(data.form_data)},
        user_data_summary = ${data.user_data_summary},
        report_data = ${JSON.stringify(data.report_data)},
        basic_analysis = ${data.basic_analysis},
        tcm_constitution = ${data.tcm_constitution},
        health_risks = ${data.health_risks},
        diet_recommendations = ${data.diet_recommendations},
        herbal_recommendations = ${data.herbal_recommendations},
        lifestyle_adjustments = ${data.lifestyle_adjustments},
        short_term_goals = ${data.short_term_goals},
        long_term_plan = ${data.long_term_plan},
        ip_address = ${data.ip_address},
        updated_at = NOW()
      WHERE user_id = ${data.user_id}
      RETURNING id
    `
    
    console.log('=== 更新现有健康评估报告 ===', data.user_id)
    return rows[0].id
  } else {
    // 用户没有报告，插入新记录
    const { rows } = await sql`
      INSERT INTO nf_health_assessment_reports (
        user_id,
        audience_group,
        form_data,
        user_data_summary,
        report_data,
        basic_analysis,
        tcm_constitution,
        health_risks,
        diet_recommendations,
        herbal_recommendations,
        lifestyle_adjustments,
        short_term_goals,
        long_term_plan,
        ip_address,
        created_at,
        updated_at
      ) VALUES (
        ${data.user_id},
        ${data.audience_group},
        ${JSON.stringify(data.form_data)},
        ${data.user_data_summary},
        ${JSON.stringify(data.report_data)},
        ${data.basic_analysis},
        ${data.tcm_constitution},
        ${data.health_risks},
        ${data.diet_recommendations},
        ${data.herbal_recommendations},
        ${data.lifestyle_adjustments},
        ${data.short_term_goals},
        ${data.long_term_plan},
        ${data.ip_address},
        NOW(),
        NOW()
      )
      RETURNING id
    `
    
    console.log('=== 创建新健康评估报告 ===', data.user_id)
    return rows[0].id
  }
}

/**
 * 根据用户ID获取最新的健康评估报告
 */
export async function getLatestHealthAssessmentReport(userId: string): Promise<HealthAssessmentReport | null> {
  const { rows } = await sql`
    SELECT * FROM nf_health_assessment_reports 
    WHERE user_id = ${userId}
    ORDER BY created_at DESC 
    LIMIT 1
  `
  
  if (rows.length === 0) {
    return null
  }
  
  const row = rows[0]
  return {
    id: row.id,
    user_id: row.user_id,
    audience_group: row.audience_group,
    form_data: row.form_data,
    user_data_summary: row.user_data_summary,
    report_data: row.report_data,
    basic_analysis: row.basic_analysis,
    tcm_constitution: row.tcm_constitution,
    health_risks: row.health_risks,
    diet_recommendations: row.diet_recommendations,
    herbal_recommendations: row.herbal_recommendations,
    lifestyle_adjustments: row.lifestyle_adjustments,
    short_term_goals: row.short_term_goals,
    long_term_plan: row.long_term_plan,
    ip_address: row.ip_address,
    created_at: row.created_at,
    updated_at: row.updated_at
  }
}

/**
 * 检查用户是否已有健康评估报告
 */
export async function hasHealthAssessmentReport(userId: string): Promise<boolean> {
  const { rows } = await sql`
    SELECT COUNT(*) as count FROM nf_health_assessment_reports 
    WHERE user_id = ${userId}
  `
  
  return parseInt(rows[0].count) > 0
}

/**
 * 删除用户的健康评估报告
 */
export async function deleteHealthAssessmentReport(userId: string): Promise<boolean> {
  const { rowCount } = await sql`
    DELETE FROM nf_health_assessment_reports 
    WHERE user_id = ${userId}
  `
  
  return (rowCount || 0) > 0
}

/**
 * 更新健康评估报告 - 简化版本，仅更新主要字段
 */
export async function updateHealthAssessmentReport(userId: string, data: Partial<HealthAssessmentReport>): Promise<boolean> {
  // 为了简化，只支持更新report_data字段
  if (!data.report_data) {
    return false
  }
  
  const { rowCount } = await sql`
    UPDATE nf_health_assessment_reports 
    SET 
      report_data = ${JSON.stringify(data.report_data)},
      updated_at = NOW()
    WHERE user_id = ${userId}
  `
  
  return (rowCount || 0) > 0
} 
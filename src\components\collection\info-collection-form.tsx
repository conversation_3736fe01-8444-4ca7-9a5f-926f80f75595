'use client'

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { audienceGroups } from '@/components/collection/audience-group-selector';
import WeightLossForm from '@/components/collection/weight-loss-form';
import FitnessHobbyForm from '@/components/collection/fitness-hobby-form';
import UrbanWorkForm from '@/components/collection/urban-work-form';

// 信息采集表单主组件
export default function InfoCollectionForm({ lang }: { lang: string }) {
  const router = useRouter();
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  
  // 从localStorage获取用户选择的群体
  useEffect(() => {
    const storedGroup = localStorage.getItem('selected_audience_group');
    if (storedGroup) {
      setSelectedGroup(storedGroup);
    }
    setLoading(false);
  }, []);
  
  // 如果没有选择群体，重定向回主页
  useEffect(() => {
    if (!loading && !selectedGroup) {
      // 在实际应用中，您可能想要重定向回受众群体选择页面
      router.push(`/${lang}`);
    }
  }, [loading, selectedGroup, router, lang]);
  
  if (loading) {
    return <div className="text-center">加载中...</div>;
  }
  
  // 根据用户选择的群体展示不同的表单
  switch (selectedGroup) {
    case 'weight-loss':
      return <WeightLossForm lang={lang} />;
    case 'fitness':
      return <FitnessHobbyForm lang={lang} />;
    case 'white-collar':
      return <UrbanWorkForm lang={lang} />;
    default:
      return <div className="text-center">未找到对应的表单</div>;
  }
}

// 健身爱好者信息采集表单（示例）
function FitnessForm({ lang }: { lang: string }) {
  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">健身营养信息采集</h2>
      <p className="text-sm text-gray-500 mb-6">
        请提供以下信息，以便我们为您制定符合训练需求的营养饮食计划
      </p>
      
      {/* 这里放置实际的表单字段 */}
      <div className="space-y-4">
        <p className="text-center text-gray-500">表单字段将根据需求进一步开发</p>
      </div>
      
      <div className="mt-6 flex justify-end">
        <Button>保存并继续</Button>
      </div>
    </Card>
  );
} 
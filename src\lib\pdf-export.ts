import { ShoppingList, ShoppingListItem, FoodCategoryNames, HerbalCategoryNames, FoodCategory, HerbalCategory } from '@/types/shopping-list'

/**
 * 导出购物清单为PDF
 * 注意：这个实现使用浏览器的打印功能来生成PDF
 * 在生产环境中，您可能需要使用专门的PDF库如jsPDF或puppeteer
 */
export function exportShoppingListToPDF(shoppingList: ShoppingList) {
  // 创建一个新的窗口用于打印
  const printWindow = window.open('', '_blank')
  
  if (!printWindow) {
    alert('请允许弹出窗口以导出PDF')
    return
  }

  // 按类别分组
  const foodItems = shoppingList.items.filter(item => item.type === 'food')
  const herbalItems = shoppingList.items.filter(item => item.type === 'herbal')

  // 按类别进一步分组
  const groupedFoodItems = groupItemsByCategory(foodItems, 'food')
  const groupedHerbalItems = groupItemsByCategory(herbalItems, 'herbal')

  // 生成HTML内容
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>购物清单 - ${new Date().toLocaleDateString()}</title>
      <style>
        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #4F46E5;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .header h1 {
          color: #4F46E5;
          margin: 0;
          font-size: 28px;
        }
        .header .date {
          color: #666;
          font-size: 14px;
          margin-top: 5px;
        }
        .summary {
          background: #F3F4F6;
          padding: 15px;
          border-radius: 8px;
          margin-bottom: 30px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .summary-item {
          text-align: center;
        }
        .summary-item .number {
          font-size: 24px;
          font-weight: bold;
          color: #4F46E5;
        }
        .summary-item .label {
          font-size: 12px;
          color: #666;
          margin-top: 5px;
        }
        .section {
          margin-bottom: 40px;
        }
        .section-title {
          font-size: 20px;
          font-weight: bold;
          color: #1F2937;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 1px solid #E5E7EB;
        }
        .category {
          margin-bottom: 25px;
        }
        .category-title {
          font-size: 16px;
          font-weight: bold;
          color: #374151;
          margin-bottom: 10px;
          background: #F9FAFB;
          padding: 8px 12px;
          border-left: 4px solid #10B981;
        }
        .items-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 10px;
        }
        .item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          border: 1px solid #E5E7EB;
          border-radius: 6px;
          background: white;
        }
        .item.purchased {
          background: #F0FDF4;
          border-color: #10B981;
          opacity: 0.7;
        }
        .item-name {
          font-weight: 500;
          flex: 1;
        }
        .item.purchased .item-name {
          text-decoration: line-through;
          color: #6B7280;
        }
        .item-quantity {
          color: #6B7280;
          font-size: 14px;
          margin-left: 10px;
        }
        .item-sources {
          font-size: 12px;
          color: #9CA3AF;
          margin-top: 2px;
        }
        .checkbox {
          width: 16px;
          height: 16px;
          border: 2px solid #D1D5DB;
          border-radius: 3px;
          margin-right: 8px;
          display: inline-block;
          position: relative;
        }
        .checkbox.checked {
          background: #10B981;
          border-color: #10B981;
        }
        .checkbox.checked::after {
          content: '✓';
          color: white;
          font-size: 12px;
          position: absolute;
          top: -2px;
          left: 2px;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          color: #6B7280;
          font-size: 12px;
          border-top: 1px solid #E5E7EB;
          padding-top: 20px;
        }
        @media print {
          body {
            margin: 0;
            padding: 10px;
            font-size: 12px;
            line-height: 1.3;
          }
          .header {
            page-break-after: avoid;
            margin-bottom: 15px;
          }
          .header h1 {
            font-size: 20px;
            margin: 0 0 5px 0;
          }
          .summary {
            page-break-after: avoid;
            margin-bottom: 15px;
            padding: 10px;
          }
          .summary-item .number {
            font-size: 16px;
          }
          .summary-item .label {
            font-size: 10px;
          }
          .section {
            margin-bottom: 12px;
            page-break-inside: auto;
            break-inside: auto;
          }
          .section-title {
            font-size: 14px;
            margin-bottom: 8px;
            page-break-after: avoid;
            orphans: 3;
          }
          .category {
            margin-bottom: 8px;
            page-break-inside: auto;
            break-inside: auto;
          }
          .category-title {
            font-size: 13px;
            margin-bottom: 6px;
            padding: 4px 8px;
            page-break-after: avoid;
            orphans: 2;
          }
          .items-grid {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 4px !important;
            column-gap: 8px !important;
          }
          .item {
            display: flex !important;
            margin-bottom: 0;
            page-break-inside: avoid;
            font-size: 11px;
            padding: 4px 6px;
            border: 1px solid #ddd;
            break-inside: avoid;
            width: 100%;
            box-sizing: border-box;
          }
          .item-sources {
            font-size: 9px;
            margin-top: 1px;
          }
          .item-quantity {
            font-size: 10px;
          }
          .checkbox {
            width: 12px;
            height: 12px;
            margin-right: 4px;
          }
          .footer {
            page-break-before: avoid;
            margin-top: 15px;
            font-size: 10px;
            padding-top: 8px;
          }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🛒 购物清单</h1>
        <div class="date">生成时间：${new Date().toLocaleString()}</div>
      </div>

      <div class="summary">
        <div class="summary-item">
          <div class="number">${shoppingList.total_items}</div>
          <div class="label">总项目</div>
        </div>
        <div class="summary-item">
          <div class="number">${foodItems.length}</div>
          <div class="label">食材项目</div>
        </div>
        <div class="summary-item">
          <div class="number">${herbalItems.length}</div>
          <div class="label">药材项目</div>
        </div>
        <div class="summary-item">
          <div class="number">${shoppingList.purchased_items}</div>
          <div class="label">已购买</div>
        </div>
      </div>

      ${foodItems.length > 0 ? `
        <div class="section">
          <div class="section-title">🥬 食材清单</div>
          ${Object.entries(groupedFoodItems).map(([category, items]) => `
            <div class="category">
              <div class="category-title">${FoodCategoryNames[category as FoodCategory]}</div>
              <div class="items-grid">
                ${items.map(item => `
                  <div class="item ${item.is_purchased ? 'purchased' : ''}">
                    <div>
                      <span class="checkbox ${item.is_purchased ? 'checked' : ''}"></span>
                      <span class="item-name">${item.name}</span>
                      <span class="item-quantity">${item.quantity}</span>
                      ${item.source_dishes && item.source_dishes.length > 0 ? `
                        <div class="item-sources">用于: ${item.source_dishes.join(', ')}</div>
                      ` : ''}
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>
      ` : ''}

      ${herbalItems.length > 0 ? `
        <div class="section">
          <div class="section-title">🌿 药材清单</div>
          ${Object.entries(groupedHerbalItems).map(([category, items]) => `
            <div class="category">
              <div class="category-title">${HerbalCategoryNames[category as HerbalCategory]}</div>
              <div class="items-grid">
                ${items.map(item => `
                  <div class="item ${item.is_purchased ? 'purchased' : ''}">
                    <div>
                      <span class="checkbox ${item.is_purchased ? 'checked' : ''}"></span>
                      <span class="item-name">${item.name}</span>
                      <span class="item-quantity">${item.quantity}</span>
                      ${item.source_formulas && item.source_formulas.length > 0 ? `
                        <div class="item-sources">用于: ${item.source_formulas.join(', ')}</div>
                      ` : ''}
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>
      ` : ''}

      <div class="footer">
        <p>此购物清单由AI智能健康管理系统生成</p>
        <p>建议购买时核对食材新鲜度，药材请到正规药店购买</p>
      </div>
    </body>
    </html>
  `

  // 写入HTML内容
  printWindow.document.write(htmlContent)
  printWindow.document.close()

  // 等待内容加载完成后打印
  printWindow.onload = () => {
    setTimeout(() => {
      printWindow.print()
      // 打印完成后关闭窗口
      printWindow.onafterprint = () => {
        printWindow.close()
      }
    }, 500)
  }
}

/**
 * 按类别分组项目
 */
function groupItemsByCategory(items: ShoppingListItem[], type: 'food' | 'herbal'): Record<string, ShoppingListItem[]> {
  const grouped: Record<string, ShoppingListItem[]> = {}

  items.forEach(item => {
    const category = item.category as string
    if (!grouped[category]) {
      grouped[category] = []
    }
    grouped[category].push(item)
  })

  return grouped
}

/**
 * 发送购物清单到邮箱
 * 注意：这需要后端邮件服务支持
 */
export async function emailShoppingList(shoppingList: ShoppingList, email: string) {
  try {
    const response = await fetch('/api/email-shopping-list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        shoppingList,
        email
      })
    })

    if (!response.ok) {
      throw new Error('发送邮件失败')
    }

    return { success: true }
  } catch (error) {
    console.error('发送购物清单邮件失败:', error)
    return { success: false, error: error instanceof Error ? error.message : '发送失败' }
  }
}

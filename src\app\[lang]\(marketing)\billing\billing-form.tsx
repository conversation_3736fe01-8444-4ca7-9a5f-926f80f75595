'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { useSession } from "next-auth/react";
import { findSubscriptionByUserId, findOrderByUserId } from '@/actions/user-order';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { XCircle, CheckCircle2, AlertCircle } from "lucide-react"
import { Billing } from "@/types/locales/billing";
import { cn } from "@/lib/utils"
import { Separator } from "@/components/ui/separator"

interface Subscription {
  orderId: string;
  credits: string;
  price: string;
  date: string;
  renewalDate: string;
}

interface Transaction {
  orderId: string;
  credits: string;
  price: string;
  date: string;
  type: string;
  status: string;
}

export function BillingForm({ i18n }: { i18n: Billing }) {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [cancellingOrderId, setCancellingOrderId] = useState<string>('');
  const { toast } = useToast()
  const { data: session, status } = useSession();

  useEffect(() => {
    const loadData = async () => {
      if (status === 'loading') {
        return;
      }

      if (!session?.user) {
        setLoading(false);
        return;
      }

      try {
        const [subsData, transData] = await Promise.all([
          findSubscriptionByUserId(),
          findOrderByUserId()
        ]);

        setSubscriptions(subsData || []);
        setTransactions(transData || []);
      } catch (error) {
        console.error('Failed to fetch data:', error);
        toast({
          title: i18n.toast.error.title,
          description: i18n.toast.error.loadData,
          variant: "destructive",
          duration: 3000,
          position: "top-center",
        })
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [session?.user, status]);

  const handleCancelSubscription = async (subscriptionId: string) => {
    setCancellingOrderId(subscriptionId);
    try {
      const response = await fetch('/api/subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ subscriptionId }),
      });

      if (response.ok) {
        setSubscriptions(prev => prev.filter(sub => sub.orderId !== subscriptionId));
        toast({
          title: i18n.toast.success.title,
          description: i18n.toast.success.cancelSubscription,
          duration: 3000,
          position: "top-center",
        })
      } else {
        throw new Error('Failed to cancel subscription');
      }
    } catch (error) {
      toast({
        title: i18n.toast.error.title,
        description: i18n.toast.error.cancelSubscription,
        variant: "destructive",
        duration: 3000,
        position: "top-center",
      })
    } finally {
      setCancellingOrderId('');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };


  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-4 border-primary-gold border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Subscriptions Section */}
      <Card className="border-primary-gold/20">
        <CardHeader className='px-4 md:px-6'>
          <CardTitle>{i18n.subscription.title}</CardTitle>
          <CardDescription>{i18n.subscription.description}</CardDescription>
        </CardHeader>
        <div className='px-4 md:px-6'>
          <Separator className='bg-primary-gold/20' />
        </div>

        {subscriptions.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-6 px-3 py-6 md:p-6">
            {subscriptions.map((sub, index) => (
              <div key={index} className="border border-primary-gold/20 rounded-lg p-6 space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-4">
                    {i18n.subscription.currentPlan.planName.replace('[0]', sub.credits)}
                  </h3>

                  <div className="space-y-4">
                    <div className="flex flex-col gap-1">
                      <span className="text-muted-foreground text-sm">{i18n.subscription.currentPlan.billingCycle}</span>
                      <span className="text-xl font-semibold">${sub.price} / {Number(sub.price) === 7.99 ? i18n.subscription.currentPlan.monthlyUnit : i18n.subscription.currentPlan.annualUnit}</span>
                    </div>

                    <div className="flex flex-col gap-1">
                      <span className="text-muted-foreground text-sm">{i18n.subscription.currentPlan.subscriptionStart}</span>
                      <span className="text-base">{new Date(sub.date).toLocaleString()}</span>
                    </div>

                    <div className="flex flex-col gap-1">
                      <span className="text-muted-foreground text-sm">{i18n.subscription.currentPlan.nextRenewal}</span>
                      <span className="text-base">{new Date(sub.renewalDate).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      className="w-full mt-4"
                      disabled={cancellingOrderId === sub.orderId}
                    >
                      {cancellingOrderId === sub.orderId ? (
                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                      ) : null}
                      {i18n.subscription.currentPlan.cancelButton}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>{i18n.subscription.cancelDialog.title}</AlertDialogTitle>
                      <AlertDialogDescription>
                        {i18n.subscription.cancelDialog.description}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{i18n.subscription.cancelDialog.cancelButton}</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleCancelSubscription(sub.orderId)}
                      >
                        {i18n.subscription.cancelDialog.confirmButton}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            ))}
          </div>
        ) : (
          <CardContent className="flex items-center justify-center py-8">
            <p className="text-muted-foreground">{i18n.subscription.currentPlan.noSubscriptions}</p>
          </CardContent>
        )}
      </Card>

      {/* Transactions Section */}
      <Card className="border-primary-gold/20">
        <CardHeader>
          <CardTitle>{i18n.transaction.title}</CardTitle>
          <CardDescription>{i18n.transaction.description}</CardDescription>
        </CardHeader>
        <div className='px-4 md:px-6'>
          <div className="border-t border-primary-gold/20">
            <div className="divide-y divide-primary-gold/20">
              {transactions.length > 0 ? (
                transactions.map((trans, index) => (
                  <div key={index}
                    className="flex flex-wrap items-center gap-y-4 py-6"
                  >
                    {/* Product Name */}
                    <dl className="w-1/2 sm:w-1/4 lg:w-auto lg:flex-1">
                      <dt className="text-sm text-muted-foreground">
                        {i18n.transaction.productName}
                      </dt>
                      <dd className="mt-1.5 font-medium">
                        {trans.credits} {i18n.transaction.unit}
                      </dd>
                    </dl>

                    {/* Price */}
                    <dl className="w-1/2 sm:w-1/4 lg:w-auto lg:flex-1">
                      <dt className="text-sm text-muted-foreground">
                        {i18n.transaction.price}
                      </dt>
                      <dd className="mt-1.5 font-medium">
                        ${trans.price}
                      </dd>
                    </dl>

                    {/* Date */}
                    <dl className="w-1/2 sm:w-1/4 lg:w-auto lg:flex-1">
                      <dt className="text-sm text-muted-foreground">
                        {i18n.transaction.date}
                      </dt>
                      <dd className="mt-1.5 font-medium">
                        {new Date(trans.date).toLocaleString()}
                      </dd>
                    </dl>

                    {/* Status */}
                    <dl className="w-1/2 sm:w-1/4 lg:w-auto lg:flex-1">
                      <dt className="text-sm text-muted-foreground">
                        {i18n.transaction.status}
                      </dt>
                      <dd className="mt-1.5">
                        <span className={cn(
                          "px-2 py-1 text-sm font-medium rounded-full",
                          trans.status.toLowerCase() === 'completed' && "bg-green-100 text-green-700",
                          trans.status.toLowerCase() === 'failed' && "bg-red-100 text-red-700",
                          trans.status.toLowerCase() === 'pending' && "bg-yellow-100 text-yellow-700"
                        )}>
                          {trans.status}
                        </span>
                      </dd>
                    </dl>
                  </div>
                ))
              ) : (
                <CardContent className="flex items-center justify-center py-8">
                  <p className="text-muted-foreground">{i18n.transaction.noTransactions}</p>
                </CardContent>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
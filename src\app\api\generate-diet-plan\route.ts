import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { NORMAL_DIET_PLAN_PROMPT, HERBAL_DIET_PLAN_PROMPT } from '@/actions/diet-constants'
import { saveDietPlan } from '@/lib/db/diet-plan'
import { getLatestHealthAssessmentReport } from '@/lib/db/health-assessment'
import { DietPlan, DietPlanGeneration } from '@/types/diet-plan'

export async function POST(request: NextRequest) {
  try {
    const { userId, includeHerbalPlan } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: '缺少用户ID' },
        { status: 400 }
      )
    }

    // 获取用户的健康评估报告
    const healthReport = await getLatestHealthAssessmentReport(userId)
    if (!healthReport) {
      return NextResponse.json(
        { error: '未找到用户健康评估报告，请先完成健康评估' },
        { status: 404 }
      )
    }

    // 检查用户是否愿意接受药膳计划
    // 只有当用户选择药膳计划，且有饮食建议和中药建议时，才使用药膳计划
    const shouldUseHerbalPlan = includeHerbalPlan && 
                               healthReport.diet_recommendations && 
                               healthReport.herbal_recommendations

    // 根据计划类型选择提示词和构建参数
    let fullPrompt: string
    let planType: 'normal' | 'herbal'

    if (shouldUseHerbalPlan) {
      // 使用中医药膳计划
      planType = 'herbal'
      fullPrompt = HERBAL_DIET_PLAN_PROMPT
        .replace('{basicAnalysis}', healthReport.basic_analysis || '')
        .replace('{dietRecommendations}', healthReport.diet_recommendations || '')
        .replace('{herbalRecommendations}', healthReport.herbal_recommendations || '')
    } else {
      // 使用普通饮食计划
      planType = 'normal'
      fullPrompt = NORMAL_DIET_PLAN_PROMPT
        .replace('{basicAnalysis}', healthReport.basic_analysis || '')
        .replace('{dietRecommendations}', healthReport.diet_recommendations || '')
    }

    // 调用通义千问API
    const apiKey = process.env.DASHSCOPE_API_KEY
    if (!apiKey) {
      throw new Error('未配置DASHSCOPE API密钥')
    }

    const startTime = Date.now()

    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: process.env.VISION_MODEL || 'qwen-plus-latest',
        input: {
          messages: [
            {
              role: 'user',
              content: fullPrompt
            }
          ]
        },
        parameters: {
          max_tokens: 12000,
          temperature: 0.7,
          top_p: 0.8,
        }
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('通义千问API错误:', errorData)
      throw new Error(`通义千问API调用失败: ${response.status}`)
    }

    const data = await response.json()
    
    // 打印AI返回的JSON结果和消耗时间
    const endTime = Date.now()
    const duration = endTime - startTime
    console.log('=== AI返回的饮食计划结果 ===')
    console.log(JSON.stringify(data, null, 2))
    console.log('=== 消耗时间 ===')
    console.log(`${duration}ms`)

    if (!data.output?.text) {
      throw new Error('AI返回数据格式错误')
    }

    // 检查是否因为长度限制被截断
    if (data.output?.finish_reason === 'length') {
      console.warn('⚠️ AI返回内容可能因长度限制被截断，正在尝试解析...')
      console.warn('返回文本长度:', data.output.text.length)
    }

    // 解析AI返回的饮食计划
    const dietPlanData = parseAIDietPlan(data.output.text)
    
    if (!dietPlanData) {
      throw new Error('无法解析AI生成的饮食计划')
    }

    // 构建数据库存储对象
    const dietPlan: DietPlan = {
      user_id: userId,
      plan_type: dietPlanData.planType,
      weekly_plan: dietPlanData.weeklyPlan,
      special_notes: dietPlanData.specialNotes,
      tcm_principles: dietPlanData.tcmPrinciples,
      basic_analysis: healthReport.basic_analysis || '',
      diet_recommendations: healthReport.diet_recommendations || '',
      herbal_recommendations: shouldUseHerbalPlan ? healthReport.herbal_recommendations : undefined
    }

    // 保存到数据库
    const dietPlanId = await saveDietPlan(dietPlan)
    dietPlan.id = dietPlanId

    console.log('=== 饮食计划生成完成 ===')
    console.log('用户ID:', userId)
    console.log('计划类型:', planType)
    console.log('数据库ID:', dietPlanId)

    return NextResponse.json({
      success: true,
      dietPlan: dietPlan
    })

  } catch (error) {
    console.error('饮食计划生成错误:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '饮食计划生成失败'
      },
      { status: 500 }
    )
  }
}

/**
 * 解析AI返回的饮食计划文本
 */
function parseAIDietPlan(responseText: string): DietPlanGeneration | null {
  try {
    console.log('开始解析AI返回的文本...')
    console.log('原始文本长度:', responseText.length)
    
    // 方法1: 尝试提取```json代码块
    const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/)
    if (jsonMatch && jsonMatch[1]) {
      console.log('找到JSON代码块，开始解析...')
      const jsonStr = jsonMatch[1].trim()
      console.log('提取的JSON字符串长度:', jsonStr.length)
      
      const parsed = JSON.parse(jsonStr)
      
      // 验证必要字段
      if (!parsed.planType || !parsed.weeklyPlan || !parsed.specialNotes) {
        console.error('饮食计划数据缺少必要字段:', parsed)
        return null
      }
      
      console.log('JSON解析成功，计划类型:', parsed.planType)
      return parsed as DietPlanGeneration
    }

    // 方法2: 尝试查找第一个{到最后一个}之间的内容
    const firstBrace = responseText.indexOf('{')
    const lastBrace = responseText.lastIndexOf('}')
    
    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      console.log('尝试提取大括号间的JSON内容...')
      const jsonStr = responseText.substring(firstBrace, lastBrace + 1)
      console.log('提取的JSON字符串:', jsonStr.substring(0, 200) + '...')
      
      const parsed = JSON.parse(jsonStr)
      
      // 验证必要字段
      if (!parsed.planType || !parsed.weeklyPlan || !parsed.specialNotes) {
        console.error('饮食计划数据缺少必要字段:', parsed)
        return null
      }
      
      console.log('JSON解析成功，计划类型:', parsed.planType)
      return parsed as DietPlanGeneration
    }

    // 方法3: 尝试修复截断的JSON
    console.log('尝试修复可能截断的JSON...')
    const lastBraceIndex = responseText.lastIndexOf('}')
    if (lastBraceIndex > firstBrace) {
      // 尝试在最后一个}处截断，然后补全
      let truncatedJson = responseText.substring(firstBrace, lastBraceIndex + 1)
      
      // 检查是否缺少结尾的}
      const openBraces = (truncatedJson.match(/{/g) || []).length
      const closeBraces = (truncatedJson.match(/}/g) || []).length
      
      if (openBraces > closeBraces) {
        console.log(`缺少${openBraces - closeBraces}个结束括号，尝试补全...`)
        for (let i = 0; i < openBraces - closeBraces; i++) {
          truncatedJson += '}'
        }
      }
      
      try {
        const parsed = JSON.parse(truncatedJson)
        
        // 验证必要字段
        if (!parsed.planType || !parsed.weeklyPlan || !parsed.specialNotes) {
          console.error('修复后的饮食计划数据缺少必要字段:', parsed)
          return null
        }
        
        console.log('JSON修复成功，计划类型:', parsed.planType)
        return parsed as DietPlanGeneration
      } catch (repairError) {
        console.error('修复JSON也失败了:', repairError)
      }
    }

    // 方法4: 如果都失败了，记录详细信息
    console.error('无法从AI返回文本中提取有效JSON')
    console.error('文本开头:', responseText.substring(0, 500))
    console.error('文本结尾:', responseText.substring(Math.max(0, responseText.length - 500)))
    
    return null
  } catch (error) {
    console.error('解析饮食计划JSON失败:', error)
    console.error('错误详情:', error instanceof Error ? error.message : '未知错误')
    console.error('原始文本前500字符:', responseText.substring(0, 500))
    return null
  }
} 
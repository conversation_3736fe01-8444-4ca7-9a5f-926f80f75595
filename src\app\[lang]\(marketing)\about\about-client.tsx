"use client";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Clock, User, Eye } from "lucide-react"
import { useState, useMemo } from "react";

// 博客文章数据
// const blogPosts = [...]

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  readTime: string;
  publishDate: string;
  author: string;
  authorRole: string;
  image: string;
  tags: string[];
}



const POSTS_PER_PAGE = 6;

export default function AboutClient({ i18n, lang }: { i18n: any, lang: string }) {
  const [activeCategory, setActiveCategory] = useState("all");
  const [displayCount, setDisplayCount] = useState(POSTS_PER_PAGE);

  const blogPosts: BlogPost[] = i18n.blogPosts;

  const categories = [
    { name: i18n.categories.all, slug: "all" },
    { name: i18n.categories.technology, slug: "technology" },
    { name: i18n.categories.conservation, slug: "conservation" },
    { name: i18n.categories.marine, slug: "marine" },
    { name: i18n.categories.behavior, slug: "behavior" },
    { name: i18n.categories.monitoring, slug: "monitoring" }
  ];

  const filteredPosts = useMemo(() => {
    if (activeCategory === "all") {
      return blogPosts;
    }
    const categoryName = categories.find(c => c.slug === activeCategory)?.name;
    return blogPosts.filter((post: BlogPost) => post.category === categoryName);
  }, [activeCategory, categories, blogPosts]);

  const displayedPosts = useMemo(() => {
    return filteredPosts.slice(0, displayCount);
  }, [filteredPosts, displayCount]);

  const hasMorePosts = displayCount < filteredPosts.length;

  const handleCategoryClick = (categorySlug: string) => {
    setActiveCategory(categorySlug);
    setDisplayCount(POSTS_PER_PAGE);
  };

  const handleLoadMore = () => {
    setDisplayCount(prev => prev + POSTS_PER_PAGE);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/80">
      {/* Header Section */}
      <section className="container px-4 py-16">
        <div className="text-center space-y-6 max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
            {i18n.hero.title||"AI动物识别博客"}
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground">
            {i18n.hero.description || "探索有关AI动物识别技术、野生动物保护和生态科普的精彩内容"}
          </p>
          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3 mt-8">
            {categories.map((category) => (
              <Button
                key={category.slug}
                variant={activeCategory === category.slug ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategoryClick(category.slug)}
                className={`transition-all duration-200 ${
                  activeCategory === category.slug 
                    ? "bg-primary text-primary-foreground" 
                    : "hover:bg-primary/10"
                }`}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="container px-4 pb-16">
        {filteredPosts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground"> {i18n.hero.noPosts || "该分类下暂无文章"}</p>
          </div>
        ) : (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {displayedPosts.map((post: BlogPost) => (
              <Card key={post.id} className="group border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 overflow-hidden">
                {/* Article Image */}
                <div className="relative h-48 overflow-hidden">
                  <div className="absolute top-3 left-3 z-10">
                    <span className="px-3 py-1 text-xs font-medium bg-primary text-primary-foreground rounded-full">
                      {post.category}
                    </span>
                  </div>
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      // 如果图片加载失败，显示fallback
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                  {/* Fallback 图标 */}
                  <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center" style={{display: 'none'}}>
                    <Eye className="w-12 h-12 text-primary/60" />
                  </div>
                </div>

                {/* Article Content */}
                <CardHeader className="space-y-3">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{post.readTime}</span>
                    </div>
                    <span>{post.publishDate}</span>
                  </div>
                  <CardTitle className="text-xl leading-tight group-hover:text-primary transition-colors">
                    <Link href={`/${lang}/about/${post.id}`} className="hover:underline">
                      {post.title}
                    </Link>
                  </CardTitle>
                  <CardDescription className="text-sm leading-relaxed">
                    {post.excerpt}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0 space-y-4">
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2">
                    {post.tags.slice(0, 3).map((tag: string, index: number) => (
                      <span key={index} className="text-xs px-2 py-1 bg-accent text-accent-foreground rounded-md">
                        {tag}
                      </span>
                    ))}
                  </div>
                  {/* Author Info */}
                  <div className="flex items-center gap-3 pt-3 border-t border-border/50">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-primary" />
                    </div>
                    <div className="text-sm">
                      <p className="font-medium text-foreground">{post.author}</p>
                      <p className="text-muted-foreground text-xs">{post.authorRole}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
        {/* Load More Button */}
        {hasMorePosts && (
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg" 
              className="px-8"
              onClick={handleLoadMore}
            >
              {i18n.hero.loadMore.replace('{count}', (filteredPosts.length - displayCount).toString())}
            </Button>
          </div>
        )}
        {/* All Loaded Message */}
        {!hasMorePosts && filteredPosts.length > POSTS_PER_PAGE && (
          <div className="text-center mt-12">
            <p className="text-muted-foreground"> {i18n.hero.allLoaded|| "已显示所有文章"}</p>
          </div>
        )}
      </section>
    </div>
  )
} 
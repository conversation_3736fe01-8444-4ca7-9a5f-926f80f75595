{"meta": {"title": "Hypertension Health Assessment - Hypertension Diet Menu | Hypertension Exercise Guide & Blood Pressure Diet Plan", "description": "Through detailed health questionnaires, use AI technology for hypertension risk assessment to get personalized blood pressure control plans, dietary recommendations, exercise guidance, and lifestyle adjustment suggestions.", "alt": "Hypertension Health Assessment - Intelligent Blood Pressure Risk Analysis System", "keywords": "DASH diet,hypertension foods,healthy diet for hypertension"}, "hero": {"aiLabel": "AI Smart Health Assessment", "title": "Hypertension Health Assessment", "subtitle": "Professional · Intelligent · Personalized Blood Pressure Management Solutions", "description": "Based on international medical standards, combined with AI intelligent analysis technology, providing you with scientifically accurate hypertension risk assessment\nTailored blood pressure control plans to comprehensively protect your cardiovascular health", "ctaPrimary": "Start Health Assessment", "ctaSecondary": "Learn About Assessment Details", "features": [{"title": "Professional Risk Assessment", "description": "Hypertension risk classification assessment based on clinical standards, scientifically accurate"}, {"title": "Personalized Solutions", "description": "Tailored non-pharmacological treatment control plans, safe and effective"}, {"title": "Continuous Monitoring", "description": "Long-term tracking of blood pressure changes, dynamic adjustment of management strategies"}]}, "pyramid": {"title": "Blood Pressure Health Management Pyramid", "subtitle": "Scientific blood pressure management hierarchy to help you build a comprehensive cardiovascular health protection system", "managementLevels": "Management Levels", "fromAcuteToPrevention": "From Acute to Prevention", "healthScore": "85", "healthScoreLabel": "Health Management Score", "levels": [{"level": 1, "label": "Acute Phase", "percentage": "80%"}, {"level": 2, "label": "Medication", "percentage": "60%"}, {"level": 3, "label": "Lifestyle", "percentage": "40%"}, {"level": 4, "label": "Monitoring", "percentage": "20%"}, {"level": 5, "label": "Prevention", "percentage": "5%"}], "pyramidLevels": [{"title": "Acute Phase Management", "subtitle": "Emergency blood pressure control and risk assessment", "level": "Level 1", "category": "Crisis Intervention", "systolic": "≥180", "diastolic": "≥110", "intervention": "Immediate", "systolicUnit": "Systolic mmHg", "diastolicUnit": "Diastolic mmHg", "interventionUnit": "Medical Intervention"}, {"title": "Medication Therapy", "subtitle": "Standardized antihypertensive medication protocols", "level": "Level 2", "category": "Medical Intervention", "systolic": "160-179", "diastolic": "100-109", "intervention": "4-6 weeks", "systolicUnit": "Systolic mmHg", "diastolicUnit": "Diastolic mmHg", "interventionUnit": "Effect Period"}, {"title": "Lifestyle Intervention", "subtitle": "Diet, exercise and behavioral adjustments", "level": "Level 3", "category": "Behavioral Improvement", "systolic": "140-159", "diastolic": "90-99", "intervention": "3-6 months", "systolicUnit": "Systolic mmHg", "diastolicUnit": "Diastolic mmHg", "interventionUnit": "Improvement Period"}, {"title": "Health Monitoring", "subtitle": "Regular check-ups and data tracking", "level": "Level 4", "category": "Monitoring Prevention", "systolic": "130-139", "diastolic": "85-89", "intervention": "Monthly", "systolicUnit": "Systolic mmHg", "diastolicUnit": "Diastolic mmHg", "interventionUnit": "Monitoring Frequency"}, {"title": "Preventive Healthcare", "subtitle": "Health education and risk prevention", "level": "Level 5", "category": "Basic Prevention", "systolic": "<130", "diastolic": "<85", "intervention": "Annual", "systolicUnit": "Systolic mmHg", "diastolicUnit": "Diastolic mmHg", "interventionUnit": "Check-up Frequency"}], "description": "Our AI assessment system will develop a multi-level scientific management plan based on your blood pressure levels and health status", "stats": [{"number": "5 Levels", "label": "Management Hierarchy"}, {"number": "360°", "label": "Comprehensive Coverage"}, {"number": "Personalized", "label": "Customized Solutions"}, {"number": "AI Smart", "label": "Scientific Analysis"}]}, "statistics": {"stats": [{"number": "50K+", "label": "User Trust"}, {"number": "98%", "label": "Assessment Accuracy"}, {"number": "30 Days", "label": "Personalized Plans"}, {"number": "Professional", "label": "Medical Standards"}]}, "assessment": {"title": "Start Your Health Assessment", "subtitle": "In just a few minutes, get professional hypertension risk assessment reports and personalized health management plans"}, "process": {"title": "Smart Assessment Process", "subtitle": "Simple four steps to obtain professional medical-grade hypertension health assessment", "steps": [{"title": "Basic Information", "desc": "Fill in basic health information such as age, gender, height and weight", "step": "01"}, {"title": "Blood Pressure Data", "desc": "Record current blood pressure values and recent blood pressure changes", "step": "02"}, {"title": "Health Status", "desc": "Detailed understanding of medical history, family history and medication use", "step": "03"}, {"title": "AI Analysis", "desc": "Generate professional risk assessment and personalized health recommendations", "step": "04"}]}, "advantages": {"title": "Why Choose Us", "subtitle": "Based on the latest medical research and clinical guidelines, providing scientifically reliable health assessment services", "features": [{"title": "AI Smart Analysis", "subtitle": "Evidence-based medical assessment model", "description": "Using internationally standardized hypertension risk assessment models, combined with machine learning algorithms, comprehensively analyzing multi-dimensional factors such as age, gender, blood pressure values, medical history, and family history to provide accurate risk classification assessment.", "tags": ["Evidence-Based Medicine", "Multi-Factor Analysis", "International Standards"]}, {"title": "Personalized Solutions", "subtitle": "Tailored health management strategies", "description": "Based on individual health status, lifestyle habits and risk levels, develop personalized blood pressure management plans, including dietary adjustments, exercise guidance, lifestyle improvements and other non-pharmacological interventions.", "tags": ["Dietary Guidance", "Exercise Recommendations", "Lifestyle"]}], "highlights": [{"title": "Scientifically Accurate", "description": "Based on international medical standards and clinical research data"}, {"title": "Fast and Convenient", "description": "Complete assessment in 3-5 minutes, generate reports instantly"}, {"title": "Professional and Reliable", "description": "Medical expert team review to ensure assessment quality"}]}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Common questions and answers about hypertension health assessment", "items": [{"question": "How accurate is this assessment?", "answer": "Our assessment is based on internationally standardized hypertension risk assessment guidelines, combined with AI intelligent analysis technology, and has high reference value. The assessment accuracy rate is over 98%, but please note that this cannot replace professional physician diagnosis. We recommend using the assessment results as an important reference for communication with doctors."}, {"question": "Is my personal information secure?", "answer": "We strictly protect your privacy and security. All health data is processed with end-to-end encryption, complying with international medical data protection standards, and is only used to generate your personal assessment reports. We will never share with any third party or use for commercial purposes."}, {"question": "What does the assessment report include?", "answer": "The detailed assessment report includes: (1) Hypertension risk level assessment and interpretation (2) Comprehensive analysis of personal health status (3) Personalized blood pressure control plan (4) Scientific dietary and exercise guidance (5) Lifestyle improvement recommendations (6) Regular monitoring and follow-up plan."}, {"question": "How often can I reassess?", "answer": "We recommend regular reassessment based on blood pressure control status. If actively adjusting lifestyle, we suggest monthly assessment to monitor progress; for stable blood pressure control, reassessment every 3-6 months to ensure the effectiveness of management plans."}]}, "form": {"mainTitle": "Customized Diet Plan for Hypertension", "subtitle": "We need your health data support! Simply fill out the questionnaire (about 2 minutes), and we can provide you with precise recommendations for low-sodium, high-potassium recipes to help you control blood pressure scientifically.", "step": "Step", "ageLabel": "Age", "years": "years", "prevButton": "Previous Step", "nextButton": "Next Step", "submitButton": "Start Customization", "submittingButton": "Assessing...", "loadExistingTitle": "Historical Assessment Found", "loadExistingDesc": "We found a recent health assessment record. Would you like to load it?", "loadExistingAction": "Load Record", "loadExistingCancel": "Fill Out Again", "submitSuccessTitle": "Assessment Successful", "submitSuccessDesc": "Your health report has been generated.", "submitErrorAuthTitle": "Please Login First", "submitErrorAuthDesc": "Please log in before conducting your health assessment.", "submitErrorNetworkTitle": "Assessment Failed", "submitErrorNetworkDesc": "Server is having issues, please try again later.", "questions": {"basicInfoTitle": "Basic Information", "birthDate": {"title": "What is your date of birth?", "monthNames": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "daySuffix": ""}, "age": {"title": "What is your age?"}, "gender": {"title": "What is your gender?", "male": "Male", "female": "Female", "non_binary": "Non-binary"}, "height": {"title": "What is your height?", "unit": "cm"}, "weight": {"title": "What is your weight?", "unit": "kg", "label": "Weight", "range": "Range: {min} - {max} kg"}, "bloodPressure": {"title": "What is your current blood pressure?", "description": "Drag the sliders to adjust systolic and diastolic pressure", "systolicLabel": "Systolic", "diastolicLabel": "Diastolic", "unknownBloodPressure": "Don't know your blood pressure?", "normalBloodPressure": "Normal Blood Pressure", "highBloodPressure": "High Blood Pressure", "lowBloodPressure": "Low Blood Pressure"}, "medicalHistory": {"title": "Do you have any health issues?", "familyHistory": "Family history of hypertension", "diabetes": "Diabetes", "heartDisease": "Heart Disease", "kidneyDisease": "Kidney Disease", "stroke": "Stroke", "cholesterol": "High Cholesterol"}, "exercise": {"title": "How many hours do you exercise per week on average?", "none": "No exercise", "light": "1-2 hours/week", "moderate": "3-4 hours/week", "intense": "5+ hours/week"}, "salt": {"title": "What is your daily salt intake?", "low": "Light flavor", "normal": "Moderate flavor", "high": "Salty flavor"}, "stress": {"title": "What is your recent emotional stress level?", "low": "Low stress", "moderate": "Moderate stress", "high": "High stress"}, "sleep": {"title": "How many hours do you sleep per day?", "unit": "hours", "short": "Insufficient sleep 😴", "good": "Sufficient sleep 😊", "long": "Sufficient sleep 😌", "range_4": "4 hours", "range_8": "8 hours", "range_12": "12 hours"}, "additionalInfo": {"title": "Do you have any other health information to add?", "description": "e.g., other diseases, allergies, long-term medications, etc.", "placeholder": "Please enter here..."}, "badHabits": {"title": "We all have some bad habits. What are yours?", "smoking": "Frequent smoking", "drinking": "Frequent drinking", "stayingUpLate": "Frequently staying up late", "overeating": "Overeating"}}, "result": {"title": "Health Assessment Report", "subtitle": "Based on your health data, we have generated the following comprehensive assessment report.", "regenerateButton": "Regenerate Report", "regeneratingButton": "Generating...", "personalInfo": "Personal Information", "riskAnalysis": "Hypertension Risk Analysis", "riskLevel": "Risk Level", "riskFactors": "Main Risk Factors", "riskDescription": "Risk Interpretation", "nonDrugTreatment": "Non-pharmacological Treatment Plan", "dietPlan": "Diet Plan", "exercisePlan": "Exercise Plan", "lifestyleAdjustment": "Lifestyle Adjustment", "dietManagement": "Diet Management", "exerciseManagement": "Exercise Management", "sleepManagement": "Sleep Management", "stressManagement": "Stress Management", "monitoringSuggestions": "Monitoring and Follow-up Suggestions", "bloodPressureMonitoring": "Blood Pressure Monitoring", "regularCheckups": "Regular Checkups", "disclaimer": "Disclaimer", "disclaimerContent": "This report is generated by AI and is for reference only. It cannot replace a professional physician's diagnosis. Please consult a doctor for professional medical advice.", "dietCategories": {"staple": "Staple Food", "meat": "Meat", "vegetable": "Vegetables", "fruit": "Fruits", "dairy": "Dairy", "nuts": "Nuts", "oil": "Oils"}, "meals": {"breakfast": "Breakfast", "lunch": "Lunch", "dinner": "Dinner"}, "exerciseCategories": {"aerobic": "Aerobic Exercise", "strength": "Strength Training", "flexibility": "Flexibility Training"}}}, "result": {"title": "Hypertension Risk Assessment Report", "description": "Personalized assessment based on your health information", "reassess": "Reassess", "riskAssessment": "I. Risk Level Assessment", "mainRiskFactors": "Main Risk Factors", "bmi": "BMI Index", "currentBloodPressure": "Current Blood Pressure", "systolic": "Systolic", "diastolic": "Diastolic", "healthAnalysis": "II. Health Status Analysis", "currentSymptoms": "(1) Current Symptoms Inference", "managementAdvice": "Management Advice", "noSymptoms": "No obvious symptoms found yet", "organDamageRisk": "Organ Damage Risk", "lowRisk": "Low Risk", "moderateRisk": "Moderate Risk", "highRisk": "High Risk", "possibleOrgans": "Possible Affected Organs", "possibleComplications": "Potential Complications", "nonDrugPlan": "3. Non-Pharmacological Control Plan", "dietAdjustment": "(1) Diet Adjustment", "dietPlanLoading": "Customizing based on your region...", "customDietPlan": "Customize Diet Plan", "dietAdvice": "Diet Advice", "dietRestriction": "Diet Restrictions", "dietPlan": "Specific Diet Plan", "exerciseIntervention": "(2) Exercise Intervention", "exercisePlanLoading": "Generating...", "customExercisePlan": "Customize Exercise Plan", "recommendedExercise": "Recommended Exercise Types", "frequency": "Frequency", "duration": "Duration", "precautions": "Precautions", "lifestyleAdjustment": "(3) Lifestyle Adjustment", "sleepManagement": "Sleep Management", "stressManagement": "Stress Management", "habitAdjustment": "Habit Adjustment", "followUpAdvice": "Follow-up Advice", "monitoringIndicators": "Monitoring Indicators", "checkupAdvice": "Checkup Advice", "emergencyIndicators": "Emergency Indicators", "reassessConfirm": "⚠️ Reassessment will clear all your customized plans (including diet and exercise plans). Are you sure you want to continue?", "loading": "Loading your health assessment..."}, "dietPlan": {"title": "Hypertension Patient Customized Diet Plan", "subtitle": "Personalized dietary plan based on risk assessment", "planTitle": "Hypertension Patient Customized Diet Plan", "planDescription": "Personalized dietary plan based on risk assessment", "dailyMealPlan": "Meal Plan", "calendarTitle": "Diet Plan Calendar", "calendarDescription": "View the complete 30-day diet plan and select the date you want to view.", "noDietPlanData": "No diet plan data available for day {day}.", "planStartsFromDay": "Plan starts from day {day}, total {total} days", "noMonthPlan": "No plan this month", "generatingProgress": "⏳ Generated {current}/30 days", "weekDays": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "today": "Today", "close": "Close", "nutritionTips": {"saltReduction": "Reducing salt intake can lower blood pressure", "balancedNutrition": "Balanced nutrition helps control blood pressure", "regularMeals": "Regular meals help stabilize blood pressure"}, "foodCategories": {"staples": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "vegetables": "Vegetables", "fruits": "Fruits", "dairy": "Dairy", "nuts": "Nuts"}, "dailyOverview": {"title": "Daily Nutrition Overview", "nutritionMatch": "Nutrition Match", "totalCalories": "Total Calories"}, "mealTimes": {"breakfast": "Breakfast", "lunch": "Lunch", "dinner": "Dinner", "snack": "Snack"}, "foodItems": {"oatmeal": "Oatmeal", "boiledEgg": "Boiled Egg", "banana": "Banana", "apple": "Apple", "rice": "Rice", "chicken": "Chicken", "fish": "Fish", "vegetables": "Vegetables", "tofu": "Tofu", "milk": "Milk", "yogurt": "Yogurt"}, "nutritionInfo": {"calories": "Calories", "kcal": "kcal", "protein": "<PERSON><PERSON>", "carbs": "Carbohydrates", "fat": "Fat", "fiber": "Fiber", "sodium": "Sodium", "potassium": "Potassium"}, "actions": {"deleteAndRegenerate": "Delete and Regenerate", "backToResults": "Back to Results", "downloadPlan": "Download Plan", "sharePlan": "Share Plan", "viewMore": "View More Diet Plans", "refresh": "Refresh"}, "timeFormats": {"morning": "7:30-8:00", "noon": "12:00-13:00", "evening": "18:30-19:30"}, "portions": {"times1": "×1", "times2": "×2", "times3": "×3", "serving": "serving", "cup": "cup", "piece": "piece", "bowl": "bowl"}, "dayNumber": "Day {day}", "monthDay": "{month}/{day}", "combinedWith": ", combined with {country}", "regionalSpecialty": " regional specialty", "clickTip": "📖 Click any dish to view detailed recipes | 🍚Staples 🥩Protein 🥬Vegetables 🍎Fruits 🥜Others", "deleteConfirm": "Are you sure you want to delete the current diet plan? You can generate a new diet plan after deletion.", "deleteSuccess": "Diet plan deleted, you can generate a new plan now", "deleteFailed": "Delete failed:", "unknownError": "Unknown error", "networkError": "Delete failed: Network error"}, "exercisePlan": {"refresh": "Refresh", "calendarTitle": "Exercise Calendar", "calendarDescription": "View the complete 30-day exercise plan and select the date you want to view.", "planStartsFromDay": "Exercise plan starts from day {day}, total {total} days", "noMonthPlan": "No exercise plan this month, check other months", "generatingProgress": "⏳ Generating, completed {current}/30 days (click refresh for latest progress)", "weekDays": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "today": "Today", "legend": {"today": "Today", "hasExercisePlan": "Has exercise plan", "currentViewing": "Currently viewing"}, "noMonthExercise": "No exercise plan this month", "noMonthExerciseDescription": "You can click the left and right arrows to switch to months with exercise plans", "exerciseArrangement": "Exercise Arrangement", "totalDuration": "Total Duration", "intensity": {"low": "Low Intensity", "medium": "Medium Intensity", "high": "High Intensity"}, "heartRateControl": "Heart Rate Control", "targetHeartRate": "Target Heart Rate:", "calculationMethod": "Calculation Method:", "specificActions": "Specific Actions:", "duration": "Duration:", "sets": "Sets:", "reps": "Reps:", "precautions": "Precautions", "requiredEquipment": "Required Equipment:", "dailyTips": "Daily Tips", "exerciseGuidelines": "Exercise Guidelines", "exerciseContraindications": "Exercise Contraindications", "progressiveAdvice": "Progressive Advice", "generatedTime": "Generated Time:", "clickTip": "🏃‍♂️ Click any exercise item to view detailed action instructions", "monthPlanTitle": "Exercise Plan", "deleteConfirm": "Are you sure you want to delete the current exercise plan? You can generate a new plan after deletion.", "deleteSuccess": "Exercise plan deleted, you can generate a new plan now", "deleteFailed": "Delete failed:", "unknownError": "Unknown error", "networkError": "Delete failed: Network error", "deleteAndRegenerate": "🗑️ Delete and Regenerate", "backToResults": "← Back to Results", "close": "Close", "exerciseActionDialog": {"intensity": {"low": "Low Intensity", "medium": "Medium Intensity", "high": "High Intensity", "unknown": "Unknown"}, "actionDescription": "Action Description", "detailedSteps": "Detailed Steps", "sets": "Sets", "reps": "Reps", "duration": "Duration", "heartRateControl": "Heart Rate Control", "targetHeartRate": "Target Heart Rate: ", "calculationMethod": "Calculation Method: ", "monitoringMethod": "Monitoring Method: ", "tips": "Tips", "precautions": "Precautions", "requiredEquipment": "Required Equipment"}}, "toast": {"unlockFullDietPlan": "💡 Unlock Full 30-Day Diet Plan", "unlockFullDietPlanDesc": "You are currently viewing Day 1 plan. Become a subscriber to unlock all 30 days of personalized content to help you achieve your health goals!", "unlockFullExercisePlan": "💡 Unlock Full 30-Day Exercise Plan", "unlockFullExercisePlanDesc": "You are currently viewing Day 1 plan. Become a subscriber to unlock all 30 days of scientific exercise guidance for better health!", "subscribe": "Subscribe", "dietPlanUpdated": "✅ Diet Plan Updated!", "dietPlanUpdatedDesc": "Added {count} days of plans, now total {total}/30 days.", "exercisePlanUpdated": "✅ Exercise Plan Updated!", "exercisePlanUpdatedDesc": "Added {count} days of plans, now total {total}/30 days.", "stillGenerating": "⏳ Still Generating...", "dietPlanGeneratingDesc": "Diet plan is being generated in the background, currently completed {completed}/30 days, please try again later.", "exercisePlanGeneratingDesc": "Exercise plan is being generated in the background, currently completed {completed}/30 days, please try again later.", "planIsLatest": "👍 Plan is Up to Date", "dietPlanLatestDesc": "Your diet plan is up to date, total {total}/30 days.", "exercisePlanLatestDesc": "Your exercise plan is up to date, total {total}/30 days.", "noPlanToRefresh": "ℹ️ No Plan to Refresh", "noDietPlanDesc": "We couldn't find your diet plan, please generate one first.", "noExercisePlanDesc": "We couldn't find your exercise plan, please generate one first.", "generateDietPlanFailed": "Failed to generate diet plan", "generateExercisePlanFailed": "Failed to generate exercise plan", "networkError": "Network error, please try again", "refreshFailed": "Refresh failed, please try again", "dietPlanGenerating": "📋 Diet Plan Generating", "dietPlanGeneratingInProgress": "Generating a more complete diet plan in the background, currently completed {completed}/30 days, please wait patiently! You can click the refresh button to check the latest progress.", "exercisePlanGenerating": "🏃‍♂️ Exercise Plan Generating", "exercisePlanGeneratingInProgress": "Generating a more complete exercise plan in the background, currently completed {completed}/30 days, please wait patiently! You can click the refresh button to check the latest progress.", "assessmentFailed": "Assessment failed", "networkErrorRetry": "Network error, please try again"}, "riskLevels": {"low": "Low Risk", "moderate": "Moderate Risk", "high": "High Risk", "very_high": "Very High Risk", "unknown": "Unknown"}, "foodCategories": {"staples": "<PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "vegetables": "Vegetables", "fruits": "Fruits", "others": "Others", "nutritionMatch": "🍽️ Nutrition Match", "viewRecipe": "Click to view detailed recipe"}, "foodRecipe": {"basicInfo": "Basic Information", "category": "Category:", "quantity": "Quantity:", "calories": "Calories:", "ingredients": "Ingredients", "steps": "Preparation Steps", "tips": "Key Tips", "nutritionValue": "Nutritional Value", "difficulty": {"easy": "Easy", "medium": "Medium", "hard": "Hard"}, "kcal": "kcal"}, "contact": {"title": "Contact Us", "description": "If you have any questions, please feel free to contact us"}}
export interface DishFeedback {
  dishName: string;
  feedbackType: string;
  customMessage?: string;
  userId?: string;
  submittedAt?: string;
}

export interface DishFeedbackFormData {
  dishName: string;
  feedbackType: string;
  customMessage: string;
}

export type DishFeedbackState = {
  errors?: {
    feedbackType?: string[];
    customMessage?: string[];
  };
  message?: string;
  success?: boolean;
};

// 反馈选项
export const FEEDBACK_OPTIONS = [
  { value: 'dislike_ingredient', label: '不喜欢该食材' },
  { value: 'complex_cooking', label: '烹饪方式太复杂' },
  { value: 'food_allergy', label: '对该食物过敏' },
  { value: 'portion_size', label: '分量不合适' }
] as const;

export type FeedbackOptionValue = typeof FEEDBACK_OPTIONS[number]['value']; 
import * as z from "zod"
import { FEEDBACK_OPTIONS } from "@/types/dish-feedback"

// 基础验证逻辑
const baseDishFeedbackSchema = {
  dishName: z.string()
    .min(1)
    .max(100)
    .trim(),
  feedbackType: z.enum(
    FEEDBACK_OPTIONS.map(option => option.value) as [string, ...string[]]
  ),
  customMessage: z.string()
    .min(5)
    .max(500)
    .trim(),
}

// 默认错误消息（用于服务端）
export const dishFeedbackSchema = z.object({
  dishName: baseDishFeedbackSchema.dishName
    .min(1, "Dish name is required")
    .max(100, "Dish name cannot exceed 100 characters"),
  feedbackType: baseDishFeedbackSchema.feedbackType,
  customMessage: baseDishFeedbackSchema.customMessage
    .min(5, "Custom message must be at least 5 characters")
    .max(500, "Custom message cannot exceed 500 characters"),
})

export type DishFeedbackFormValues = z.infer<typeof dishFeedbackSchema> 
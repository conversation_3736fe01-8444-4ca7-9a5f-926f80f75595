'use client'

import { useState, useEffect } from 'react'
import ProfileCompletionDialog from './profile-completion-dialog'

export default function ProfileCompletionDialogWrapper({ isLoggedIn, lang }: { isLoggedIn: boolean, lang: string }) {
  const [showDialog, setShowDialog] = useState(false)
  
  // 检查用户是否已有健康评估报告
  const checkHealthAssessmentReport = async () => {
    try {
      const response = await fetch('/api/check-health-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: 'current_user' }),
      })
      
      const data = await response.json()
      return data.success && data.hasReport
    } catch (error) {
      console.error('检查健康报告时出错:', error)
      return false
    }
  }
  
  // 检查是否显示弹窗
  useEffect(() => {
    const checkAndShowDialog = async () => {
      // 只有登录用户才需要检查是否显示弹窗
      if (!isLoggedIn) {
        return
      }
      
      // 检查用户是否已完成信息填写
      const hasCompletedProfile = localStorage.getItem('profile_completed') === 'true'
      
      // 检查用户是否已有健康评估报告
      const hasHealthReport = await checkHealthAssessmentReport()
      
      // 只有在登录用户既没有完成信息填写，也没有健康评估报告时才显示弹窗
      if (!hasCompletedProfile && !hasHealthReport) {
        // 延迟显示弹窗，以提供更好的用户体验
        const timer = setTimeout(() => {
          setShowDialog(true)
        }, 1500)
        
        return () => clearTimeout(timer)
      }
    }
    
    checkAndShowDialog()
  }, [isLoggedIn])
  
  // 处理关闭弹窗
  const handleCloseDialog = () => {
    setShowDialog(false)
  }
  
  // 处理点击"立即完善"按钮
  const handleComplete = () => {
    setShowDialog(false)
    
    // 滚动到健康评估区域
    const healthAssessmentSection = document.getElementById('health-assessment-section')
    if (healthAssessmentSection) {
      healthAssessmentSection.scrollIntoView({ behavior: 'smooth' })
    }
  }
  
  return (
    <ProfileCompletionDialog 
      isOpen={showDialog}
      onClose={handleCloseDialog}
      onComplete={handleComplete}
      lang={lang}
    />
  )
} 
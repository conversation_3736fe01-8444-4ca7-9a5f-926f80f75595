'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { 
  Loader2, 
  RefreshCw, 
  ChefHat, 
  Clock, 
  Flame, 
  Zap, 
  Wheat, 
  Droplets,
  Calendar,
  Info,
  BookOpen,
  Leaf,
  MessageSquare,
  CheckCircle2
} from 'lucide-react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@/types/diet-plan'
import { FEEDBACK_OPTIONS } from '@/types/dish-feedback'
import { submitDishFeedback } from '@/actions/dish-feedback'

interface DietPlanViewerProps {
  dietPlan: DietPlan
  onRegenerate: () => void
  isRegenerating: boolean
  className?: string
}

export function DietPlanViewer({ 
  dietPlan, 
  onRegenerate, 
  isRegenerating, 
  className = ''
}: DietPlanViewerProps) {
  const [selectedDay, setSelectedDay] = useState('day1')
  const [submittedFeedbacks, setSubmittedFeedbacks] = useState<Set<string>>(new Set())
  const [feedbackDialogs, setFeedbackDialogs] = useState<{ [key: string]: boolean }>({})
  const [feedbackForms, setFeedbackForms] = useState<{ [key: string]: { type: string; message: string } }>({})
  const [submittingFeedback, setSubmittingFeedback] = useState<{ [key: string]: boolean }>({})
  const { toast } = useToast()

  const dayNames = {
    day1: '周一',
    day2: '周二', 
    day3: '周三',
    day4: '周四',
    day5: '周五',
    day6: '周六',
    day7: '周日'
  }

  // 反馈处理函数
  const handleFeedbackSubmit = async (dishName: string) => {
    const feedbackData = feedbackForms[dishName]
    if (!feedbackData?.type || !feedbackData?.message) {
      toast({
        title: "提交失败",
        description: "请选择反馈类型并填写具体内容",
        variant: "destructive"
      })
      return
    }

    setSubmittingFeedback(prev => ({ ...prev, [dishName]: true }))
    
    try {
      const result = await submitDishFeedback({
        dishName,
        feedbackType: feedbackData.type,
        customMessage: feedbackData.message
      })

      if (result.success) {
        setSubmittedFeedbacks(prev => new Set([...prev, dishName]))
        setFeedbackDialogs(prev => ({ ...prev, [dishName]: false }))
        setFeedbackForms(prev => ({ ...prev, [dishName]: { type: '', message: '' } }))
        
        toast({
          title: "反馈提交成功",
          description: result.message,
        })
      } else {
        toast({
          title: "提交失败",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "提交失败",
        description: "发生错误，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setSubmittingFeedback(prev => ({ ...prev, [dishName]: false }))
    }
  }

  const updateFeedbackForm = (dishName: string, field: 'type' | 'message', value: string) => {
    setFeedbackForms(prev => ({
      ...prev,
      [dishName]: {
        ...prev[dishName],
        [field]: value
      }
    }))
  }

  const mealNames = {
    breakfast: '早餐',
    lunch: '午餐',
    dinner: '晚餐',
    snack: '加餐',
    herbalTea: '药膳茶'
  }

  const mealIcons = {
    breakfast: '🌅',
    lunch: '☀️',
    dinner: '🌙',
    snack: '🍎',
    herbalTea: '🍵'
  }

  // 定义餐食显示顺序：早餐→午餐→晚餐→加餐→药膳茶
  const mealOrder = ['breakfast', 'lunch', 'dinner', 'snack', 'herbalTea']

  const renderNutritionBadge = (label: string, value: number, unit: string, icon: React.ReactNode) => (
    <div className="flex items-center space-x-2 bg-gray-50 rounded-lg px-3 py-2">
      {icon}
      <div className="text-sm">
        <span className="text-gray-600">{label}</span>
        <span className="font-semibold ml-1">{value}{unit}</span>
      </div>
    </div>
  )

  const renderCookingStepsDialog = (dish: Dish) => (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50">
          <BookOpen className="w-4 h-4" />
          制作方法
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
            <ChefHat className="w-5 h-5 text-green-600" />
            {dish.name} - 制作方法
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            详细的制作步骤和食材配方
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* 食材配方 */}
          {dish.ingredients && (
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2 flex items-center gap-2">
                <Info className="w-4 h-4" />
                食材配方
              </h4>
              <p className="text-gray-700">{dish.ingredients}</p>
            </div>
          )}

          {/* 烹饪方式 */}
          {dish.cookingMethod && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">烹饪方式：</span>
              <Badge variant="outline" className="text-blue-600 border-blue-200">
                {dish.cookingMethod}
              </Badge>
            </div>
          )}

          {/* 制作步骤 */}
          {dish.cookingSteps && dish.cookingSteps.length > 0 && (
            <div>
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <Clock className="w-4 h-4 text-orange-500" />
                制作步骤
              </h4>
              <div className="space-y-3">
                {dish.cookingSteps.map((step, index) => (
                  <div key={index} className="flex gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white text-sm font-bold rounded-full flex items-center justify-center">
                      {index + 1}
                    </div>
                    <p className="text-gray-700 leading-relaxed">{step}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 中医属性 */}
          {dietPlan.plan_type === 'herbal' && dish.tcmProperties && (
            <div className="bg-yellow-50 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2 flex items-center gap-2">
                <Leaf className="w-4 h-4" />
                中医药性
              </h4>
              <p className="text-gray-700">{dish.tcmProperties}</p>
            </div>
          )}

          {/* 营养成分 */}
          {dish.nutrition && (
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">营养成分</h4>
              <div className="grid grid-cols-2 gap-2">
                {renderNutritionBadge('热量', dish.nutrition.calories, 'kcal', <Flame className="w-4 h-4 text-red-500" />)}
                {renderNutritionBadge('蛋白质', dish.nutrition.protein, 'g', <Zap className="w-4 h-4 text-yellow-500" />)}
                {renderNutritionBadge('碳水', dish.nutrition.carbs, 'g', <Wheat className="w-4 h-4 text-orange-500" />)}
                {renderNutritionBadge('脂肪', dish.nutrition.fat, 'g', <Droplets className="w-4 h-4 text-blue-500" />)}
              </div>
            </div>
          )}

          {/* 反馈部分 */}
          <div className="border-t pt-4">
            {submittedFeedbacks.has(dish.name) ? (
              <div className="flex items-center gap-2 p-3 bg-green-50 text-green-700 rounded-lg">
                <CheckCircle2 className="w-5 h-5" />
                <span className="font-medium">已提交反馈，感谢您的意见！</span>
              </div>
            ) : (
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <MessageSquare className="w-4 h-4 text-gray-600" />
                  <h4 className="font-semibold text-gray-800">对这道菜有意见？</h4>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-700 mb-2 block">
                      请选择反馈类型：
                    </Label>
                    <RadioGroup
                      value={feedbackForms[dish.name]?.type || ''}
                      onValueChange={(value) => updateFeedbackForm(dish.name, 'type', value)}
                      className="space-y-2"
                    >
                      {FEEDBACK_OPTIONS.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem value={option.value} id={`${dish.name}-${option.value}`} />
                          <Label 
                            htmlFor={`${dish.name}-${option.value}`}
                            className="text-sm text-gray-700 cursor-pointer"
                          >
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700 mb-2 block">
                      具体反馈内容：
                    </Label>
                    <Textarea
                      placeholder="请详细描述您的意见，如：煎鸡胸肉太麻烦，希望换成烤鸡胸肉"
                      value={feedbackForms[dish.name]?.message || ''}
                      onChange={(e) => updateFeedbackForm(dish.name, 'message', e.target.value)}
                      className="min-h-[80px] resize-none"
                      maxLength={500}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      {feedbackForms[dish.name]?.message?.length || 0}/500
                    </div>
                  </div>

                  <Button
                    onClick={() => handleFeedbackSubmit(dish.name)}
                    disabled={submittingFeedback[dish.name] || !feedbackForms[dish.name]?.type || !feedbackForms[dish.name]?.message}
                    className="w-full"
                  >
                    {submittingFeedback[dish.name] ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        提交中...
                      </>
                    ) : (
                      '提交反馈'
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )

  const renderDish = (dish: Dish, index: number) => (
    <div key={index} className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
      <div className="flex justify-between items-start mb-3">
        <h4 className="font-semibold text-gray-800 text-lg">{dish.name}</h4>
        <div className="flex items-center gap-2">
          {dietPlan.plan_type === 'herbal' && dish.tcmProperties && (
            <Badge variant="secondary" className="bg-green-100 text-green-700">
              {dish.tcmProperties}
            </Badge>
          )}
          {renderCookingStepsDialog(dish)}
        </div>
      </div>
      
      <div className="space-y-3">
        {dish.ingredients && (
          <div>
            <p className="text-sm text-gray-600 mb-1">食材配方：</p>
            <p className="text-gray-800">{dish.ingredients}</p>
          </div>
        )}
        
        {dish.cookingMethod && (
          <div>
            <p className="text-sm text-gray-600 mb-1">烹饪方式：</p>
            <Badge variant="outline" className="text-blue-600 border-blue-200">
              {dish.cookingMethod}
            </Badge>
          </div>
        )}
        
        {dish.nutrition && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {renderNutritionBadge('热量', dish.nutrition.calories, 'kcal', <Flame className="w-4 h-4 text-red-500" />)}
            {renderNutritionBadge('蛋白质', dish.nutrition.protein, 'g', <Zap className="w-4 h-4 text-yellow-500" />)}
            {renderNutritionBadge('碳水', dish.nutrition.carbs, 'g', <Wheat className="w-4 h-4 text-orange-500" />)}
            {renderNutritionBadge('脂肪', dish.nutrition.fat, 'g', <Droplets className="w-4 h-4 text-blue-500" />)}
          </div>
        )}
      </div>
    </div>
  )

  const renderMeal = (meal: Meal, mealType: string) => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{mealIcons[mealType as keyof typeof mealIcons]}</span>
          <h3 className="text-xl font-bold text-gray-800">
            {mealNames[mealType as keyof typeof mealNames]}
          </h3>
        </div>
        <div className="flex space-x-2">
          {meal.totalNutrition && (
            renderNutritionBadge('总热量', meal.totalNutrition.calories, 'kcal', <Flame className="w-4 h-4 text-red-500" />)
          )}
        </div>
      </div>
      
      <div className="grid gap-4">
        {meal.dishes && meal.dishes.map ? meal.dishes.map((dish, index) => renderDish(dish, index)) : (
          <div className="text-amber-600 font-medium">菜品详情生成中...</div>
        )}
      </div>
      
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold text-gray-700 mb-2">本餐营养汇总</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {meal.totalNutrition ? (
            <>
              {renderNutritionBadge('热量', meal.totalNutrition.calories, 'kcal', <Flame className="w-4 h-4 text-red-500" />)}
              {renderNutritionBadge('蛋白质', meal.totalNutrition.protein, 'g', <Zap className="w-4 h-4 text-yellow-500" />)}
              {renderNutritionBadge('碳水', meal.totalNutrition.carbs, 'g', <Wheat className="w-4 h-4 text-orange-500" />)}
              {renderNutritionBadge('脂肪', meal.totalNutrition.fat, 'g', <Droplets className="w-4 h-4 text-blue-500" />)}
            </>
          ) : (
            <div className="col-span-4 text-amber-600 font-medium">营养数据生成中...</div>
          )}
        </div>
      </div>
    </div>
  )

  const renderDayPlan = (dayPlan: DayPlan | null | undefined) => {
    if (!dayPlan) {
      return <div className="text-red-500 font-bold p-4">未找到该天的饮食计划数据</div>;
    }
    return (
      <div className="space-y-8">
        {/* 每日总览 */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center">
            <Calendar className="w-6 h-6 mr-2 text-green-600" />
            {dayPlan.date} 饮食计划
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {dayPlan.dailyTotal ? (
              <>
                {renderNutritionBadge('日总热量', dayPlan.dailyTotal.calories, 'kcal', <Flame className="w-5 h-5 text-red-500" />)}
                {renderNutritionBadge('日总蛋白质', dayPlan.dailyTotal.protein, 'g', <Zap className="w-5 h-5 text-yellow-500" />)}
                {renderNutritionBadge('日总碳水', dayPlan.dailyTotal.carbs, 'g', <Wheat className="w-5 h-5 text-orange-500" />)}
                {renderNutritionBadge('日总脂肪', dayPlan.dailyTotal.fat, 'g', <Droplets className="w-5 h-5 text-blue-500" />)}
              </>
            ) : (
              <div className="col-span-4 text-amber-600 font-medium">
                营养数据生成中，请稍后查看完整数据...
              </div>
            )}
          </div>
        </div>
        {/* 餐食详情 */}
        {mealOrder.map((mealType) => {
          const meal = dayPlan.meals[mealType as keyof typeof dayPlan.meals]
          if (!meal) return null
          return (
            <div key={mealType}>
              {renderMeal(meal, mealType)}
              <Separator className="my-6" />
            </div>
          )
        })}
      </div>
    )
  }

  return (
    <div className={`max-w-6xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0">
        <CardHeader className="text-center space-y-4 bg-gradient-to-r from-green-50 to-blue-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <ChefHat className="w-8 h-8 text-green-600" />
              <div className="text-left">
                <CardTitle className="text-2xl font-bold text-gray-800">
                  您的专属饮食计划
                </CardTitle>
                <CardDescription className="text-gray-600">
                  {dietPlan.plan_type === 'herbal' ? '中医膳食调理方案' : '科学营养配餐方案'}
                </CardDescription>
              </div>
            </div>
            <Button 
              onClick={onRegenerate}
              disabled={isRegenerating}
              variant="outline"
              className="flex items-center space-x-2"
            >
              {isRegenerating ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>重新生成中...</span>
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4" />
                  <span>重新生成</span>
                </>
              )}
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* 计划类型和特别说明 */}
          <div className="mb-6 space-y-4">
            <div className="flex items-center space-x-2">
              <Badge 
                variant={dietPlan.plan_type === 'herbal' ? 'default' : 'secondary'} 
                className={dietPlan.plan_type === 'herbal' ? 'bg-green-600' : 'bg-blue-600'}
              >
                {dietPlan.plan_type === 'herbal' ? '中医膳食计划' : '营养健康计划'}
              </Badge>
              <Badge variant="outline">一周7天方案</Badge>
            </div>
            
            {dietPlan.special_notes && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-2">
                  <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-blue-800 mb-1">特别说明</h4>
                    <p className="text-blue-700 text-sm">{dietPlan.special_notes}</p>
                  </div>
                </div>
              </div>
            )}

            {dietPlan.tcm_principles && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start space-x-2">
                  <ChefHat className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-green-800 mb-1">中医膳食原理</h4>
                    <p className="text-green-700 text-sm">{dietPlan.tcm_principles}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 日程选择器和详情 */}
          <Tabs value={selectedDay} onValueChange={setSelectedDay}>
            <TabsList className="grid w-full grid-cols-7 mb-6">
              {Object.entries(dayNames).map(([dayKey, dayName]) => (
                <TabsTrigger key={dayKey} value={dayKey} className="text-sm">
                  {dayName}
                </TabsTrigger>
              ))}
            </TabsList>

            {Object.entries(dietPlan.weekly_plan).map(([dayKey, dayPlan]) => (
              <TabsContent key={dayKey} value={dayKey}>
                <ScrollArea className="h-[800px] pr-4">
                  {renderDayPlan(dayPlan)}
                </ScrollArea>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
} 
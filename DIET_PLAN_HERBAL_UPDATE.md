# 饮食计划功能升级 - 增加中药调理支持 🌿

## 升级概述

已成功升级饮食计划功能，现在可以融入中药调理方案，生成更完整的中医药膳计划。

## 📋 数据源扩展

### 原有数据源
- `basic_analysis`: 基本健康分析
- `diet_recommendations`: 中医饮食调理建议

### ✨ 新增数据源
- `herbal_recommendations`: 中药调理方案

## 🎯 计划类型优化

### 普通饮食计划 (normal)
**触发条件**: 用户不愿意接受药膳计划
**参考数据**:
- ✅ `basic_analysis` (基本分析)
- ✅ `diet_recommendations` (饮食建议)
- ❌ `herbal_recommendations` (不使用)

**生成特点**:
- 科学营养配比
- 现代饮食习惯
- 健康减脂导向

### 中医药膳计划 (herbal)
**触发条件**: 用户愿意接受药膳计划 + 有完整的中医建议数据
**参考数据**:
- ✅ `basic_analysis` (基本分析)
- ✅ `diet_recommendations` (饮食建议)
- ✅ `herbal_recommendations` (中药方案)

**生成特点**:
- 药食同源理念
- 体质调理配方
- 传统食疗智慧
- **新增**: 药材融入日常饮食

## 🔧 技术实现更新

### 1. 判断逻辑优化
```typescript
// 升级前
const shouldUseHerbalPlan = includeHerbalPlan && healthReport.diet_recommendations

// 升级后  
const shouldUseHerbalPlan = includeHerbalPlan && 
                           healthReport.diet_recommendations && 
                           healthReport.herbal_recommendations
```

### 2. 数据类型扩展
```typescript
export interface DietPlan {
  // ... 原有字段
  diet_recommendations?: string
  herbal_recommendations?: string  // ✨ 新增字段
}
```

### 3. 数据库结构升级
```sql
-- 新增字段
ALTER TABLE nf_diet_plans ADD COLUMN "herbal_recommendations" text;

-- 字段注释
COMMENT ON COLUMN "public"."nf_diet_plans"."herbal_recommendations" 
IS '来源健康评估的中药调理建议（药膳计划参考）';
```

### 4. AI提示词增强
```
## 中药调理方案：
{herbalRecommendations}

特别说明：在药膳计划中，要根据中药调理方案融入相应的药食同源食材，体现中医药膳特色
```

## 📊 用户体验改进

### 🎯 智能判断流程
```
用户选择药膳计划 → 检查数据完整性 →
├── 有完整数据 → 生成中医药膳计划 (使用3个数据源)
└── 数据不完整 → 生成普通饮食计划 (使用2个数据源)
```

### 🌿 药膳计划特色
1. **药材入菜**: 将中药调理方案中的药材直接作为食材融入菜肴
2. **药膳菜品**: 当归炖鸡汤、黄芪排骨汤、山药薏米粥、枸杞炒菠菜、茯苓饼等
3. **体质调理**: 根据体质特点和药材药性进行精准搭配
4. **传统智慧**: 体现真正的中医"药食同源"理念
5. **严禁茶饮**: 不生成单独的药膳茶，所有药材都融入具体菜品

## 🔄 数据流程对比

### 升级前
```
健康评估数据 → basic_analysis + diet_recommendations → 饮食计划
```

### 升级后
```
健康评估数据 → basic_analysis + diet_recommendations + herbal_recommendations → 完整药膳计划
```

## 📁 文件更新清单

### 核心逻辑
- ✅ `src/app/api/generate-diet-plan/route.ts` - API逻辑升级
- ✅ `src/actions/diet-constants.ts` - AI提示词增强

### 数据结构
- ✅ `src/types/diet-plan.ts` - 类型定义扩展
- ✅ `src/lib/db/diet-plan.ts` - 数据库操作更新

### 数据库
- ✅ `src/sql/diet_plans.sql` - 表结构升级
- ✅ `scripts/init-diet-plan-db.sql` - 初始化脚本更新

## 🚀 部署要求

### 1. 数据库升级
```sql
-- 为现有表添加新字段
ALTER TABLE nf_diet_plans ADD COLUMN IF NOT EXISTS "herbal_recommendations" text;

-- 或者重新执行初始化脚本
-- scripts/init-diet-plan-db.sql
```

### 2. 重启服务
```bash
npm run dev
```

## 💡 使用效果

### 普通饮食计划示例
```json
{
  "planType": "normal",
  "weeklyPlan": {
    "day1": {
      "meals": {
        "breakfast": {
          "dishes": [
            {
              "name": "全麦面包配煎蛋",
              "ingredients": "全麦面包2片，鸡蛋1个"
            }
          ]
        }
      }
    }
  }
}
```

### 药膳计划示例
```json
{
  "planType": "herbal",
  "weeklyPlan": {
    "day1": {
      "meals": {
        "breakfast": {
          "dishes": [
            {
              "name": "山药薏米粥",
              "ingredients": "山药50g，薏米30g，大米30g，红枣3颗",
              "cookingMethod": "煮制",
              "tcmProperties": "健脾祛湿，补中益气",
              "nutrition": {"calories": 280, "protein": 8, "carbs": 58, "fat": 2}
            }
          ]
        },
        "dinner": {
          "dishes": [
            {
              "name": "当归炖鸡汤",
              "ingredients": "土鸡500g，当归10g，红枣5颗，枸杞15g，生姜3片",
              "cookingMethod": "炖制",
              "tcmProperties": "补血活血，滋阴润燥",
              "nutrition": {"calories": 420, "protein": 35, "carbs": 8, "fat": 28}
            }
          ]
        },
        "snack": {
          "dishes": [
            {
              "name": "黄芪蜜枣",
              "ingredients": "黄芪10g，蜜枣3颗，核桃仁20g",
              "cookingMethod": "蒸制",
              "tcmProperties": "补气健脾，润肺止咳",
              "nutrition": {"calories": 180, "protein": 5, "carbs": 25, "fat": 8}
            }
          ]
        }
      }
    }
  }
}
```

## 🎯 核心优势

✅ **数据更完整**: 三个维度的健康数据融合  
✅ **计划更精准**: 基于完整中医理论的个性化方案  
✅ **功能更强大**: 药食同源的真正实现  
✅ **体验更智能**: 自动判断生成最适合的计划类型  

现在用户可以获得真正融合中医药理论的个性化药膳饮食计划！🌿✨ 